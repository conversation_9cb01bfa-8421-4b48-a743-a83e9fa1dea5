import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const workShiftApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/workShift",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/workShift",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/workShift/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/workShift/${id}`,
      method: "delete",
    }),
};
