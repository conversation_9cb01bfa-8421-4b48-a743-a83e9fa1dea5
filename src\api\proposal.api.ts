import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const proposalApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/proposal",
      params,
    }),
  groupByContractCode: (params?: any): AxiosPromise<any> =>
    request({
      url: "v1/admin/proposal/groupBy/contractCode",
      params,
    }),

  findOne: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}`,
      params,
    }),
  checkUpdate: (id: number, params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/checkUpdate`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/proposal",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}`,
      method: "patch",
      data,
    }),
  updateSimple: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/simple`,
      method: "patch",
      data,
    }),

  updateFollowStaffs: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/followStaff`,
      method: "patch",
      data,
    }),

  updateDetails: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/details`,
      method: "patch",
      data,
    }),

  updateInvoiceFile: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/invoiceFile`,
      method: "patch",
      data,
    }),

  deleteInvoiceFile: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/invoiceFile`,
      method: "delete",
      data,
    }),
  summary: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/summary/status`,
      params,
    }),

  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/approve`,
      method: "patch",
      data,
    }),

  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/reject`,
      method: "delete",
      data,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/complete`,
      method: "patch",
    }),
  paid: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}/paid`,
      method: "patch",
    }),

  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposal/${id}`,
      method: "delete",
    }),
};
