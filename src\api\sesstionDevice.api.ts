import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const sessionDeviceApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/sessionDevice",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/sessionDevice",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/sessionDevice/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/sessionDevice/${id}`,
      method: "delete",
    }),
};
