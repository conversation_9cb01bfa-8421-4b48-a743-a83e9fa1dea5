import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const productSampleCheckApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productSampleCheck",
      params,
    }),
  detail: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productSampleCheck/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productSampleCheck",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productSampleCheck/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productSampleCheck/${id}`,
      method: "delete",
    }),
};
