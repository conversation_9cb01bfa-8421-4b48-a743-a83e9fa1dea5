import { ContractContent } from "@/types/contractContent";
import { ModalStatus } from "@/types/modal";
import { Collapse, message, Modal, Tabs } from "antd";
import React, { useImperativeHandle, useState, forwardRef, useMemo } from "react";
import { ContentViTabState } from "./ContentViTabState";
import { ContentEnTabState } from "./ContentEnTabState";

export interface ContractPaymentModalRef {
  handleCreate: () => void;
  handleUpdate: (contractContents: Partial<ContractContent>[]) => void;
}

interface ContractPaymentModalProps {
  onClose: () => void;
  onSubmitOk: (data: Partial<ContractContent>[]) => void;
  readOnly?: boolean;
}

export const ModalContractPayment = forwardRef<
  ContractPaymentModalRef,
  ContractPaymentModalProps
>(({ onClose, onSubmitOk, readOnly = false }, ref) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [status, setStatus] = useState<ModalStatus>("create");
  const [contractContents, setContractContents] = useState<
    Partial<ContractContent>[]
  >([]);
  const [draftContents, setDraftContents] = useState<
    Partial<ContractContent>[]
  >([]);

  const content = useMemo(() => {
    const index = contractContents.findIndex((item) => item.type === "PAYMENT");
    return {
      index: index,
      content: contractContents[index],
    };
  }, [contractContents])

  useImperativeHandle(ref, () => ({
    handleCreate() {
      setContractContents([]);
      setDraftContents([]);

      setVisible(true);
      setStatus("create");
    },
    handleUpdate(contents: Partial<ContractContent>[]) {
      setContractContents(contents);
      setDraftContents([...contents]);

      setVisible(true);
      setStatus("update");
    },
  }));

  const handleChange = (index: number, updated: Partial<ContractContent>, typeEn?: boolean) => {
    const newData = [...draftContents];
    if (typeEn) {
      newData[index].contentEn = updated.contentEn;
      newData[index].titleVi = updated.titleVi;
    }
    else {
      newData[index].contentVi = updated.contentVi;
      newData[index].titleVi = updated.titleVi;
    }

    setDraftContents(newData);
  };

  const createData = async () => {
    setLoading(true);
    try {
      // await contractContentApi.createMany(contractContents); // Nếu có API createMany
      message.success("Create ContractContent successfully!");
      setVisible(false);
    } finally {
      setLoading(false);
    }
  };

  const updateData = async () => {
    setLoading(true);
    try {
      // await Promise.all(contractContents.map(c => contractContentApi.update(c.id!, c)));
      setContractContents(draftContents);
      onSubmitOk(draftContents);
      setVisible(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      visible={visible}
      title={
        status === "create" ? "Create ContractContent" : "Nội dung hợp đồng"
      }
      onCancel={() => {
        setVisible(false);
        onClose();
      }}
      cancelText="Đóng"
      okButtonProps={{
        className: readOnly ? "hidden" : "",
      }}
      width={800}
      okText="Lưu"
      confirmLoading={loading}
      onOk={() => {
        status === "create" ? createData() : updateData();
      }}
    >
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab="Tiếng Việt" key="1">
          <ContentViTabState
            readOnly={readOnly}
            contractContent={content?.content}
            onChange={(updated) => handleChange(content?.index, updated)}
          />
        </Tabs.TabPane>
        <Tabs.TabPane tab="Tiếng Anh" key="2">
          <ContentEnTabState
            readOnly={readOnly}
            contractContent={content?.content}
            onChange={(updated) => handleChange(content?.index, updated, true)}
          />
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
});
