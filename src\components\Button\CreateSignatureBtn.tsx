import { staffUploadApi, uploadImage } from "@/api/image.api";
import { base64ToFile } from "@/utils/image";
import { $url } from "@/utils/url";
import { Button, Row, Tabs, message } from "antd";
import { useRef, useState } from "react";
import ReactSignatureCanvas from "react-signature-canvas";
import AppModal, { AppModalAction } from "../Modal/AppModal";
import { SingleImageUpload } from "../Uploads/SingleImageUpload";

type Props = {
  onSubmit: (url: string) => Promise<void>;
};

enum ETabs {
  hand = "HAND",
  upload = "UPLOAD",
}

const CreateSignatureBtn = ({ onSubmit }: Props) => {
  const canvasRef = useRef<ReactSignatureCanvas>(null);
  const modal = useRef<AppModalAction>();
  const [currTab, setCurrTab] = useState<ETabs>(ETabs.hand);
  const [imageUrl, setImageUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const handleOnOpenCreateModal = () => {
    modal.current?.open({});
  };

  const handleClearSignature = () => {
    if (currTab == ETabs.hand) {
      canvasRef.current?.clear();
    } else {
      setImageUrl("");
    }
  };

  return (
    <>
      <Button onClick={handleOnOpenCreateModal} type="primary">
        Cập nhật chữ ký số
      </Button>
      <AppModal
        confirmLoading={loading}
        onOk={async () => {
          try {
            setLoading(true);
            if (currTab == ETabs.hand) {
              const file = await base64ToFile(
                canvasRef.current?.toDataURL() as string
              );
              let url = "";
              if (!canvasRef.current?.isEmpty()) {
                url = await uploadImage(file, staffUploadApi);
              }
              await onSubmit(url);
            } else {
              await onSubmit(imageUrl);
            }
            setImageUrl("");
            modal.current?.close();
          } catch (error) {
          } finally {
            setLoading(false);
          }
        }}
        title="Cập nhật chữ ký số"
        destroyOnClose
        ref={modal}
      >
        {() => (
          <Tabs
            activeKey={currTab}
            onChange={(tab) => {
              setCurrTab(tab as ETabs);
            }}
            centered
          >
            <Tabs.TabPane key={ETabs.hand} tab="Ký tay">
              <ClearSignatureBtn onClick={handleClearSignature} />
              <div className="border border-solid">
                <ReactSignatureCanvas
                  ref={canvasRef}
                  penColor="black"
                  canvasProps={{
                    width: 500,
                    height: 200,
                    className: "sigCanvas",
                  }}
                />
              </div>
            </Tabs.TabPane>
            <Tabs.TabPane key={ETabs.upload} tab="Tải hình ảnh">
              <SingleImageUpload
                onUploadOk={(url) => {
                  setImageUrl($url(url));
                }}
                imageUrl={imageUrl}
              ></SingleImageUpload>
            </Tabs.TabPane>
          </Tabs>
        )}
      </AppModal>
    </>
  );
};

const ClearSignatureBtn = ({ onClick }: { onClick: () => void }) => {
  return (
    <Row justify="end" onClick={onClick} className="mb-2">
      <Button danger ghost>
        Reset chữ ký
      </Button>
    </Row>
  );
};
export default CreateSignatureBtn;
