import { PurchaseOrder } from "@/types/purchaseOrder";
import { PrintPaymentInfo } from "@/views/PurchasePage/PrintModal/PrintPurchaseModal";
import { PrintPurchaseOrderType } from "@/views/PurchasePage/components/PrintPurchaseBtn";
import { POInlandBodyPDF } from "./POInland";
import { PaymentBeforeDayBodyPDF } from "./PaymentBeforeDay";
import { ProposalCreditPDF } from "./ProposalCredit";
import { ProposalForeignPDF } from "./ProposalForeign";
import {
  ReportOrderFooter,
  ReportOrderHeader,
  ReportProposalHeader,
} from "./ReportOrderComponent";
import { RequiredPaymentBodyPDF } from "./RequirePayment";
import { Contract } from "@/types/contract";
import { ContractBodyPDF } from "./ContractBodyPDF";
import { ContractFooter, ContractHeader } from "./ContractComponent";
import { ContractContent } from "@/types/contractContent";

/**
 * Trả về mẫu in dựa theo type
 */
class ContractPDF {
  static getPrintComponent({
    data,
    dataInput,
    printType,
    unitType,
    typeContract,
    contents
  }: {
    printType: "vi" | "en" | "all";
    data: Contract;
    dataInput?: PrintPaymentInfo;
    unitType: "box" | "unit";
    typeContract?: string;
    contents?: ContractContent[]
  }): React.ReactElement {
    return (
      <div>
        <ContractHeader data={data} printType={printType} />
        <ContractBodyPDF
          data={data}
          printType={printType}
          unitType={unitType}
          typeContract={typeContract}
          contents={contents}
        />
        <ContractFooter data={data} printType={printType} />
      </div>
    );
  }
}

export default ContractPDF;
