import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const customerApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customer",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customer",
      data,
      method: "post",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customer/import",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customer/${id}`,
      method: "delete",
    }),
};
