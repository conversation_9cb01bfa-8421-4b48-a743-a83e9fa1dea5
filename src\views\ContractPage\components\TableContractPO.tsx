import { productApi } from "@/api/product.api";
import { InputNumberVN } from "@/components/Input/InputNumberVN";
import { ProductSelectorRef } from "@/components/ProductSelector/ProductSelector";
import { useProduct } from "@/hooks/useProduct";
import { useUnit } from "@/hooks/useUnit";
import { ModalStatus } from "@/types/modal";
import { Product } from "@/types/product";
import { PurchaseOrderDetail } from "@/types/purchaseOrder";
import { formatVND, formatVND2FDs } from "@/utils";
import { ProductModalRef } from "@/views/Product/components/ProductModal";
import { CopyOutlined, DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Button,
  Input,
  Modal,
  Popconfirm,
  Select,
  Space,
  Table,
  Tooltip,
  message,
} from "antd";
import Column from "antd/es/table/Column";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { quoteApi } from "@/api/quote.api";
import quoteExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/quoteExportDemo";
import { Contract, ContractPO } from "@/types/contract";
import { useTermOfPayment } from "@/hooks/useTermOfPayment";

export interface TableContractProductRef {
  getValue: () => {
    contractProducts: ContractPO[];
  };
  setValue: (contract: Partial<Contract>) => void;
}

export const validateContractProducts = (
  products: Partial<ContractPO & { etdDetail: string }>[]
) => {
  for (const [i, item] of products.entries()) {
    if (!item.po) return `Dòng ${i + 1}: Chưa nhập mã PO`;
    if (!item.totalFCL) return `Dòng ${i + 1}: Chưa nhập số lượng FCL`;
    if (!item.contType) return `Dòng ${i + 1}: Chưa chọn loại cont`;
    if (!item.etdDetail) return `Dòng ${i + 1}: Chưa nhập thời gian giao hàng`;
    // if (!item.amount) return `Dòng ${i + 1}: Chưa nhập tổng giá trị`;
    // if (!item.quantity) return `Dòng ${i + 1}: Chưa nhập tổng số lượng`;
  }
  return null;
};

export const TableContractPO = observer(
  React.forwardRef(
    (
      {
        readOnly = false,
        contractProducts,
        onAddRow,
        onDeleteContractProduct,
        onChangeContractProduct,
      }: {
        readOnly?: boolean;
        contractProducts?: Partial<ContractPO & { date: string }>[];
        onChangeContractProduct?: (
          index: number,
          product: Partial<ContractPO & { date: string }>
        ) => void;
        onDeleteContractProduct?: (index: number) => void;

        onAddRow?: (duplicate?: number) => void;
      },
      ref
    ) => {
      const [loadingTable, setLoadingTable] = useState(false);

      const optionsCont = [
        {
          value: "20FT",
          label: "20FT",
        },
        {
          value: "40FT",
          label: "40FT",
        },
        {
          value: "0",
          label: "Combined shipment",
        },
      ];

      useImperativeHandle(
        ref,
        () => {
          return {};
        },
        [contractProducts]
      );

      const addRow = (customData?: Partial<ContractPO>[]) => {
        onAddRow?.();
      };

      return (
        <>
          <Space
            style={{
              marginTop: "10px",
              marginBottom: "10px",
              width: "100%",
              justifyContent: readOnly ? "end" : "space-between",
              alignItems: "start",
            }}
          ></Space>

          <div>
            <Table
              className="table-striped-rows"
              tableLayout="fixed"
              bordered
              loading={loadingTable}
              dataSource={contractProducts}
              size="small"
              pagination={false}
              locale={{
                emptyText: readOnly
                  ? "Danh sách trống"
                  : `Ấn vào dấu "+" để thêm dòng`,
              }}
              scroll={{ y: 400 }}
            >
              {!readOnly && (
                <Column
                  align="center"
                  width={80}
                  title={
                    <Space>
                      <Tooltip placement="top" title="Thêm dòng">
                        <Button
                          className="bg-black"
                          type="primary"
                          ghost
                          icon={<PlusOutlined />}
                          onClick={() => {
                            addRow();
                          }}
                        ></Button>
                      </Tooltip>
                    </Space>
                  }
                  key="name"
                  render={(text, record: ContractPO, index: number) => (
                    <div className="flex gap-2 justify-center">
                      <Popconfirm
                        title="Dòng này sẽ bị xóa. Tiếp tục?"
                        onConfirm={() => {
                          onDeleteContractProduct?.(index);
                        }}
                      >
                        <DeleteOutlined className="text-red-500 text-lg" />
                      </Popconfirm>
                      <CopyOutlined
                        onClick={() => {
                          const newRecord = { ...record, id: undefined };
                          onAddRow?.(index);
                          // contractDetails.push(newRecord);
                          // handleChangeContractDetails?.([...contractDetails]);
                        }}
                        className="text-blue-500 text-lg"
                      />
                    </div>
                  )}
                />
              )}

              <Column
                width={100}
                title={
                  <Space className="w-full justify-between">
                    <div>
                      Mã PO
                      <span className="text-red-500">*</span>
                    </div>{" "}
                  </Space>
                }
                key="age"
                render={(text, record: ContractPO, index) =>
                  readOnly ? (
                    record.po
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.po}
                      onChange={(e) => {
                        record.po = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Số lượng FCL <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={(text, record: ContractPO, index) =>
                  readOnly ? (
                    formatVND(record.totalFCL)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.totalFCL}
                      onChange={(value) => {
                        record.totalFCL = value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />
              <Column
                align="right"
                width={75}
                title={
                  <>
                    Loại cont <span className="text-red-500">*</span>
                  </>
                }
                key="contType"
                render={(text, record: ContractPO, index) =>
                  readOnly ? (
                    record.contType
                  ) : (
                    <Select
                      className="w-full"
                      options={optionsCont}
                      value={record.contType}
                      onChange={(value) => {
                        record.contType = value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />
              <Column
                width={150}
                title={
                  <>
                    Thời gian giao hàng
                    <span className="text-red-500">*</span>
                  </>
                }
                key="etdDetail"
                render={(
                  text,
                  record: ContractPO & { etdDetail: string },
                  index
                ) =>
                  readOnly ? (
                    record.etdDetail
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.etdDetail}
                      onChange={(e) => {
                        record.etdDetail = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />

              <Column
                width={150}
                title={
                  <>
                    Thời gian giao hàng (EN)
                    <span className="text-red-500">*</span>
                  </>
                }
                key="etdDetailEn"
                render={(
                  text,
                  record: ContractPO & { etdDetailEn: string },
                  index
                ) =>
                  readOnly ? (
                    record.etdDetailEn
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.etdDetailEn}
                      onChange={(e) => {
                        record.etdDetailEn = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Tổng giá trị <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={
                  (text, record: ContractPO, index) => {
                    return formatVND2FDs(record.totalPrice);
                  }
                  // readOnly ? (
                }
              />
              <Column
                align="right"
                width={100}
                title={
                  <>
                    Tổng số lượng <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={(text, record: ContractPO, index) =>
                  // readOnly ? (
                  formatVND(record.quantity)
                }
              />
            </Table>
          </div>
        </>
      );
    }
  )
);
