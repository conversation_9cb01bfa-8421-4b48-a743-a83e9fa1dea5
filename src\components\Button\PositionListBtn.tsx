import { positionApi } from "@/api/position.api";
import { usePosition } from "@/hooks/usePosition";
import { Position } from "@/types/position";
import { PositionDnd } from "@/views/PositionPage/components/PositionDnd";
import {
  PositionModal,
  PositionModalRef,
} from "@/views/PositionPage/components/PositionModal";
import { PlusOutlined } from "@ant-design/icons";
import { Button, Modal, Space, message } from "antd";
import { useEffect, useRef, useState } from "react";
import { IoListOutline } from "react-icons/io5";

type Props = {
  onSuccess: () => void;
};

const PositionListBtn = ({ onSuccess }: Props) => {
  const [visible, setVisible] = useState(false);
  const positionModalRef = useRef<PositionModalRef>();
  const { fetchData, positions, query, setPositions } = usePosition({
    initQuery: { page: 1, limit: 50 },
  });

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible]);

  const onChangePosition = async (newData: Position[]) => {
    const promiseAll = newData.map((item, index) =>
      positionApi.update(item.id, { position: { no: index + 1 } })
    );
    await Promise.all(promiseAll);
    message.success("Đã lưu thay đổi");
  };

  return (
    <>
      <Button
        title="Danh sách chức vụ"
        onClick={() => setVisible(true)}
        type="primary"
        size="small"
      >
        <div className="translate-y-[2px]">
          <IoListOutline />
        </div>
      </Button>
      <Modal maskClosable={false}
        centered
        footer={false}
        title="Danh sách chức vụ"
        open={visible}
        onCancel={() => setVisible(false)}
      >
        <Space className="w-full my-2">
          <Button
            size="small"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => positionModalRef.current?.handleOpen("create")}
          >
            Thêm mới
          </Button>
        </Space>

        {/* <Table
          scroll={{ y: 1000, x: "auto" }}
          className="table-striped-rows"
          pagination={false}
          dataSource={positions}
        >
          <Column title="Chức vụ" dataIndex="name" key="name" />
          <Column
            title="Thứ tự hiển thị"
            width={100}
            dataIndex="no"
            key="name"
          />

          <Column
            width={50}
            key="action"
            render={(text, record: Position) => (
              <Space>
                <Button
                  ghost
                  size="small"
                  type="primary"
                  icon={<EditFilled />}
                  onClick={() =>
                    positionModalRef.current?.handleOpen("update", record)
                  }
                ></Button>
                <Popconfirm
                  title={`Xóa chức vụ này?`}
                  onConfirm={async () => {
                    await positionApi.delete(record.id);
                    message.success("Đã xóa chức vụ");
                    fetchData();
                  }}
                  okText={"Xóa"}
                  cancelText={"Đóng"}
                >
                  <Button size="small" danger icon={<DeleteFilled />}></Button>
                </Popconfirm>
              </Space>
            )}
          />
        </Table> */}
        <PositionDnd
          positions={positions}
          onChangePosition={(data) => onChangePosition(data)}
          onUpdate={(record) =>
            positionModalRef.current?.handleOpen("update", record)
          }
          onDelete={async (record) => {
            await positionApi.delete(record.id);
            message.success("Đã xóa chức vụ");
            fetchData();
          }}
        />
      </Modal>
      <PositionModal onSubmitOk={fetchData} ref={positionModalRef} />
    </>
  );
};

export default PositionListBtn;
