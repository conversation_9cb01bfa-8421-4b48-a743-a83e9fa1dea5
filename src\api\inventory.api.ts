import { userStore } from "@/store/userStore";
import { checkRole } from "@/utils/auth";
import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const inventoryApi = {
  findAll: (params?: any): AxiosPromise<any> => {
    const isGetAll =
      checkRole("view-all-inventory", userStore.info.role?.permissions) ||
      userStore.info.role?.isAccounting ||
      userStore.info.role?.isAdmin;
    return request({
      url: "/v1/admin/inventory",
      params: { ...params, isGetAll },
    });
  },
  getOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}`,
    }),
  getDetails: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/details`,
      params,
    }),
  findOne: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/inventory/details",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/inventory",
      data,
      method: "post",
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/approve`,
      method: "patch",
      data,
    }),
  report: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/report`,
      method: "get",
      params,
    }),

  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/reject`,
      method: "delete",
      data,
    }),

  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/complete`,
      method: "patch",
    }),

  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}`,
      method: "patch",
      data,
    }),
  info: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/info`,
      method: "patch",
      data,
    }),

  updateDeadline: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/deadline`,
      method: "patch",
      data,
    }),

  updateFollowStaffs: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/followStaff`,
      method: "patch",
      data,
    }),
  updateNoteDetails: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/note`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}`,
      method: "delete",
    }),

  summary: (params: any): AxiosPromise<any> => {
    const isGetAll =
      checkRole("view-all-inventory", userStore.info.role?.permissions) ||
      userStore.info.role?.isAccounting ||
      userStore.info.role?.isAdmin;

    return request({
      url: `/v1/admin/inventory/summary/status`,
      params: { ...params, isGetAll },
    });
  },
  summaryType: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/summary/type`,
      params,
    }),
  assignSupper: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/supper`,
      data,
      method: "patch",
    }),
  supperSign: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/sign/supper`,
      data,
      method: "patch",
    }),
  managerSign: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventory/${id}/sign/manger`,
      data,
      method: "patch",
    }),
};
