import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const notificationApi = {
  getOwn: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification/own",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}`,
    }),
  readAll: (): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/read-all`,
      method: "post",
    }),
  getUnRead: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/notification/un-read",
    }),
  read: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/notification/${id}/read`,
      method: "post",
    }),
};
