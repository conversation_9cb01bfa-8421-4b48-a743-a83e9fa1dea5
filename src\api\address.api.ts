import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const addressApi = {
  getDistricts: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/district",
      params,
    }),
  getCities: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/city",
      params,
    }),
  removeCityFromArea: (cityId?: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/city/${cityId}/area`,
      data,
      method: "patch",
    }),
  getWard: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/ward",
      params,
    }),

  updateShipFee: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/city/${id}`,
      method: "patch",
      data,
    }),
};
