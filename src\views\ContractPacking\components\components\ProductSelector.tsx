
import { ContractProduct } from "@/types/contract";
import { AutoComplete } from "antd";

type Props = {
  products: ContractProduct[];
  value?: string;
  onChange?: (value: string, option?: any) => void;
  placeholder?: string;
};

const ProductSelector = ({ products, value, onChange, placeholder = "Chọn sản phẩm..." }: Props) => {
  const options = products.map((product) => ({
    label: product.productName,
    value: product.productName,
    product: product,
  }));

  return (
    <AutoComplete
      value={value}
      options={options}
      onChange={onChange}
      placeholder={placeholder}
      allowClear
      filterOption={(inputValue, option) =>
        option?.label?.toLowerCase().includes(inputValue.toLowerCase()) || false
      }
    />
  );
};

export default ProductSelector;
