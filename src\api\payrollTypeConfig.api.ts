import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const payrollTypeConfigApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payrollTypeConfig",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payrollTypeConfig",
      data,
      method: "post",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payrollTypeConfig/import",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollTypeConfig/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollTypeConfig/${id}`,
      method: "delete",
    }),
};
