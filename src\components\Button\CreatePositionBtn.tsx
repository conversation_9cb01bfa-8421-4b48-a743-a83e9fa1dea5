import {
  PositionModal,
  PositionModalRef,
} from "@/views/PositionPage/components/PositionModal";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useRef } from "react";

type Props = {
  onSuccess: () => void;
};

const CreatePositionBtn = ({ onSuccess }: Props) => {
  const positionModalRef = useRef<PositionModalRef>();
  const handleOnOpenCreateModal = () => {
    positionModalRef.current?.handleOpen("create");
  };

  return (
    <>
      <Button
        title="Thêm chức vụ"
        onClick={handleOnOpenCreateModal}
        type="primary"
        size="small"
      >
        <PlusOutlined></PlusOutlined>
      </Button>
      <PositionModal ref={positionModalRef} onSubmitOk={onSuccess} />
    </>
  );
};

export default CreatePositionBtn;
