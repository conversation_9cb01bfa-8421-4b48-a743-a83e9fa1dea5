import { AppModalAction } from "@/components/Modal/AppModal";
import { ImportOutlined } from "@ant-design/icons";
import { Button, message } from "antd";
import { RcFile } from "antd/es/upload";
import { useRef, useState } from "react";

import { areaDetailApi } from "@/api/areaDetail.api";
import { ExcelUtil } from "@/utils/Excel/ExcelUtil";
import { ExportImportExcelDemoFile } from "@/utils/exportExcelDemo";
import { ErrorLog, ImportExcelCol } from "@/utils/importExcel";
import ImportDataModal from "@/views/HolidayPage/components/ImportDataModal";
import ExcelJS from "exceljs";
import { getBaoBiUnits } from "./PackageModal";
import { ImportExcelData } from "@/utils/importExcelData";
import { Upload } from "antd/lib";
import { ProposalDetail } from "@/types/proposal";

type Props = {
  onSuccess?: (data: ProposalDetail[]) => void;
};

const excelColumns: ImportExcelCol<ProposalDetail>[] = [
  {
    title: "Loại bao bì",
    key: "name",
    validate: [
      {
        type: "required",
        errorType: "danger",
      },
    ],
  },
  {
    title: "Mã",
    key: "productCode",
    validate: [
      {
        type: "required",
        errorType: "danger",
      },
    ],
  },

  {
    title: "Số lượng theo hợp đồng",
    key: "contractQuantity",
    validate: [
      {
        type: "required",
        errorType: "danger",
      },
    ],
  },
  {
    title: "Đơn vị/1 thùng",
    key: "quantityOnUnit",
    validate: [
      {
        type: "required",
        errorType: "danger",
      },
    ],
  },
  {
    title: "% phụ trội",
    key: "missPercent",
    validate: [
      {
        type: "required",
        errorType: "danger",
      },
    ],
  },
  {
    title: "Ghi chú",
    key: "note",
  },
];

const ImportPackageDetail = ({ onSuccess }: Props) => {
  const importModal = useRef<AppModalAction>();
  const [data, setData] = useState<ProposalDetail[]>([]);
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [loading, setLoading] = useState(false);

  const onDownloadDemoFile = async () => {
    const units = await getBaoBiUnits();

    const wb = new ExcelJS.Workbook();

    const excelDemoFile = new ExportImportExcelDemoFile(
      excelColumns.map((v) => ({
        ...v,
        header: v.title + "",
      })),
      wb
    );

    const defaultData = units.map((v) => ({
      name: v.name,
    }));

    excelDemoFile.init("Quy cách đóng gói");

    excelDemoFile.demoSheet.addRows(defaultData);

    ExcelUtil.autoWidth(excelDemoFile.demoSheet);

    excelDemoFile.download("Quy cách đóng gói - Đề nghị bao bì.xlsx");
  };

  const handleReadExcelData = async (file: RcFile) => {
    try {
      const importData = new ImportExcelData<ProposalDetail, any>({
        columns: excelColumns,
        importColumns: excelColumns,
        file,
      });

      importData.validateFileType();

      const data = await importData.readData();

      console.log(data);

      setData(data.data);
      setErrors(data.dangerErrors);

      return false;
    } catch (error: any) {
      message.error(error?.message, 3);
      console.error(error);
      return Upload.LIST_IGNORE;
    }
  };

  return (
    <div>
      <Button
        size="small"
        onClick={() => {
          importModal.current?.open({});
        }}
        type="primary"
      >
        <ImportOutlined></ImportOutlined>
        Import dữ liệu
      </Button>
      <ImportDataModal
        errors={errors.map((v) => `[DÒNG ${v?.rowNum}]: ${v.message}`)}
        dragger={{
          onChange: (info) => {
            if (info.file.status == "removed") {
              setData([]);
              setErrors([]);
            }
          },
        }}
        onDownloadDemoExcel={onDownloadDemoFile}
        modal={{
          afterClose: () => {
            setData([]);
            setErrors([]);
          },
          destroyOnClose: true,
          onOk: () => {
            onSuccess?.(data);
            importModal.current?.close();
          },
          confirmLoading: loading,
          okButtonProps: {
            disabled: !data?.length || !!errors.length,
          },
          width: 600,
          title: "Nhập dữ liệu bao bì",
        }}
        ref={importModal}
        beforeUpload={handleReadExcelData}
      />
    </div>
  );
};

export default ImportPackageDetail;
