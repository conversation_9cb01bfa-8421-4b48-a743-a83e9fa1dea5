import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const reportApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report",
      params,
    }),
  findOne: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      params,
    }),
  summary: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report/summary/status",
      params,
    }),
  summaryArea: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report/summary/area",
      params,
    }),
  summaryParentArea: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report/summary/parentArea",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/report",
      data,
      method: "post",
    }),
  resubmit: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/resubmit`,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      method: "patch",
      data,
    }),
  upload: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/upload`,
      method: "post",
      data,
    }),
  fixed: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/fixed`,
      method: "patch",
      data,
    }),
  resolve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/resolve`,
      method: "patch",
      data,
    }),
  fail: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}/fail`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/report/${id}`,
      method: "delete",
    }),
};
