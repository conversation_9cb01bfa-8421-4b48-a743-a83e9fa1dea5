import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const configurationApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration`,
      params,
    }),
  findOne: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration/param`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration`,
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/configuration/${id}`,
      method: "patch",
      data,
    }),
};
