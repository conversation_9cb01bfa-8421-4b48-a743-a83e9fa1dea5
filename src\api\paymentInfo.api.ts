import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const paymentInfoApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentInfo",
      params,
    }),

  findOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentInfo/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentInfo",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentInfo/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentInfo/${id}`,
      method: "delete",
    }),
};
