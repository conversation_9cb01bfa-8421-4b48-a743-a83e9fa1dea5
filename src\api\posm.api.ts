import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const posmApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posm",
      params,
    }),
  findStore: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posm/${id}/store`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posm",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posm/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posm/${id}`,
      method: "delete",
    }),
};
