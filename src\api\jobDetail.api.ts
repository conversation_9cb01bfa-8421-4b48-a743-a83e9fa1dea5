import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const jobDetailApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/jobDetail",
      params,
    }),
  findPrev: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/jobDetail/prev",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/jobDetail",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/jobDetail/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/jobDetail/${id}`,
      method: "delete",
    }),
};
