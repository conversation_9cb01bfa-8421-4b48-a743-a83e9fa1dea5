import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const presentationActivityApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/presentationActivity",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/presentationActivity",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/presentationActivity/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/presentationActivity/${id}`,
      method: "delete",
    }),
};
