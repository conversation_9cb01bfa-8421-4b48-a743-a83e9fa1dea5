import {
  DepartmentModal,
  DepartmentModalRef,
} from "@/views/Department/components/DepartmentModal";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useRef } from "react";
import { UserZaloModal, UserZaloModalRef } from "../Modal/UserZaloModal";
import { ZaloUser } from "@/types/zalo-user";

type Props = {
  onSuccess: (record: ZaloUser) => void;
};

const AddUserZaloBtn = ({ onSuccess }: Props) => {
  const modal = useRef<UserZaloModalRef>();
  const handleOnOpenCreateModal = () => {
    modal.current?.handleOpen("create");
  };

  return (
    <>
      <Button
        title="Thêm user zalo"
        onClick={handleOnOpenCreateModal}
        type="primary"
        size="small"
      >
        <PlusOutlined></PlusOutlined>
      </Button>
      <UserZaloModal ref={modal} onSubmitOk={onSuccess} />
    </>
  );
};

export default AddUserZaloBtn;
