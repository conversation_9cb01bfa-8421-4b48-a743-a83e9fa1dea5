import { Area } from "@/types/area";
import { Select } from "antd";
import { FormInstance } from "antd/es/form/Form";
import { Rule } from "antd/lib/form";
import FormItem, { FormItemProps } from "antd/lib/form/FormItem";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { useAddress } from "../../hooks/useAddress";
import { AddressData, City, District, Ward } from "../../types/address";

const rules: Rule[] = [{ required: true, message: "Bắt buộc nhập!" }];

export interface AddressParam {
  parentCode?: string;
}

export interface AddressSelect {
  setValue: (data: IAddress) => void;
  fetchCity: (areaId: number) => void;
}

export interface IAddress {
  district: District;
  city: City;
  ward?: Ward;
  area?: Area;

  // address: string;
}

export const AddressSelect = React.forwardRef(
  (
    {
      hiddenWard = false,
      formItemProps,
      form,
      onChange,
      defaultValue,
      isHaveArea = false, //Nếu is Have area thì disable city (Chọn area mới chọn city)
    }: {
      hiddenWard?: boolean;
      defaultValue?: IAddress;
      form: FormInstance<any>;
      onChange: (data: any) => void;
      formItemProps?: FormItemProps;
      isHaveArea?: boolean;
    },
    ref
  ) => {
    const [queryWard, setQueryWard] = useState<AddressParam>();
    const [queryDistrict, setQueryDistrict] = useState<AddressParam>();
    const [isDisabledCity, setIsDisabledCity] = useState(isHaveArea);
    const {
      cities,
      districts,
      loading,
      wards,
      fetchCity,
      fetchDistrict,
      fetchWard,
      clearDistrict,
      clearWard,
      updateCity,
      updateDistrict,
      updateWard,
    } = useAddress();

    useImperativeHandle(
      ref,
      () => {
        return {
          setValue(data: IAddress) {
            if (data?.city) {
              fetchCity({ areaId: data.area?.id });
              setIsDisabledCity(false);
            }
            if (data?.district) {
              updateDistrict([...districts, data.district]);
            }
            if (data?.ward) {
              updateWard([...wards, data.ward]);
            }

            form.setFieldsValue({
              cityId: data?.city?.id,
              wardId: data?.ward?.id,
              districtId: data?.district?.id,
            });
          },
          fetchCity(areaId: number) {
            if (areaId) {
              fetchCity({ areaId });
              setIsDisabledCity(false);
            } else {
              setIsDisabledCity(true);
              updateCity([]);
              updateDistrict([]);
              updateWard([]);
            }
          },
        };
      },
      [cities, wards, districts]
    );

    useEffect(() => {
      if (queryDistrict?.parentCode) {
        fetchDistrict(queryDistrict);
      }
    }, [queryDistrict]);

    useEffect(() => {
      if (queryWard?.parentCode) {
        fetchWard(queryWard);
      }
    }, [queryWard]);

    useEffect(() => {
      fetchCity().then((data) => {
        if (defaultValue) {
          if (defaultValue.city) {
            updateCity([...data, defaultValue.city]);
            setQueryDistrict({ parentCode: defaultValue.city.code });
          }
          if (defaultValue.district) {
            setQueryWard({ parentCode: defaultValue.district.code });
          }
          if (defaultValue.ward) {
            updateWard([...wards, defaultValue.ward]);
          }

          form.setFieldsValue({
            cityId: defaultValue?.city?.id,
            wardId: defaultValue?.ward?.id,
            districtId: defaultValue?.district?.id,
          });
        }
      });
    }, [defaultValue]);

    const handleChangeCity = (cityId: number) => {
      form.resetFields(["wardId", "districtId"]);
      if (cityId) {
        const code = cities.find((e) => e.id == cityId)?.code;
        setQueryDistrict({ parentCode: code });
      } else {
        clearDistrict();
      }
    };

    const handleChangeDistrict = (districtId: number) => {
      form.resetFields(["wardId"]);
      if (districtId) {
        const parentCode = districts.find((e) => e.id == districtId)?.code;
        setQueryWard({ parentCode });
      } else {
        clearWard();
      }
    };

    const handleSubmitAddress = (value: number) => {
      if (value) {
        const { districtId, cityId, wardId } = form.getFieldsValue();
        const data: AddressData = {
          district: districts.find((e) => e.id == districtId),
          ward: wards.find((e) => e.id == wardId),
          city: cities.find((e) => e.id == cityId),
        };
        onChange(data);
      } else {
        onChange(undefined);
      }
    };

    return (
      <div className="address-selector-form">
        <FormItem
          {...formItemProps}
          rules={rules}
          required
          label="Tỉnh/Thành phố"
          name={"cityId"}
        >
          <Select
            disabled={isDisabledCity}
            onChange={handleChangeCity}
            style={{ width: "100%" }}
            onClear={() => {
              clearDistrict();
              clearWard();
            }}
            allowClear
            placeholder="Nhập tên tỉnh/thành phố"
            showSearch
            filterOption={(input, option) =>
              option?.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            {cities?.map((item) => (
              <Select.Option value={item.id} key={item.id}>
                {item.nameWithType}
              </Select.Option>
            ))}
          </Select>
        </FormItem>
        <FormItem
          {...formItemProps}
          rules={rules}
          required
          label="Quận/Huyện"
          name={"districtId"}
        >
          <Select
            disabled={!districts.length}
            onClear={clearWard}
            onChange={handleChangeDistrict}
            style={{ width: "100%" }}
            allowClear
            placeholder="Nhập tên Quận/Huyện"
            showSearch
            filterOption={(input, option) =>
              option?.props.children
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            }
          >
            {districts?.map((item) => (
              <Select.Option value={item.id} key={item.id}>
                {item.nameWithType}
              </Select.Option>
            ))}
          </Select>
        </FormItem>
        {!hiddenWard && (
          <FormItem
            {...formItemProps}
            rules={rules}
            required
            label="Xã/Phường"
            name={"wardId"}
          >
            <Select
              disabled={!wards.length}
              style={{ width: "100%" }}
              allowClear
              onChange={handleSubmitAddress}
              placeholder="Nhập tên Xã/Thị trấn"
              showSearch
              filterOption={(input, option) =>
                option?.props.children
                  .toLowerCase()
                  .indexOf(input.toLowerCase()) >= 0
              }
            >
              {wards?.map((item) => (
                <Select.Option value={item.id} key={item.id}>
                  {item.nameWithType}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
        )}

        {/* </Form> */}
      </div>
    );
  }
);
