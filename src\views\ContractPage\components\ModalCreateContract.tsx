import { fileAttachApi } from "@/api/uploadFile.api";
import { userStore } from "@/store/userStore";
import { FileAttachType } from "@/types/file";
import { ModalStatus } from "@/types/modal";
import { v4 as uuidv4 } from "uuid";

import { purchaseApi } from "@/api/purchase.api";
import TableListStaffGroup from "@/components/StaffGroupComponents/TableListStaffGroup";
import { useProvider } from "@/hooks/useProvider";
import { ProposalStatus, ProposalType } from "@/types/proposal";
import { PurchaseOrderStatus } from "@/types/purchaseOrder";
import { Staff, StaffGroupAction } from "@/types/staff";
import { TableSuggestionPaymentRef } from "@/views/SuggestionPaymentPage/components/TableSuggestionPayment";
import { CloseOutlined, SaveFilled, WifiOutlined } from "@ant-design/icons";
import { useNetworkState } from "@uidotdev/usehooks";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  Modal,
  Popover,
  Radio,
  Select,
  UploadFile,
  message,
  notification,
} from "antd";
import { useForm } from "antd/es/form/Form";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { toJS } from "mobx";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useSearchParams } from "react-router-dom";

import { commentApi } from "@/api/comment.api";
import {
  Contract,
  ContractPackingDetail,
  ContractPO,
  ContractProduct,
  ContractStatus,
  ContractType,
  ContractTypeTrans,
  PODetail,
} from "@/types/contract";
import { useContract } from "@/hooks/useContract";
import {
  TableContractRef,
  validateTableContractDetails,
} from "./TableContract";
import { CustomerSelector } from "@/components/Selector/CustomerSelector";
import { MarketSelector } from "@/components/Selector/MarketSelector";
import { DetailInputProps } from "@/views/ProductionManager/Packaging/components/PackageDetailTable";
import { useContractContent } from "@/hooks/useContractContent";
import {
  ContractContentModal,
  ContractContentModalRef,
} from "./ModalContractContents";
import { ContractConfigType, ContractContent } from "@/types/contractContent";
import { contractApi } from "@/api/contract.api";
import { contractContentApi } from "@/api/contractContent.api";
import { Category, CategoryType } from "@/types/category";
import { categoryApi } from "@/api/category.api";
import { categoryByInventory } from "@/types/inventoryDetail";
import { PermissionName } from "@/router";
import { observer } from "mobx-react";

import { ProductBySelector } from "@/components/Selector/ProductBySelector";
import { useTermOfPayment } from "@/hooks/useTermOfPayment";
import { useUnit } from "@/hooks/useUnit";
import {
  TableContractPO,
  TableContractProductRef,
  validateContractProducts,
} from "./TableContractPO";
import { TablePODetail } from "./TablePODetail";
import { TableContractProductNew } from "./TableContractProductNew";

interface ModalCreateContractProps {
  fetchData: () => void;
  onOk: (isDraff?: boolean) => void;
  onCancel: () => void;
}

export interface ModalCreateContractRef {
  handleOpen: (data?: Contract) => void;
  handleUpdate: (record: Contract) => void;
  createData: (data?: Contract, isOpenTemp?: boolean) => void;
}

const ModalCreateContract = React.forwardRef(
  ({ fetchData, onOk, onCancel }: ModalCreateContractProps, ref) => {
    let [searchParams, setSearchParams] = useSearchParams();

    const isEditProcedureCode = userStore.checkRole(
      PermissionName.editProcedureCode
    );
    const {
      fetchData: fetchPaymentTerm,
      paymentTerms,
      query: queryPaymentTerm,
    } = useTermOfPayment({
      initQuery: { page: 1, limit: 50 },
    });
    const {
      fetchData: fetchContract,
      contracts,
      query: queryPurchase,
    } = useContract({
      initQuery: {
        page: 1,
        limit: 50,
        status: PurchaseOrderStatus.Complete,
      },
    });

    const {
      fetchData: fetchDataUnit,
      units,
      query: queryUnit,
    } = useUnit({
      initQuery: { page: 1, limit: 0 },
    });

    useEffect(() => {
      fetchPaymentTerm();
      fetchDataUnit();
    }, []);

    const [contractProducts, setContractProducts] = useState<
      Partial<ContractProduct>[]
    >([]);
    const [tableContractProducts, setTableContractProducts] = useState<
      Partial<ContractProduct>[]
    >([]);
    const [tableContractPOs, setTableContractPOs] = useState<
      Partial<ContractPO>[]
    >([]);
    const tableContractRef = useRef<TableContractRef>();
    const tableContractProductRef = useRef<TableContractProductRef>();
    const tableSuggestionPaymentRef = useRef<TableSuggestionPaymentRef>();
    const tableSuggestionRepairRef = useRef<TableSuggestionPaymentRef>();
    const [form] = useForm();
    const [details, setDetails] = useState<any>();
    const [packingDetails, setPackingDetails] = useState<
      Partial<ContractPackingDetail>[]
    >([]);

    const [status, setStatus] = useState<ModalStatus>("create");
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [fileListInvoice, setFileListInvoice] = useState<UploadFile[]>([]);
    const [fileListPreview, setFileListPreview] = useState<UploadFile[]>([]);
    const [inspecStaffs, setInspecStaffs] = useState<Staff[]>([]);
    const [followStaffs, setFollowStaffs] = useState<Staff[]>([]);
    const [visible, setVisible] = useState(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [loadingComment, setLoadingComment] = useState<boolean>(false);
    const contractContentModalRef = useRef<ContractContentModalRef>(null);
    const [contractContents, setContractContents] = useState<
      Partial<ContractContent>[]
    >([]);
    const [customerId, setCustomerId] = useState(0);

    const [contractTempSelected, setContractTempSelected] =
      useState<Contract>();
    const {
      fetchData: fetchProviders,
      providers,
      query: queryProvider,
    } = useProvider({
      initQuery: { page: 1, limit: 50 },
    });

    const {
      fetchData: fetchContractConfig,
      contractContents: contractConfigs,
      query: queryContractConfig,
      setQuery: setQueryContractConfig,
    } = useContractContent({
      initQuery: { page: 1, limit: 50 },
    });
    const [exportLoading, setExportLoading] = useState(false);

    const proposalType = searchParams.get("proposalType") as ProposalType;

    const isCreateFromBaoBi = proposalType == ProposalType.BaoBi;

    const network = useNetworkState();

    const [productsPackaging, setProductsPackaging] = useState([]);

    const [api, contextHolder] = notification.useNotification();
    const netWorkErrorNotification = (action: string) => {
      if (action === "OPEN") {
        api.open({
          message: (
            <div className="flex gap-2">
              <WifiOutlined className="text-red-500" />
              <span className="text-lg text-red-500 font-semibold">
                Không có kết nối mạng!
              </span>
            </div>
          ),
          description:
            "Bạn đang offline, hãy kiểm tra lại đường truyền và tiếp tục thao tác.",
          duration: 0,
          placement: "bottomRight",
        });
      } else {
        api.destroy();
      }
    };

    const netWorkConnectNotification = (action: string) => {
      if (action === "OPEN") {
        api.open({
          message: (
            <div className="flex gap-2">
              <WifiOutlined className="text-green-500" />
              <span className="text-lg text-green-500 font-semibold">
                Kết nối mạng đã được khôi phục!
              </span>
            </div>
          ),
          description: "Đã có kết nối mạng trở lại, bạn có thể tiếp tục.",
          duration: 0,
          placement: "bottomRight",
        });
      } else {
        api.destroy();
      }
    };

    const handleAddPacking = () => {
      //@ts-ignore
      // setContractDetails([...contractDetails, newProduct])
      // console.log("[...contractDetails, newProduct]", [...contractDetails, newProduct])
    };

    const handleAddContractProduct = (duplicate?: number) => {
      if (duplicate !== undefined) {
        const newProduct: Partial<ContractProduct> = {
          code: "",
          productName: "",
          ascCF: false,
          ascNCF: false,
          quantity: 0,
          unitPrice: 0,
          amount: 0,
          specification: "",
          specificationEn: "",
          quantityUnit: 0,
          quantityFinal: 0,
          packing: "",
          thermometer: 0,
          unitId: 0,
        };
        setContractProducts([...contractProducts, newProduct]);
        // setContractProductsList([...contractProductsList]);
      } else {
        const newProduct: Partial<ContractProduct> = {
          code: "",
          productName: "",
          ascCF: false,
          ascNCF: false,
          quantity: 0,
          unitPrice: 0,
          amount: 0,
          specification: "",
          specificationEn: "",
          quantityUnit: 0,
          quantityFinal: 0,
          packing: "",
          thermometer: 0,
          unitId: 0,
        };
        setContractProducts([...contractProducts, newProduct]);
      }
    };

    const handleChangeContractPO = (index: number, po: Partial<ContractPO>) => {
      const updated = [...tableContractPOs];
      updated[index] = {
        ...po, poDetails: tableContractPOs[index]?.poDetails ?? [
          {
            productName: "",
            quantity: 0,
            unitPrice: 0,
            amount: 0,
            name: "",
            contractProductIndex: 0
          } as any
        ]
      };
      setTableContractPOs(updated);
    };

    const handleChangeContractDetail = (
      index: number,
      product: Partial<ContractProduct & { date: string }>
    ) => {
      const updated = [...contractProducts];
      updated[index] = product as any;
      setContractProducts(updated);
    };

    const updateTotalPriceByDetails = (
      detailsList: Partial<ContractProduct>[][]
    ) => {
      setContractProducts((prevProducts) =>
        prevProducts.map((product, index) => {
          const details = detailsList[index] || [];
          const totalQuantity = details.reduce(
            (sum, detail) => sum + (detail.quantity || 0),
            0
          );


          let totalPrice = 0;
          details.map((detail) => {
            if (detail.contractProductIndex !== undefined) {
              const unitPrice = tableContractProducts[detail.contractProductIndex]?.unitPrice ?? 0;
              totalPrice += unitPrice * (detail?.quantity ?? 0)
            }
          })
          const totalAmount = details.reduce(
            (sum, detail) => sum + (detail.unitPrice || 0),
            0
          );
          return {
            ...product,
            price: totalAmount,
            quantity: totalQuantity,
            totalPrice: totalPrice
          };
        })
      );
    };

    // Khi cập nhật dữ liệu PODetail
    const handleChangeContractDetails = (
      index: number,
      details: Partial<PODetail>[]
    ) => {
      const updatedPOList = [...tableContractPOs];
      if (updatedPOList[index]) {
        // Map lại các trường cho poDetails
        const validDetails = (details as PODetail[]).map((detail) => ({
          ...detail,
          name: detail.name || "",
        }));
        updatedPOList[index] = {
          ...updatedPOList[index],
          poDetails: validDetails,
        };
      }


      console.log("setContractProducts", updatedPOList)

      // console.log("updatedPOList", updatedPOList);

      setTableContractPOs(updatedPOList);
      updateTotalPriceByDetails(
        updatedPOList.map((po) => (po as any).poDetails || [])
      );
    };

    const handleDeleteContractPO = (index: number) => {
      const updatedProducts = [...tableContractPOs];
      updatedProducts.splice(index, 1);
      setTableContractPOs(updatedProducts);

      const updatedDetails = [...contractProducts];
      updatedDetails.splice(index, 1);
      setContractProducts(updatedDetails);
    };

    const handleGetStaffGroup = async (type: CategoryType) => {
      //@ts-ignore
      const categoryType = categoryByInventory[type];
      const { data } = await categoryApi.findAll({ type: categoryType });
      const staffList: Category = data.categories?.[0];

      setFollowStaffs(staffList?.followStaffs || []);
      setInspecStaffs(staffList?.inspecStaffs || []);
    };

    // Function để fetch contract content dựa trên contractType
    const fetchContractContentByType = useCallback(async (contractType: ContractType) => {
      try {
        // Cập nhật query với contractType
        const newQuery = {
          ...queryContractConfig,
          contractType,
          page: 1,
          limit: 50
        };
        setQueryContractConfig(newQuery);

        // Gọi API trực tiếp với query mới
        const { data } = await contractContentApi.findAll(newQuery);

        // API trả về data.contractConfigs, không phải data trực tiếp
        const contractContents = data.contractConfigs?.map((it: any) => ({
          id: it.id,
          type: it.type,
          titleVi: it.titleVi,
          titleEn: it.titleEn,
          contentVi: it.contentVi,
          contentEn: it.contentEn,
        })) || [];

        setContractContents(contractContents);

        console.log('Fetched contract contents for type:', contractType, contractContents);
      } catch (error) {
        console.error('Error fetching contract content:', error);
      }
    }, [queryContractConfig, setQueryContractConfig]);

    useEffect(() => {
      const contractContents = contractConfigs.map((it) => ({
        id: it.id,
        type: it.type,
        titleVi: it.titleVi,
        titleEn: it.titleEn,
        contentVi: it.contentVi,
        contentEn: it.contentEn,
      }));
      setContractContents(contractContents);
    }, [contractConfigs]);

    useEffect(() => {
      if (!network.online) {
        netWorkConnectNotification("CLOSE");
        netWorkErrorNotification("OPEN");
      } else {
        netWorkErrorNotification("CLOSE");
        netWorkConnectNotification("OPEN");
      }
    }, [network.online]);

    const handleGetOneComment = async (id: number) => {
      try {
        setLoadingComment(true);
        const { data } = await commentApi.findOne(id);
        return data;
      } catch (error) {
      } finally {
        setLoadingComment(false);
      }
    };

    useImperativeHandle(
      ref,
      () => {
        return {
          /**
           * Fill lại data vào form
           */
          async handleOpen(data: Contract) {


            if (data) {
              fetchContract();
              //   setTimeout(() => {
              //     tableQuoteRef.current?.setValue(
              //       //@ts-ignore
              //       data?.details
              //     );
              //   }, 100);

              setContractTempSelected(data);
              form.setFieldsValue({
                ...data,
                deadlineDate: "",
              });
              setVisible(true);
            } else {
              fetchProviders();
              fetchContract();
              handleGetStaffGroup(CategoryType.Contract);
              form.resetFields();
              setVisible(true);
              setContractTempSelected(undefined);

              // Set loại hợp đồng mặc định và fetch content tương ứng
              const defaultContractType = ContractType.General;
              form.setFieldsValue({
                type: defaultContractType,
                contentVi: "",
                contentEn: "",
              });

              // Fetch contract content theo loại hợp đồng mặc định
              await fetchContractContentByType(defaultContractType);
            }
          },

          async handleUpdate(record?: Contract) {
            fetchContract();
            setStatus("update");
            setDetails(record);
            setVisible(true);
            //@ts-ignore
            setContractProducts(
              record?.contractProducts.map((item) => ({
                ...item,
              })) || []
            );
            setContractProducts([...(record?.contractProducts || [])]);
            // setPackingDetails(
            //   record?.contractProduct
            //     ?.contractPackingDetails as Partial<ContractPackingDetail>[]
            // );
            if (record?.contractContents) {
              setContractContents(record?.contractContents);
            } else if (record?.type) {
              // Nếu không có contractContents, fetch theo contractType
              await fetchContractContentByType(record.type);
            }
            //to do
            const indexDocs = record?.contractContents?.findIndex(
              (item) => item.type === ContractConfigType.RequiredDocuments
            );
            const indexPayment = record?.contractContents?.findIndex(
              (item) => item.type === ContractConfigType.Payment
            );

            const docs =
              indexDocs && indexDocs !== -1
                ? record?.contractContents?.[indexDocs]
                : paymentTerms;
            const payment =
              indexPayment && indexPayment !== -1
                ? record?.contractContents?.[indexPayment]
                : paymentTerms;

            form.setFieldsValue({
              ...record,
              productBy: record?.productBy && JSON.parse(record.productBy),
              inspecStaffIds: record?.inspecStaffs?.map((item) => item.id),
              followStaffIds: record?.followStaffs?.map((item) => item.id),
              customerId: record?.customer?.id,
              marketId: record?.market?.id,
              signAt: record?.signAt ? dayjs.unix(record.signAt) : undefined,
              // invoiceFileIds: record?.invoiceFiles.map((item) => item.id),
              staffId: record?.createdStaff?.id,
              deadlineDate: "",
              paymentTerm: record?.paymentTerm,
              packing: record?.contractDetails?.[0].packing,
              total: record?.total,
              unit: record?.unit,
              contactPersonName: record?.contactPersonName,
              docs: docs,
              payment: payment,
              indexDocs: indexDocs,
              indexPayment: indexPayment,
              contentVi: (docs as any)?.contentVi,
              contentEn: (docs as any)?.contentEn,
              // contType: record?.contType
            });

            setFollowStaffs(record?.followStaffs || []);
            setInspecStaffs(record?.inspecStaffs || []);

            const contractProducts = record?.contractProducts || [];
            const allContractDetails = record?.contractDetails || [];

            const groupedDetailsList = contractProducts.map((product) => {
              const code = product.code;
              // Lọc ra tất cả contractDetails có code trùng
              const detailsForThisProduct = allContractDetails.filter(
                (detail) => detail.code === code
              );
              return detailsForThisProduct;
            });

            // Set lại contractDetailsList
            setTableContractProducts((record?.contractProducts ?? []) as any);
            setContractProducts((record?.contractPos ?? []) as any);

            setTableContractPOs((record?.contractPos?.map((contractPo) => ({
              ...contractPo,
              poDetails: contractPo?.poDetails?.map((detail) => {
                const index = record?.contractProducts?.findIndex((item) => item.id === detail?.contractProduct?.id);
                return ({
                  ...detail,
                  contractProductIndex: index
                })
              })
              // poDetails: {
              //   ...contractPo.poDetails ?? [],
              //   contractProductIndex: 0
              // }
            })) ?? []) as any)
            console.log("setTableContractPOs", (record?.contractPos?.map((contractPo) => ({
              ...contractPo,
              poDetails: {
                ...contractPo.poDetails
              }
              // poDetails: {
              //   ...contractPo.poDetails ?? [],
              //   contractProductIndex: 0
              // }
            })) ?? []));

            setTimeout(() => {
              //phải settimeout để tránh không component chưa xuất hiện kịp
              if (record) {
                tableContractRef.current?.setValue(record);
              }
            }, 100);
          },

          createData(isTemp: boolean) {
            form.resetFields();
            setVisible(true);
            handleGetStaffGroup(CategoryType.Contract);

            setContractTempSelected(undefined);
          },
        };
      },
      []
    );

    const validateTable = (tablePODetails: PODetail[] = []) => {
      let isValidate = true;
      if (tablePODetails.length == 0) {
        return false;
      }
      console.log({ tablePODetails });
      isValidate = tablePODetails.every(
        (item) =>
          item.name &&
          item.price &&
          item.quantity &&
          item.amount &&
          item.contractProduct
      );

      return isValidate;
    };

    const getPurchaseCode = async (providerId: number) => {
      try {
        const { data } = await purchaseApi.code({ providerId });
        form.setFieldsValue({ code: data.code });
      } catch (error) {
        form.resetFields(["code"]);
      }
    };

    const handleOnAddDetail = () => {
      setPackingDetails((details) => {
        const newDetail: Partial<ContractPackingDetail> =
          packingDetails.length == 0
            ? {
              id: Date.now(),
              nameVi: "Thùng",
              nameEn: "Container",
              contractQuantity: 0,
              note: "",
              productCode: "",
            }
            : {
              id: Date.now(),
              nameVi: "",
              nameEn: "",
              contractQuantity: 0,
              note: "",
              productCode: "",
            };

        return [...details, newDetail];
      });
    };

    const debounceChangeDetailData = debounce(
      (value, fieldName, record) =>
        handleChangeDetailData(value, fieldName, record),
      200
    );

    const handleChangeDetailData: DetailInputProps<ContractPackingDetail>["onChange"] =
      (value, fieldName, record) => {
        const updatedDetails = [...packingDetails];
        const index = updatedDetails.findIndex((item) => item.id === record.id);

        Object.assign(record, {
          [fieldName]: value,
        });

        if (fieldName === "quantityOnUnit" && index > 0) {
          const base = updatedDetails[0];
          const baseQuantity = Number(base.contractQuantity || 0);
          const unitPerBase = Number(value || 0);

          record.contractQuantity = baseQuantity * unitPerBase;
        }

        setPackingDetails(updatedDetails);
      };

    const updateData = async (payload: any) => {
      const resUpdate = await contractApi.update(details.id, payload);
      onUpdateOk();

      setTimeout(() => {
        tableSuggestionPaymentRef.current?.setValue(
          resUpdate?.data?.proposalDetails,
          resUpdate.data
        );
      }, 100);

      message.success("Cập nhật thành công");
    };

    //submitFormCreate
    const createData = async (payload: any) => {
      console.log("createData", payload);
      setLoading(true);
      console.log({ payload });

      await contractApi.create(payload);
      message.success("Thêm mới thành công");
      //TODO MOdal
      onUpdateOk();
    };


    // Cải thiện hàm validateContractProductQuantities để hiển thị tên sản phẩm
    const validateContractProductQuantities = (
      contractProducts: ContractProduct[],
      contractPos: ContractPO[]
    ): {
      isValid: boolean;
      exceeded: {
        index: number;
        productName: string;
        total: number;
        max: number
      }[]
    } => {
      const quantityByIndex: Record<number, number> = {};

      for (const pos of contractPos) {
        for (const detail of pos.poDetails || []) {
          const index = detail.contractProductIndex;
          if (index !== undefined && index >= 0) {
            if (!quantityByIndex[index]) {
              quantityByIndex[index] = 0;
            }
            quantityByIndex[index] += detail.quantity || 0;
          }
        }
      }

      const exceeded: {
        index: number;
        productName: string;
        total: number;
        max: number
      }[] = [];

      for (const index in quantityByIndex) {
        const indexNum = Number(index);
        const total = quantityByIndex[indexNum];
        const product = contractProducts[indexNum];
        const max = product?.quantity ?? 0;

        if (total > max) {
          exceeded.push({
            index: indexNum,
            productName: product?.productName || `Sản phẩm #${indexNum + 1}`,
            total,
            max
          });
        }
      }

      return {
        isValid: exceeded.length === 0,
        exceeded,
      };
    };

    const showValidationError = (exceededItems: any[]) => {
      notification.error({
        message: "Lỗi số lượng sản phẩm",
        description: (
          <div>
            <div className="mb-2">Các sản phẩm sau vượt quá số lượng hợp đồng:</div>
            <div className="space-y-1">
              {exceededItems.map((item, index) => (
                <div key={index} className="flex justify-between bg-red-50 p-2 rounded">
                  <span className="font-medium">{item.productName}</span>
                  <span className="text-red-600">
                    {item.total}/{item.max} (vượt {item.total - item.max})
                  </span>
                </div>
              ))}
            </div>
          </div>
        ),
        duration: 5,
        placement: "topRight",
      });
    };

    const handleCreateContract = async (isDraff?: boolean) => {
      // Kiểm tra bảng hàng hóa
      // const error = validateContractDetails(contractDetailsGroupByName, status);
      // if (error) {
      //   message.error(error);
      //   return;
      // }

      // Kiểm tra bảng PO
      const errorPO = validateContractProducts(contractProducts as any);
      if (errorPO) {
        message.error(errorPO);
        return;
      }

      // Kiểm tra hàng hóa của PO
      const errorTable = validateTableContractDetails(contractProducts);
      if (errorTable) {
        message.error(errorTable);
        return;
      }

      console.log({ contractContents });
      const followStaffIds = followStaffs?.map((item: Staff) => {
        return item.id;
      });

      const inspecStaffIds = inspecStaffs?.map((item: Staff) => {
        return item.id;
      });

      if (inspecStaffIds.length == 0) {
        message.error("Vui lòng chọn người duyệt");
        return;
      }
      if (followStaffIds.length == 0) {
        message.error("Vui lòng chọn người theo dõi");
        return;
      }

      const {
        name,
        code,
        isAllInspec,
        description,
        fileAttaches,
        categoryId,
        staffId,
        providerId,
        parentId,
        paymentTermId,
        customerId,
        marketId,
        note,
        signAt,
        procedureCode,
        po,
        productBy,
        total,
        unit,
        contactPersonName,
        paymentTerm,
        termsOfDelivery,
        requiredDocuments,
        type
      } = form.getFieldsValue(true);
      if (
        !packingDetails.every(
          (item) =>
            item.nameVi &&
            item.nameEn &&
            item.productCode &&
            item.contractQuantity
          // item.quantityOnUnit &&
          // item.note
        )
      ) {
        message.error("Vui lòng điền đầy đủ thông tin bảng quy cách đóng gói!");
        return;
      }

      if (!customerId) {
        message.error("Vui lòng chọn khách hàng!");
        return;
      }
      if (!marketId) {
        message.error("Vui lòng chọn thị trường!");
        return;
      }
      if (!signAt) {
        message.error("Vui lòng chọn ngày ký hợp đồng!");
        return;
      }

      const provider = providers.find((item) => item.id == providerId);

      const newContractProducts = tableContractProducts.map((item, index) => {
        return {
          ...item,
          unitId: item.unitId || item.unit?.id,
        };
      });

      const newContractPOs = tableContractPOs.map((po, poIndex) => ({
        name: po.name,
        po: po.po,
        contType: po.contType,
        totalFCL: po.totalFCL,
        quantity: po.quantity,
        etdDetail: po.etdDetail,
        etdDetailEn: po.etdDetailEn,
        address: po.address,
        amount: po.amount,
        poDetails: (po.poDetails || []).map((detail) => ({
          name: detail.name || "",
          quantity: detail.quantity,
          price: detail.price,
          contractProductIndex: detail.contractProductIndex,
          amount: detail.amount,
        })),
      }));

      const contractPos = newContractPOs.map((item) => (
        {
          ...item,
          amount: item.poDetails.reduce((sum, item) => sum + (item.amount || 0), 0),
          quantity: item.poDetails.reduce((sum, item) => sum + (item.quantity || 0), 0),
        }
      ))

      const payload = {
        unitId: unit?.id,
        contract: {
          type,
          termsOfDelivery,
          requiredDocuments,
          paymentTerm,
          contactPersonName,
          total,
          name,
          code,
          description,
          status:
            status === "create" && !isDraff ? ContractStatus.New : undefined,
          deadlineDate: "",
          procedureCode,
          isAllInspec,
          po,
          //   moneyTax: tableDetails?.moneyTax,
          //   taxPercent: tableDetails?.taxPercent,
          //   subTotal: tableDetails?.subTotal,
          //   amount: tableDetails?.amount,
          note,
          signAt: signAt ? dayjs(signAt).unix() : undefined,
          totalPrice: contractPos.reduce((a, b) => a + (b?.amount || 0), 0),
          totalQuantity: contractProducts.reduce(
            (sum, product) => sum + (product.quantity || 0),
            0
          ),
          productBy: JSON.stringify(productBy),
        },

        contractProducts: newContractProducts,
        contractPos: contractPos,

        followStaffIds: followStaffs.map((staff) => staff.id),
        inspecStaffIds,
        fileAttachIds: [] as number[],
        categoryId,
        providerId,
        provider,
        parentId,
        paymentTermId: paymentTermId || 0,
        customerId,
        marketId,
        contractContents: Array.from(
          new Map(contractContents.map(item => [item.type, item])).values()
        ),
        //  contractProducts: contractProducts.map(({ id, ...rest }) => ({
        //     ...rest,
        //     address: form.getFieldsValue().address,
        //     qualityTerms: form.getFieldsValue().qualityTerms,
        //     paymentTermId: form.getFieldsValue().paymentTermId,
        //     paymentTerm: form.getFieldValue("paymentTerm"),
        //   })),
      };

      // const productsName = payload.contractProducts.map(
      //   (item) => item.productName
      // );

      // contractProducts.map((item, index) => {
      //   if (!productsName.includes(item.productName)) {
      //     payload.contractProducts.push({
      //       ...item,
      //       packing: "",
      //       thermometer: item.thermometer,
      //       specification: item.specification,
      //       specificationEn: item.specificationEn,
      //     });

      //     productsName.push(item.productName);
      //   }
      // });

      if (isDraff) {
        const localData = {
          ...payload,

          id: contractTempSelected?.id || uuidv4(),
          name: payload?.contract?.name,
          createdAt: dayjs().unix(),
          staffId,
          followStaffs,
          inspecStaffs,
          category: {
            id: categoryId,
          },
          description,
          status: ProposalStatus.Temp,
          fileAttaches,
        };

        // PurchaseOrderLocal.createOrUpdate(localData);

        alert(
          "Dữ liệu" +
          JSON.stringify(localData, null, 2) +
          " đã được lưu vào bộ nhớ"
        );

        message.success("Lưu nháp thành công");
        // removeSearchParam();
        setVisible(false);
        onOk(isDraff);
        return;
      }
      // const isValidated = validateTable(tableDetails?.contractDetails);
      // if (!isValidated)
      //   return message.warning("Vui lòng điền các mục cần thiết trong bảng");

      await form.validateFields();
      //Upload file fileAttachs lên để lấy ids
      let fileAttachIds: number[] = [];
      let oldFileIds: number[] = [];
      if (fileAttaches?.length) {
        //
        let requestArr: any = [];

        fileAttaches.forEach((item: any) => {
          if (item.id) {
            //Nếu file cũ => dùng lại id cũ
            oldFileIds.push(item.id);
          } else {
            //File mới phải gọi api này để lấy id
            requestArr.push(
              fileAttachApi.create({
                fileAttach: {
                  name: item.name,
                  url:
                    import.meta.env.VITE_API_URL + item?.response?.data?.path ||
                    item?.url,
                  type: item.type.includes("image")
                    ? FileAttachType.Image
                    : FileAttachType.File,
                  mimetype: item.type,
                },
              })
            );
          }
        });

        const res = await Promise.all(requestArr);
        fileAttachIds = res.map((item) => item?.data?.id).concat(oldFileIds);
        payload.fileAttachIds = fileAttachIds;
      }
      try {
        console.log("payload", payload);

        const result = validateContractProductQuantities((payload.contractProducts ?? []) as any, (payload.contractPos ?? []) as any);

        if (!result.isValid) {
          showValidationError(result.exceeded);
          return;
        }

        switch (status) {
          case "create":
            try {
              setLoading(true);
              createData(payload);
            } catch (error) {
              //   setLoading(false);
              //   Modal.confirm({
              //     title: "Cảnh báo",
              //     content: (
              //       <>
              //         <p className="mb-2">
              //           Mã PO đã bị sử dụng bởi một đơn mua hàng khác. Tạo phiếu
              //           với mã PO mới?
              //         </p>
              //       </>
              //     ),
              //     okText: "Tạo với mã PO mới",
              //     cancelText: "Đóng",
              //     onOk: async () => {
              //       const { data } = await purchaseApi.code({
              //         providerId: payload.providerId,
              //       });
              //       createData({ ...payload, code: data.code });
              //     },
              //   });
            }

            break;
          case "update":
            setLoading(true);
            await updateData(payload);
            setLoading(false);
            onUpdateOk();

            break;
        }
        onOk();
      } finally {
        setLoading(false);
      }
    };

    const onUpdateOk = () => {
      // form.resetFields();
      // onOk();
      // fetchData();
      // setVisible(false);
      // setContractProduct(undefined);
      //   removeSearchParam();

      handleCancelModal();
    };

    const handleCancelModal = () => {
      form.resetFields();
      setFileList([]);
      setFileListInvoice([]);
      setContractProducts([]);
      setVisible(false);
      setStatus("create");
      setInspecStaffs([]);
      setFollowStaffs([]);
      setPackingDetails([]);
      // setContractProductsList([]);
      setContractContents([]);

      if (status == "create") {
        searchParams.delete("mode");
        searchParams.delete("type");
        searchParams.delete("proposalId");
        searchParams.delete("proposalType");

        setSearchParams([...searchParams], { replace: true });

        onCancel();
      }
    };

    // const contractDetailsGroupByName = useMemo(() => {
    //   const result: ContractDetail[] = [];

    //   contractProducts?.forEach((item) => {
    //     const name = item.productName?.toLowerCase();

    //     if (result.find((detail) => detail.productName.toLowerCase() == name)) {
    //     } else {
    //       result.push({
    //         ...item,
    //       });
    //     }
    //   });

    //   return result;
    // }, [contractProducts]);

    const handleChangeCD = (updatedDetails: Partial<ContractProduct>[]) => {
      // setContractProducts(updatedDetails as ContractProduct[]);
      setTableContractProducts(updatedDetails as ContractProduct[]);
    };

    return (
      <div>
        <Modal
          className="custom-close-icon custom-title"
          style={{ top: 15 }}
          width={5000}
          keyboard={false}
          title={
            <h1 className="text-lg md:text-2xl">
              {`${status === "update" ? "Chỉnh sửa hợp đồng" : "Tạo hợp đồng"}`}
            </h1>
          }
          closeIcon={<CloseOutlined className="text-lg" />}
          open={visible}
          onCancel={handleCancelModal}
          okText="Xác nhận"
          destroyOnClose
          footer={[
            <>
              {/* {!network.online && (
                <span className="mr-10 text-red-500">
                  Bạn đang offline, hãy chọn lưu nháp để lưu thông tin vào bộ
                  nhớ
                </span>
              )} */}

              <Button
                onClick={handleCancelModal}
                className="text-xs md:text-sm px-2 md:px-[15px]"
              >
                Đóng
              </Button>
              {/* {(status == "create" ||
                (status == "update" &&
                  details?.status == ProposalStatus.Temp)) && (
                <Button
                  onClick={() => handleCreatePurchase(true)}
                  type="primary"
                  ghost
                  icon={<ContainerFilled />}
                  className="text-xs md:text-sm px-2 md:px-[15px]"
                >
                  Lưu nháp
                </Button>
              )} */}

              <Button
                loading={loading}
                onClick={() => handleCreateContract()}
                icon={<SaveFilled />}
                type="primary"
                className="text-xs md:text-sm px-2 md:px-[15px]"
              >
                {"Lưu thông tin"}
              </Button>
            </>,
          ]}
        >
          {contextHolder}

          <Form
            className="form-margin-0"
            initialValues={{
              staffId: toJS(userStore?.info)?.id,
              isAllInspec: false,
            }}
            form={form}
            name="validateOnly"
            layout="vertical"
            autoComplete="off"
          >
            <div className="flex -mx-2  ">
              {/* Cột trái */}
              <div className="w-full md:w-1/2 px-2">
                <div className="flex items-center mb-3">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px] label-required">
                    Tên hợp đồng:
                  </label>
                  <Form.Item name="name" className="w-full !mb-0" rules={[
                    { required: true, message: "Vui lòng nhập tên hợp đồng" },
                  ]}>
                    <Input className="!h-[28px]" />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-3.5">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Số PO:
                  </label>
                  <Form.Item name="po" className="w-full !mb-0">
                    <Input
                      className="!h-[28px]"
                    // disabled={status == "update"}
                    />
                  </Form.Item>
                </div>

                <div className="flex items-center mb-3.5">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Điều kiện giao hàng:
                  </label>
                  <Form.Item name="termsOfDelivery" className="w-full !mb-0">
                    <Input className="!h-[28px]" />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-3.5">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Điều kiện thanh toán:
                  </label>
                  <Form.Item name="paymentTerm" className="w-full !mb-0">
                    <Input className="!h-[28px]" />
                  </Form.Item>
                </div>

                <div className="flex items-center mb-3.5">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Yêu cầu tài liệu
                  </label>
                  <Form.Item name="requiredDocuments" className="w-full !mb-0">
                    <Input.TextArea rows={3} />
                  </Form.Item>
                </div>

                 {/* <div className="flex items-center mb-3.5">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                   Loại hợp đồng
                  </label>
                  <Form.Item name="type" className="w-full !mb-0">
                    <Select
                      options={Object.values(ContractType).map((item) => ({
                        label: ContractTypeTrans[item],
                        value: item,
                      }))}
                      onChange={(value: ContractType) => {
                        form.setFieldValue('type', value);
                        fetchContractContentByType(value);
                      }}
                    />
                  </Form.Item>
                </div>
                <div className="flex items-center mt-3">
                  <Button
                    size="small"
                    type="primary"
                    className="font-medium  !text-xs"
                    onClick={() => {
                      if (contractContents)
                        contractContentModalRef.current?.handleUpdate(
                          contractContents
                        );
                    }}
                  >
                    Chỉnh sửa nội dung hợp đồng
                  </Button>
                </div> */}
              </div>

              {/* Cột phải */}
              <div className="w-full md:w-1/2 px-2">
                <div className="flex items-center mb-1">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px] label-required">
                    Ngày ký hợp đồng:
                  </label>
                  <Form.Item name="signAt" className="w-full !mb-0">
                    <DatePicker
                      format={"DD/MM/YYYY"}
                      className="!h-[28px] w-full"
                    />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-1">
                  <label className="!h-[28px]min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px] label-required">
                    Khách hàng:
                  </label>
                  <Form.Item
                    name="customerId"
                    className="w-full !mb-0"
                    rules={[
                      { required: true, message: "Vui lòng chọn khách hàng" },
                    ]}
                  >
                    <CustomerSelector
                      onChange={(id, option) => {
                        form.setFieldValue(
                          "marketId",
                          option?.data?.market?.id
                        );
                        setCustomerId(id);
                      }}
                    />
                  </Form.Item>
                </div>

                <div className="flex items-center mb-1">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px] label-required">
                    Thị trường:
                  </label>
                  <Form.Item
                    name="marketId"
                    className="w-full !mb-0"
                    rules={[
                      { required: true, message: "Vui lòng chọn thị trường" },
                    ]}
                  >
                    <MarketSelector />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-1">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Nơi sản xuất:
                  </label>
                  <Form.Item name="productBy" className="w-full !mb-0">
                    <ProductBySelector />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-1">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Mô tả:
                  </label>
                  <Form.Item name="description" className="w-full !mb-0">
                    <Input className="!h-[28px]" />
                  </Form.Item>
                </div>
                <div className="flex items-center mb-1">
                  <label className="min-w-[110px] md:min-w-[150px] font-semibold !text-xs md:!text-[13px]">
                    Người liên hệ
                  </label>
                  <Form.Item name="contactPersonName " className="w-full !mb-0">
                    <Input
                      className="!h-[28px]"
                      defaultValue={form.getFieldValue("contactPersonName")}
                      onChange={(e) => {
                        form.setFieldValue("contactPersonName", e.target.value);
                      }}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-1">
              <Form.Item
                name="inspecStaffIds"
                style={{ width: "100%", marginBottom: 0 }}
              >
                <div className="flex gap-2 items-center">
                  <label className="font-semibold !text-xs md:!text-[13px] label-required">
                    Thông tin người duyệt ({inspecStaffs?.length} thành viên):{" "}
                  </label>
                  <div className="gap-1 text-ellipsis w-[250px] overflow-hidden whitespace-nowrap text-blue-500">
                    {inspecStaffs &&
                      inspecStaffs?.length > 0 &&
                      inspecStaffs
                        ?.map((item) => {
                          return `${item?.name} (${item?.code})`;
                        })
                        .join(", ")}
                  </div>
                  <Popover
                    className="z-[1001]"
                    overlayStyle={{
                      width: "600px",
                    }}
                    placement="right"
                    content={
                      <>
                        <TableListStaffGroup
                          onChange={(data) => {
                            setInspecStaffs(data);
                          }}
                          type={StaffGroupAction.Inspec}
                          dataSource={inspecStaffs}
                        />
                      </>
                    }
                    trigger="click"
                  >
                    <Button size="small" className="font-medium ml-3 !text-xs">
                      Xem thêm
                    </Button>
                  </Popover>
                </div>
              </Form.Item>
              <Form.Item
                name="followStaffIds"
                style={{ width: "100%", marginBottom: 5 }}
              >
                <div className="flex gap-2 items-center">
                  <label className="font-semibold !text-[13px] label-required">
                    Thông tin người theo dõi ({followStaffs?.length} thành
                    viên):
                  </label>

                  <div className="gap-1 text-ellipsis w-[250px] overflow-hidden whitespace-nowrap text-blue-500">
                    {followStaffs &&
                      followStaffs.length > 0 &&
                      followStaffs
                        ?.map((item) => {
                          return `${item?.name} (${item?.code})`;
                        })
                        .join(", ")}
                  </div>

                  <Popover
                    className=""
                    overlayStyle={{
                      width: "600px",
                    }}
                    placement="right"
                    content={
                      <>
                        <TableListStaffGroup
                          onChange={(data) => {
                            setFollowStaffs(data);
                          }}
                          type={StaffGroupAction.Follow}
                          dataSource={followStaffs}
                        />
                      </>
                    }
                    trigger="click"
                  >
                    <Button size="small" className="font-medium ml-3 !text-xs">
                      Xem thêm
                    </Button>
                  </Popover>
                </div>
              </Form.Item>

              <div className="flex items-center">
                <label
                  htmlFor=""
                  className="min-w-[100px] md:min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Đóng hợp đồng:
                </label>
                <Form.Item
                  name="isAllInspec"
                  className="w-full md:w-1/2 !mb-0 !overflow-hidden"
                >
                  <Radio.Group>
                    <Radio value={true}>Tất cả người duyệt đều duyệt</Radio>
                    <Radio value={false}>1 người duyệt</Radio>
                  </Radio.Group>
                </Form.Item>
              </div>

              <div>
                <div className="text-[16px] font-semibold">
                  Danh sách hàng hóa:
                </div>
                <div className="">
                  <TableContractProductNew
                    customerId={customerId}
                    onAddRow={handleAddPacking}
                    status={status}
                    contractDetails={tableContractProducts}
                    handleChangeContractDetails={(
                      updatedDetails: Partial<ContractProduct>[]
                    ) => handleChangeCD(updatedDetails)}
                  />
                </div>
                <div className="mt-5 text-[16px] font-semibold">
                  Danh sách PO:
                </div>
                <TableContractPO
                  onAddRow={handleAddContractProduct}
                  contractProducts={contractProducts as any}
                  // status={"create"}
                  ref={tableContractProductRef}
                  // hiddenPrivateInfo={false}
                  onChangeContractProduct={handleChangeContractPO}
                  onDeleteContractProduct={handleDeleteContractPO}
                />
                {tableContractPOs?.map((product, index) => (
                  <div
                    key={index}
                    style={{
                      marginTop: 20,
                    }}
                  >
                    <div className="text-[16px] font-semibold">
                      Hàng hóa của PO: {product?.po}
                    </div>
                    <TablePODetail
                      status="create"
                      ContractPO={product}
                      contractDetails={product?.poDetails ?? []
                      }
                      listProducts={tableContractProducts}
                      handleChangeContractDetails={(
                        updatedDetails: Partial<PODetail>[]
                      ) => handleChangeContractDetails(index, updatedDetails)}
                    />
                  </div>
                ))}
              </div>
              <Col span={24}>
                <Divider className="my-0 mb-4"></Divider>
                {/* Quy cách đóng gói */}
                <section>
                  <ContractContentModal
                    ref={contractContentModalRef}
                    onClose={() => { }}
                    onSubmitOk={(data) => {
                      setContractContents(data);
                    }}
                  ></ContractContentModal>
                </section>
              </Col>
            </div>
          </Form>
        </Modal>
      </div>
    );
  }
);

export default observer(ModalCreateContract);
