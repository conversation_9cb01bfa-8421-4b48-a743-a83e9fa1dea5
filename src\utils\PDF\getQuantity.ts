import { PODetail } from "@/types/contract";

interface PO {
    poDetails: PODetail[];
}

const getQuantityOfEachItemFromPO = (pos:PO[])=>{
    const quantityMap: Record<string, number> = {};
    
    pos.forEach((po: PO) => {
        po.poDetails.forEach((detail) => {
        if (quantityMap[detail.name]) {
            quantityMap[detail.name] += detail.quantity;
        } else {
            quantityMap[detail.name] = detail.quantity;
        }
        });
    });
    
    return quantityMap;
}

export default getQuantityOfEachItemFromPO