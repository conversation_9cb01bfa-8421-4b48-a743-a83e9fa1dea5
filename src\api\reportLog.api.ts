import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const reportLogApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/reportLog",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/reportLog",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/reportLog/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/reportLog/${id}`,
      method: "delete",
    }),
};
