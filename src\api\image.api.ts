import { $url } from "@/utils/url";
import { getToken } from "@/utils/auth";
import { request } from "@/utils/request";
import { settings } from "../../settings";

export const staffUploadApi =
  import.meta.env.VITE_API_URL + "/v1/admin/staff/upload/image";

export const uploadImage = async (file: File, url?: string) => {
  const formData = new FormData();
  formData.append("file", file);
  const { data } = await request({
    url,
    data: formData,
    headers: {
      token: getToken() || "",
      version: settings.version,
      role: "admin",
    },
    method: "POST",
  });
  return $url(data?.path);
};
