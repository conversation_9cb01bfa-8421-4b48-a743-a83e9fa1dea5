import { proposalApi } from "@/api/proposal.api";
import { useNetworkContext } from "@/context/NetworkContext";
import { userStore } from "@/store/userStore";
import { ModalStatus } from "@/types/modal";
import {
  Proposal,
  ProposalDetail,
  ProposalStatus,
  ProposalType,
} from "@/types/proposal";
import { ContainerFilled, PlusOutlined, SaveFilled } from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  Modal,
  Row,
  Space,
  Spin,
  Steps,
  Typography,
  message,
} from "antd";
import { useForm } from "antd/lib/form/Form";
import dayjs from "dayjs";
import { debounce } from "lodash";
import React, { useImperativeHandle, useState } from "react";
import { flushSync } from "react-dom";
import { v4 } from "uuid";
import PackageDetailTable, { DetailInputProps } from "./PackageDetailTable";
import PackageMainInfo from "./PackageMainInfo";
import { ProposalLocal } from "@/utils/proposalLocal";
import { Contract } from "@/types/contract";
import { contractApi } from "@/api/contract.api";
import { Staff } from "@/types/staff";
import { getImageIdsV2 } from "@/views/ProductionManager/Annunciator/components/ReportDetail";
import ImportPackageDetail from "@/views/ProductionManager/Packaging/components/ImportPackageDetail";
import { checkIsThung } from "@/views/ProductionManager/ProductionList/components/PrintProductionOrder";

export interface PackageModalRef {
  handleOpen: (
    status: ModalStatus,
    record?: Proposal,
    contractId?: number,
    contractDetailId?: number
  ) => void;
  close: () => void;
}

export interface BaoBiUnit {
  name: string;
  isMain?: boolean;
}

// export const getBaoBiUnits = async () => {
//   const baiBiConfig = await proposalConfigApi
//     .findByType({ type: ProposalConfigType.BaoBiProductDefault })
//     .then(({ data }) => data as ProposalConfig);
//   const parsedValues = JSON.parse(baiBiConfig.value) as { name: string }[];
//   return parsedValues.map(({ name }) => {
//     return {
//       name,
//       isMain: checkIsThung(name),
//     };
//   });
// };

export const packageDetailUnitNames: BaoBiUnit[] = [
  { name: "Thùng", isMain: true },
  { name: "Túi" },
  { name: "Hộp" },
  { name: "Khay" },
  { name: "Màng seal" },
  { name: "Decal thùng" },
  { name: "Túsi sốt" },
];

export const getInitDetailData = (
  proposal?: Proposal,
  unitNames?: BaoBiUnit[],
  isDraff?: boolean
): Partial<ProposalDetail>[] => {
  if (proposal?.proposalDetails) {
    return proposal.proposalDetails.map(
      ({
        id,
        isMain,
        productCode,
        contractQuantity,
        name,
        quantityOnUnit,
        missPercent,
        missQuantity,
        note,
      }) => ({
        id: isDraff ? v4() : id,
        isMain,
        quantityOnUnit,
        productCode,
        contractQuantity,
        nameVi: name,
        missPercent,
        missQuantity,
        note,
      })
    );
  } else {
    return (
      unitNames?.map((v) => ({
        id: v4(),
        isMain: v.isMain,
        productCode: "",
        contractQuantity: 0,
        quantityOnUnit: 0,
        nameVi: v.name,
        missPercent: 0,
        missQuantity: 0,
        note: "",
      })) || []
    );
  }
};

export const findOneContract = async (id: number) => {
  return contractApi.findOne(id).then(({ data }) => data as Contract);
};
export const findOneProposal = async (id: number) => {
  return proposalApi.findOne(id).then(({ data }) => data as Proposal);
};
export const PackageModal = React.forwardRef(
  ({ onSubmitOk }: { onSubmitOk: (isDraff?: boolean) => void }, ref) => {
    const { isOnline } = useNetworkContext();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [form] = useForm<any>();
    const [visibleModal, setVisibleModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [modalLoading, setModalLoading] = useState(false);
    const [selectedContract, setSelectedContract] = useState<Contract>();
    const [selectedProposal, setSelectedProposal] = useState<Proposal>();

    const [details, setDetails] = useState<ProposalDetail[]>([]);
    const quantityOnUnit = Form.useWatch("quantityOnUnit", form);

    useImperativeHandle(
      ref,
      () => {
        return {
          async handleOpen(
            status: ModalStatus,
            record?: Proposal,
            contractId?: number,
            contractDetailId?: number
          ) {
            debugger;
            setModalLoading(true);
            try {
              setVisibleModal(true);

              // Xử lý trường hợp tạo mới từ CreatePackageProposal
              if (status === "copy" || status == "create") {
                if (
                  record &&
                  record.contractCode &&
                  record.productName &&
                  !record.id
                ) {
                  form.setFieldsValue({
                    name: record.name || record.contractCode,
                    contractCode: record.contractCode,
                    productName: record.productName,
                    customerId: record.customer?.id,
                    customer: record.customer,
                    factoryCode: record.factoryCode,
                    createdStaff: userStore.info,
                    createdStaffId: userStore.info.id,
                  });
                }

                setStatus(status);

                if (contractId) {
                  const data = await findOneContract(contractId);
                  let contractDetail = data?.contractDetails?.[0];
                  const detail = data.contractProducts?.find(
                    (item) => item.id === contractDetailId
                  );

                  if (
                    !data?.contractDetails ||
                    data?.contractDetails.length === 0
                  ) {
                    setDetails([
                      {
                        nameVi: "Thùng",
                        isMain: false,
                        productCode: "",
                        missPercent: 0,
                        missQuantity: 0,
                        quantityOnUnit: detail?.quantityUnit ?? 0,
                        contractQuantity: 0,
                        date: "",
                        orderNo: "",
                        note: "",
                        usageNote: "",
                        quantity: detail?.quantity ?? 0,
                        price: 0,
                        totalPrice: 0,
                        moneyTax: 0,
                        size: "",
                        package: "",
                        dueAt: 0,
                        length: 0,
                        width: 0,
                        height: 0,
                      } as any,
                    ]);
                  }

                  const contractProduct = data.contractProducts.find(
                    (p) => p.id === contractDetailId
                  );

                  if (contractDetailId && Array.isArray(data.contractDetails)) {
                    contractDetail =
                      data.contractDetails.find(
                        (detail) => detail.id === contractDetailId
                      ) || contractDetail;

                    if (status == "copy" && contractDetail) {
                      setDetails(
                        //@ts-ignore
                        (contractDetail.contractPackingDetails || []).map(
                          (d) => ({
                            ...d,
                            quantity: contractDetail.quantityFinal,
                            quantityOnUnit: contractDetail.quantityUnit,
                          })
                        ) as ProposalDetail[]
                      );
                    }
                  }
                  setSelectedContract(data);

                  form.setFieldsValue({
                    ...data,
                    customerId: data?.customer?.id,
                    createdStaff: data?.createdStaff || userStore.info,
                    createdStaffId: data?.createdStaff?.id || userStore.info.id,
                    productName: contractProduct?.productName,
                    contractCode: data?.code,
                    quantity: contractDetail?.quantityFinal,
                    quantityUnit: contractDetail?.quantityUnit,
                    contractDetailId: contractDetail?.id,
                  });
                } else {
                  flushSync(() => {
                    const details = getInitDetailData(
                      record,
                      packageDetailUnitNames,
                      record?.status == ProposalStatus.Temp
                    );

                    setDetails(details as ProposalDetail[]);
                  });
                }

                setModalLoading(false);

                return;
              } else if (record && status == "update") {
                const data = await findOneProposal(record.id);
                setSelectedProposal(data);
                setDetails(
                  (data?.proposalDetails as ProposalDetail[]).map((item) => ({
                    ...item,
                    nameVi: item.name,
                  }))
                );
                form.setFieldsValue({
                  ...data,
                  customerId: data?.customer?.id,
                  createdStaff: data?.createdStaff || userStore.info,
                  createdStaffId: data?.createdStaff?.id || userStore.info.id,
                });
              }

              setStatus(status);
            } catch (error) {
            } finally {
              setModalLoading(false);
            }
          },
          close: onClose,
        };
      },
      []
    );

    const validateDetail = (record: ProposalDetail[]) => {
      for (const item of record) {
        if (!item.nameVi) {
          message.error("Vui lòng nhập tên loại bao bì");
          throw "";
        }
        // if (!item.contractQuantity) {
        //   message.error("Vui lòng nhập số lượng thùng theo hợp đồng");
        //   throw "";
        // }
        if (!item.productCode) {
          message.error("Vui lòng nhập mã bao bì");
          throw "";
        }
      }
    };

    const getFormData = async (isDraff = false) => {
      const {
        id,
        followStaffs,
        createdStaffId,
        createdStaff,
        inspecStaffs,
        fileAttaches,
        customer,
        customerId,
        quantity,
        quantityOnUnit,
        contractDetailId,
        ...data
      } = form.getFieldsValue();

      console.log("details data: ", details);

      // Map lại details để đổi quantityOnUnit -> quantityUnit
      const mappedDetails =
        details?.map?.((detail) => ({
          ...detail,
          name: detail.nameVi,
          quantityUnit: detail.quantityOnUnit,
          id:
            typeof detail.id == "string" || status == "copy"
              ? undefined
              : detail.id,
        })) || [];

      const payload = {
        proposal: {
          ...data,
          dateAt:
            status != "update"
              ? dayjs().startOf("day").add(30, "day").unix()
              : undefined,
        },
        followStaffIds: followStaffs?.map?.((f: Staff) => f.id) || [],
        inspecStaffIds: inspecStaffs?.map?.((i: Staff) => i.id) || [],
        staffId: createdStaffId,
        details: mappedDetails,
        id,
        customerId,
        contractDetailId,
      };

      if (status == "copy") {
        //Tạo mới file attach nếu copy
        fileAttaches?.forEach?.((file: any) => {
          Object.assign(file, {
            id: "copy",
          });
        });
      }

      //Trả data đối với lưu nháp
      if (isDraff) {
        return {
          ...payload,
          ...payload.proposal,
          id: payload.id || v4(),
          name: payload.proposal.name,
          createdAt: dayjs().unix(),
          followStaffs,
          inspecStaffs,
          description: payload.proposal.description,
          status: ProposalStatus.Temp,
          fileAttaches,
          proposalDetails: payload.details,
        };
      }

      //@ts-ignore
      const fileAttachIds = await getImageIdsV2(fileAttaches);

      return {
        ...payload,
        contractId: selectedContract?.id,
        fileAttachIds,
      };
    };

    const handleSaveDraff = async () => {
      setLoading(true);
      try {
        const data = await getFormData(true);

        ProposalLocal.createOrUpdate(data);
        message.success("Lưu nháp thành công");
        onSubmitOk(true);
        setVisibleModal(false);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };

    const handleSubmitForm = async () => {
      await form.validateFields();
      setLoading(true);
      try {
        const { id, ...formData } = await getFormData();
        validateDetail(formData?.details as ProposalDetail[]);
        switch (status) {
          case "update":
            await proposalApi.update(id as number, formData);
            break;
          default:
            await proposalApi.create(formData);
            break;
        }
        message.success("Thao tác thành công");
        onSubmitOk();
        setVisibleModal(false);
      } finally {
        setLoading(false);
      }
    };

    //Thùng
    // const mainDetail = useMemo(() => {
    //   return details.find((detail) => detail.isMain);
    // }, [details]);

    const debounceChangeDetailData = debounce(
      (value, fieldName, record) =>
        handleChangeDetailData(value, fieldName, record),
      200
    );

    const handleChangeDetailData: DetailInputProps<ProposalDetail>["onChange"] =
      (value, fieldName, record) => {
        const updatedDetails = [...details];
        const index = updatedDetails.findIndex((item) => item.id === record.id);

        Object.assign(record, {
          [fieldName]: value,
        });

        if (fieldName === "quantityOnUnit" && index > 0) {
          const base = updatedDetails[0];
          const baseQuantity = Number(base.contractQuantity || 0);
          const unitPerBase = Number(value || 0);

          record.contractQuantity = baseQuantity * unitPerBase;
        }

        setDetails(updatedDetails);
      };

    // useEffect(() => {
    //   if (mainDetail) {
    //     handleChangeDetailData(
    //       mainDetail?.contractQuantity || 0,
    //       "contractQuantity",
    //       mainDetail
    //     );
    //   }
    //   return () => {};
    // }, [quantityOnUnit]);

    const handleOnAddDetail = () => {
      setDetails((details) => {
        const newDetail: ProposalDetail = {
          id: Date.now(),
          nameVi: "",
          nameEn: "",
          contractQuantity: 0,
          note: "",
          productCode: "",
          createdAt: dayjs().unix(),
          updatedAt: dayjs().unix(),
          isDeleted: false,
          quantityOnUnit: 0,
          // Add default values for all other required ProposalDetail properties here
          // Example:
          // someOtherField: defaultValue,
        } as ProposalDetail;

        return [...details, newDetail];
      });
    };

    const handleOnDeleteDetail = (id: ProposalDetail["id"]) => {
      setDetails((details) => {
        const findIndex = details.findIndex((v) => v.id == id);
        if (findIndex > -1) {
          details.splice(findIndex, 1);
        }
        return [...details];
      });
    };

    const onClose = () => {
      setVisibleModal(false);
    };

    return (
      <Modal
        width={1200}
        afterClose={() => {
          form.resetFields();
          setSelectedContract(undefined);
          setDetails([]);
        }}
        style={{
          top: 20,
        }}
        footer={
          <>
            <Button
              onClick={onClose}
              className="text-xs md:text-sm px-2 md:px-[15px]"
            >
              Đóng
            </Button>

            {/* {(status == "create" ||
              (status == "update" &&
                (selectedProposal?.status == ProposalStatus.Temp ||
                  !selectedProposal?.status))) && (

            )} */}
            {(status == "create" || status == "copy") && (
              <Button
                onClick={handleSaveDraff}
                type="primary"
                ghost
                icon={<ContainerFilled />}
                className="text-xs md:text-sm px-2 md:px-[15px]"
              >
                Lưu nháp
              </Button>
            )}

            <Button
              loading={loading}
              onClick={handleSubmitForm}
              icon={<SaveFilled />}
              type="primary"
              className="text-xs md:text-sm px-2 md:px-[15px]"
            >
              {"Lưu thông tin"}
            </Button>
          </>
        }
        onCancel={() => setVisibleModal(false)}
        open={visibleModal}
        title={
          status == "create" || status == "copy"
            ? "Thêm đề nghị bao bì"
            : "Cập nhật đề nghị bao bì"
        }
        confirmLoading={loading}
        onOk={handleSubmitForm}
      >
        <Spin spinning={modalLoading}>
          <Steps></Steps>

          <Form form={form} layout="vertical">
            <Form.Item name={"id"} hidden>
              <Input></Input>
            </Form.Item>
            <Form.Item name={"fileAttaches"} hidden>
              <Input></Input>
            </Form.Item>
            <Form.Item name={"createdStaff"} hidden>
              <Input></Input>
            </Form.Item>
            <Form.Item
              initialValue={ProposalType.Contract}
              name={"type"}
              hidden
            >
              <Input></Input>
            </Form.Item>

            <Row gutter={[24, 12]}>
              <PackageMainInfo
                contract={selectedContract}
                status={status}
                form={form}
              ></PackageMainInfo>

              <Col span={24}>
                <Divider className="my-0 mb-4"></Divider>
                {/* Quy cách đóng gói */}
                <section>
                  <Space align="center">
                    <Typography.Title className="mt-0" level={4}>
                      Quy cách đóng gói
                    </Typography.Title>
                    <Button
                      onClick={handleOnAddDetail}
                      icon={<PlusOutlined />}
                      size="small"
                      type="primary"
                    >
                      Thêm
                    </Button>
                    {/* <ImportPackageDetail
                      onSuccess={(data) => {
                        console.log(data);

                        const contractQuantity = data.find((value) =>
                          checkIsThung(value.name)
                        );
                        if (contractQuantity) {
                          setDetails((prev) => {
                            return data.map((v) => ({
                              ...v,
                              isMain: checkIsThung(v.name),
                              missQuantity:
                                v.contractQuantity +
                                (v.contractQuantity * (v.missPercent || 1)) /
                                  100,
                            }));
                          });
                        } else {
                          message.error(
                            'Bạn thiếu dữ liệu "Thùng" hoặc sai tên, vui lòng kiểm tra lại file.'
                          );
                        }
                      }}
                    /> */}
                  </Space>

                  <PackageDetailTable
                    details={details}
                    selectedContract={selectedContract}
                    onChange={debounceChangeDetailData}
                    onDelete={handleOnDeleteDetail}
                    // readonly
                  ></PackageDetailTable>
                </section>
              </Col>
              {/* {selectedProposal && status == "view" && (
                <Col span={24}>
                  <CommentSection
                    type={CommentSectionType.proposal}
                    dataSelected={selectedProposal}
                  />
                </Col>
              )} */}
            </Row>
          </Form>
        </Spin>
      </Modal>
    );
  }
);
