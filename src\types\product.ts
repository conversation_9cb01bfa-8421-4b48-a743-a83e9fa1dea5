import { InventoryDetail } from "./inventory";
import { OrderDetail } from "./order";
import { ProductUnit } from "./productUnit";
import { PromotionCampaignDetail } from "./promotion";
import { PurchaseOrderDetail } from "./purchaseOrder";
import { Staff } from "./staff";
import { Stock } from "./stock";
import { Store } from "./store";
import { Unit } from "./unit";

// export interface Product {
//   id: number;
//   createdAt: number;
//   updatedAt: number;
//   isDeleted: boolean;
//   isBlocked: boolean;
//   code: string;
//   syncId: number;
//   businessId: number;
//   name: string;
//   privateName: string; //tên nội bộ
//   imageUrl: string;
//   importPrice: number; //giá nhập kho
//   suggestImportPrice: number; //giá nhập đề xuất
//   price: number;
//   unit: Unit;
//   remain: number; //
//   shipping: number; //
//   holding: number; //
//   available: number; // Tồn kho có thể bán
//   note: string;
//   productCategory: ProductCategory;
//   orderDetails: OrderDetail[];
// }

export interface ProductForm extends Product {
  productCategoryId: number;
  ortherImages: any[];
  unitId: number;
}

export interface ProductSampleCheck {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  quantity: number;
  productSample: ProductSample;
  images: any[];
  createdStaff: Staff;
}

export interface ProductSample {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  quantity: number; //sl ban đầu
  product: Product;
  store: Store;
  images: any[];
  createdStaff: Staff;
  productSampleChecks: ProductSampleCheck[];
  sku: string;
}

export interface ProductCategory {
  id: number;
  createdAt: number;
  updatedAt: number;
  deletedBy: string;
  isDeleted: boolean;
  deletedAt: number;
  code: string;
  name: string;
  nameEn: string; //tên tiếng anh
  imageUrl: string;
  level: number;
  slug: string;
  refPoint: number; //thưởng điểm hoa hồng khi chia sẻ mua hàng, tính theo %
  isVisible: boolean;
  metaKeyword: string;
  visibleOnMenu: boolean; //hiển thị trên menu
  isHighlight: boolean; //true: cate nổi bật, hiển thị dạng list + product ở mobile
  position: number;
  description: string;
  children: ProductCategory[];
  parent: ProductCategory;
  products: Product[];
}

export enum Module {
  Common = "COMMON",
  Freeze = "FREEZE",
}
export interface Product {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  module: Module;
  name: string;
  imageUrl: string;
  note: string;
  minQuantity: number; //tồn kho tối thiểu
  isBlocked: boolean;
  //custom
  productCategory: ProductCategory;
  stock: Stock;
  unit: Unit;
  purchaseOrderDetails: PurchaseOrderDetail[];
  inventoryDetails: InventoryDetail[];
  available: number; // Tồn kho có thể bán
  contractProduct?: any;
}
