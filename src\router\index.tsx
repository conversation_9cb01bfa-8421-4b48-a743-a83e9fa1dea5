import React from "react";
const PurchasePage = React.lazy(
  () => import("@/views/PurchasePage/PurchasePage")
);
const SuggestionPage = React.lazy(
  () => import("@/views/SuggestionPage/SuggestionPage")
);

const WareHousePage = React.lazy(
  () => import("@/views/WareHouse/WareHousePage")
);
const HomePage = React.lazy(() => import("@/views/HomePage"));
const ProviderPage = React.lazy(
  () => import("@/views/ProviderPage/ProviderPage")
);
const ProfilePage = React.lazy(() => import("@/views/Profile/ProfilePage"));
const RolePage = React.lazy(() => import("@/views/Role/RolePage"));
const ConfigPage = React.lazy(() => import("@/views/Config/ConfigPage"));
const TestCanvasPage = React.lazy(() => import("@/views/Test/TestCanvasPage"));

import {
  FaClipboardList,
  FaDoorClosed,
  FaFileContract,
  FaStore,
} from "react-icons/fa6";
import { GiAutoRepair } from "react-icons/gi";

const DepartmentPage = React.lazy(
  () => import("@/views/Department/Department")
);

import { PiSnowflakeDuotone } from "react-icons/pi";

import {
  CarOutlined,
  ProductOutlined,
  SearchOutlined,
  SettingFilled,
} from "@ant-design/icons";

import { FaBusinessTime, FaCalendarWeek } from "react-icons/fa";

import {
  MdLockClock,
  MdProductionQuantityLimits,
  MdReportProblem,
} from "react-icons/md";
export enum PermissionName {
  editProcedureCode = "edit-procedure-code",

  createJob = "create-job",
  editJob = "edit-job",
  editSalaryCoefficient = "edit-salary-coefficient",

  startCheckIn = "start-check-in",
  checkIn = "/checkIn",
  inspector = "inspector",
  checkInAdmin = "checkIn-Admin",
  bep = "bep",
  endCheckIn = "end-check-in",

  payrollSalary = "/payroll-salary",
  searchPayrollSalary = "/search_payroll-salary",

  QLSXReport = "/report",
  QLSXAnnunciator = "/annunciator",
  QLSXFixer = "/fixer",
  QLSXInspector = "/inspector",
  dateConfig = "/dateConfig",
  listSanXuat = "/production-list",

  viewAllProductionConditionReport = "view_all_production_condition_report",
  deleteProductionConditionReport = "delete_production_condition_report",
  viewAllProductionListContent = "view_all_production_list_content",

  taoBaoBi = "tao_bao_bi",
  xemTatCaBaoBi = "xem_tat_ca_bao_bi",
  xemTatCaListSanXuat = "xem_tat_ca_list_san_xuat",
  xemTatCaQuyTrinhDonGian = "xem_tat_ca_quy_trinh_don_gian",
  xemTatCaQuyTrinhChiTiet = "xem_tat_ca_quy_trinh_chi_tiet",
  xemTatCaKhachHang = "xem_tat_ca_khach_hang",
  capNhatTatCaKhachHang = "cap_nhat_tat_ca_khach_hang",
  capNhatKhachHangCuaMinh = "cap_nhat_khach_hang_cua_minh",
  xoaKhachHang = "xoa_khach_hang",
  xemTatCaBaoBiDuocTag = "xem_tat_ca_bao_bi_duoc_tag",
  xemTatCaListSanXuatDuocTag = "xem_tat_ca_list_san_xuat_duoc_tag",
  xemTatCaQuyTrinhDonGianDuocTag = "xem_tat_ca_quy_trinh_don_gian_duoc_tag",
  xemTatCaQuyTrinhChiTietDuocTag = "xem_tat_ca_quy_trinh_chi_tiet_duoc_tag",

  //customer
  viewAllCustomer = "view_all_customer",
  //hop dong
  viewAllContract = "view_all_contract",
  //cau hinh hop dong
  updateContractConfig = "update_contract_config",

  //chi xem hop dong
  viewOnlyContract = "view_only_contract",

  //chi sua thong tin thanh toan hop dong
  paymentUpdate = "payment_update",

  //cap nhat hop dong
  updateContract = "update_contract",
  //tao hop dong
  createContract = "create_contract",

  //xoa hop dong
  deleteContract = "delete_contract",
}

import { MdOutlineContentPasteSearch } from "react-icons/md";

const permissionArr = Object.values(PermissionName);

export const checkInPermissionName = permissionArr
  .map((v) => v)
  .slice(
    permissionArr.indexOf(PermissionName.startCheckIn),
    permissionArr.indexOf(PermissionName.endCheckIn)
  );

import { DepartmentType } from "@/types/department";
import { RouteGroup } from "@/types/route";
import CompanyPage from "@/views/Company/CompanyPage";
import PayrollConfigPage from "@/views/Config/PayrollConfigPage";
import DepartmentPayrollPage from "@/views/Department/DepartmentPayroll";
import { AllowanceGroupPage } from "@/views/EmployeeGroup/AllowanceGroupPage";
import { Payroll } from "@/views/Payroll/Payroll";
import SearchPayrollPage from "@/views/SearchPayrollPage";
import { AiOutlineGlobal, AiTwotoneSetting } from "react-icons/ai";
import { FaIdCardAlt, FaStoreAlt } from "react-icons/fa";
import {
  FaBox,
  FaBuilding,
  FaCartShopping,
  FaFileLines,
  FaKey,
  FaMoneyBill,
  FaTruck,
} from "react-icons/fa6";
import { HiUserGroup } from "react-icons/hi2";
import { RouteObject } from "react-router-dom";
import { AdminLayout } from "../layouts/AdminLayout";
import { NotFoundPage } from "../views/404/NotFoundPage";
import LoginPage from "../views/Login/LoginPage";
import { ProductPage } from "../views/Product";
import { StaffPage } from "../views/Staff/StaffPage";
import BonusWrapperPage from "@/views/Maternity/BonusWrapperPage";
import { AreaPage } from "@/views/ProductionManager/Area/AreaPage";
import ReportPage from "@/views/ProductionManager/Report/ReportPage";
import ChooseArea from "@/views/ProductionManager/Annunciator/ChooseArea";
import Declare from "@/views/ProductionManager/Annunciator/Declare";
import RepairPage from "@/views/ProductionManager/Annunciator/Repair";
import CensorshipPage from "@/views/ProductionManager/Annunciator/Censorship";
import ConfigProductManagementPage from "@/views/ConfigProductManagement/ConfigProductManagementPage";
import { CustomerPage } from "@/views/ProductionManager/Customer/CustomerPage";
import { IoPeopleOutline, IoStorefrontOutline } from "react-icons/io5";
import { NationPage } from "@/views/ProductionManager/Nation/NationPage";
import { PackagePage as PackageContractPage } from "@/views/ContractPacking/PackingPage";

import { GoPackage } from "react-icons/go";
import NetworkProvider from "@/context/NetworkContext";
import FixerChooseAreaWrapper from "@/views/ProductionManager/Fixer/ChooseAreaWrapper";
import CheckerChooseAreaWrapper from "@/views/ProductionManager/Checker/ChooseAreaWrapper";
import { AppSummaryKey } from "@/context/AppContext";
import { ReportStatus } from "@/types/report";
import { ProductionListPage } from "@/views/ProductionManager/ProductionList/ProductionListPage";
import { RiFlowChart } from "react-icons/ri";
import { BasicProcedurePage } from "@/views/ProductionManager/BasicProcedure/BasicProcedurePage";
import { DetailsProcedurePage } from "@/views/ProductionManager/DetailsProcedure/DetailsProcedurePage";
import ImportExcelModule from "@/modules/excel/ImportExcelModule";
import { SessionDevicePage } from "@/views/SessionDevice/SessionDevicePage";
import QuotePage from "@/views/QuotePage/QuotePage";
import { MarketPage } from "@/views/Market/MarketPage";
import ContractMainPage from "@/views/Contract/ContractMainPage";
import { ColdStorageProductPage } from "@/views/ColdStorageProduct";
import ColdStorageWareHouse from "@/views/ColdStorageWareHouse/ColdStorageWareHouse";
import { ContractDashboardPage } from "@/views/ContractDashboard/ContractDashboardPage";
import { LogisticPage } from "@/views/Logistic/LogisticPage";
import ContractPage from "@/views/ContractPage/ContractPage";

export interface RouterCount {
  link: string[];
  role?: PermissionName;
}

export interface Route extends RouteObject {
  title?: string;
  children?: Route[];
  icon?: React.ReactNode;
  breadcrumb?: string;
  isAccess?: boolean;
  hidden?: boolean;
  count?: RouterCount;
  name?: string;
  noRole?: boolean;
  aliasPath?: string;
  bg?: string;
  checkIsDev?: boolean;
  isDisabled?: boolean;
  isPublic?: boolean; //Ẩn ở menu chọn quyền và luôn hiển thị với tất cả user
  description?: string;
  group?: RouteGroup;
  onClick?: () => void;
  isParent?: boolean;
}

const routersForStaging: Route[] = [
  //Payroll
  {
    title: "Nhà máy",
    icon: <FaBuilding />,
    path: "/company",
    name: "/company",
    breadcrumb: "Nhà máy",
    element: <CompanyPage title="Nhà máy" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.payroll,
  },
  {
    title: "Quản lý giờ công",
    icon: <FaBusinessTime />,
    path: "/payroll",
    name: "/payroll",
    breadcrumb: "Quản lý giờ công",
    element: <Payroll title="Quản lý giờ công" />,
    bg: "from-[#ef540b] to-[#eeaa1b]",
    group: RouteGroup.payroll,
  },
  {
    title: "Quản lý lương",
    icon: <FaMoneyBill />,
    path: PermissionName.payrollSalary,
    name: PermissionName.payrollSalary,
    breadcrumb: "Quản lý lương",
    element: <Payroll title="Quản lý lương" />,
    bg: "from-[#4ac29a] to-[#bdfff3]",
    group: RouteGroup.payroll,
  },
  {
    title: "Nhóm trợ cấp",
    icon: <FaIdCardAlt />,
    path: "/allowance-group",
    name: "/allowance-group",
    breadcrumb: "Nhóm trợ cấp",
    element: <AllowanceGroupPage title="Nhóm trợ cấp" />,
    bg: "from-[#ef540b] to-[#eeaa1b]",
    group: RouteGroup.payroll,
  },
  {
    title: "Phòng ban (TL)",
    icon: <HiUserGroup />,
    path: "/payroll-department",
    name: "/payroll-department",
    breadcrumb: "Phòng ban (TL)",
    element: (
      <DepartmentPayrollPage
        type={DepartmentType.Payroll}
        title="Phòng ban (TL)"
      />
    ),
    bg: "from-[#ef540b] to-[#eeaa1b]",
    group: RouteGroup.payroll,
  },
  {
    title: "Thai sản, BHXH",
    icon: <PiSnowflakeDuotone />,
    path: "/maternity",
    name: "/maternity",
    breadcrumb: "Thai sản, BHXH",
    element: <BonusWrapperPage title="Thai sản, BHXH" />,
    bg: "from-[#044df0] to-[#00b0ef]",
    group: RouteGroup.payroll,
  },
  {
    title: "Cấu hình",
    breadcrumb: "Cấu hình",
    icon: <AiTwotoneSetting />,
    path: "/config",
    name: "/config",
    element: <PayrollConfigPage title="Cấu hình" />,
    bg: "from-[#044df0] to-[#00b0ef]",
    group: RouteGroup.payroll,
    children: [
      {
        title: "Chỉnh sửa công đoạn",
        path: PermissionName.editJob,
        name: PermissionName.editJob,
      },

      {
        title: "Thêm mới công đoạn",
        path: PermissionName.createJob,
        name: PermissionName.createJob,
      },
      {
        title: "Chỉnh sửa hệ số tính lương đánh giá",
        path: PermissionName.editSalaryCoefficient,
        name: PermissionName.editSalaryCoefficient,
      },
    ],
  },
  {
    title: "Tra cứu giờ công",
    breadcrumb: "Tra cứu giờ công",
    icon: <SearchOutlined />,
    path: "/search_payroll",
    name: "/search_payroll",
    element: <SearchPayrollPage title="Tra cứu giờ công" />,
    bg: "from-[#111111] to-[#666]",
    group: RouteGroup.payroll,
  },
  {
    title: "Tra cứu lương",
    breadcrumb: "Tra cứu lương",
    icon: <SearchOutlined />,
    path: PermissionName.searchPayrollSalary,
    name: PermissionName.searchPayrollSalary,
    element: <SearchPayrollPage title="Tra cứu lương" />,
    bg: "from-[#5f2c82] to-[#49a09d]",
    group: RouteGroup.payroll,
  },

  //Payroll
];

export const adminRoutes: Route[] = [
  {
    title: "Trang chủ",
    icon: <FaIdCardAlt />,
    path: "/home",
    name: "/home",
    breadcrumb: "Trang chủ",
    element: <HomePage title="Trang chủ" />,
    hidden: true,
    isPublic: true,
  },
  {
    title: "Đề nghị",
    icon: <FaFileLines />,
    path: "/suggestion",
    name: "/suggestion",
    breadcrumb: "Đề nghị",
    bg: "from-cyan-500 to-blue-500",
    element: <SuggestionPage title="Đề nghị" />,
    children: [
      {
        path: "delete-proposal",
        name: "delete-proposal",
        title: "Xóa đề nghị",
        description: "Được phép xóa đề nghị kể cả khi đề nghị được duyệt",
      },
      {
        path: "close-proposal",
        name: "close-proposal",
        title: "Đóng đề nghị",
        description:
          "Bên cạnh người tạo, admin, kế toán. User nào có quyền này cũng được phép đóng đề nghị",
      },
      {
        path: "update-product-code",
        name: "update-product-code",
        title: "Cập nhật mã hàng",
        description:
          "Sau khi đề nghị đặt hàng - vật tư được duyệt. User nào có quyền này sẽ được cập nhật mã hàng cho đề nghị",
      },
      {
        path: "see-all-proposal-package",
        name: "see-all-proposal-package",
        title: "Xem tất cả đề nghị vật tư - Bao bì",
        description: "Quyền cho phép xem tất cả đề nghị vật tư - Bao bì",
      },
    ],
  },
  {
    title: "Mua hàng",
    icon: <FaCartShopping />,
    path: "/mua-hang",
    name: "/mua-hang",
    bg: "from-[#ff4456] to-[#ff4a2f]",
    element: <PurchasePage title="Mua hàng" />,
    children: [
      {
        path: "delete-purchase",
        name: "delete-purchase",
        title: "Xóa mua hàng",
        description: "Được phép xóa mua hàng kể cả khi mua hàng được duyệt",
      },
      {
        path: "view-money-info",
        name: "view-money-info",
        title: "Xem giá tiền",
        description:
          "Bên cạnh người tạo, admin, kế toán. User nào có quyền này cũng được phép xem giá tiền",
      },
    ],
  },
  {
    title: "Báo giá",
    icon: <FaCalendarWeek />,
    path: "/bao-gia",
    name: "/bao-gia",
    bg: "from-[#ff4456] to-[#ff4a2f]",
    element: <QuotePage title="Báo giá" />,
  },
  {
    title: "Kho vật tư",
    icon: <FaStoreAlt />,
    path: "/kho-vat-tu",
    name: "/kho-vat-tu",
    element: <WareHousePage title="Kho vật tư" />,
    bg: "from-[#a152e3] to-[#c994f5]",
    children: [
      {
        path: "view-all-inventory",
        name: "view-all-inventory",
        title: "Xem tất cả phiếu kho",
        description: "Được phép xem tất cả phiếu kho",
      },
      {
        path: "delete-inventory",
        name: "delete-inventory",
        title: "Xóa phiếu kho",
        description: "Được phép xóa phiếu kho kể cả khi phiếu kho được duyệt",
      },
      {
        path: "view-PO-missing",
        name: "view-PO-missing",
        title: "Xem hàng hóa chưa về đủ",
        description: "Xem được tab hàng hóa chưa về đủ ở trang Kho vật tư",
      },
      {
        path: "view-PO-products",
        name: "view-PO-products",
        title: "Xem hàng hóa PO",
        description: "Xem được tab hàng hóa PO ở trang Kho vật tư",
      },
      {
        path: "view-inventory-product",
        name: "view-inventory-product",
        title: "Xem danh sách kho",
        description: "Xem được danh sách kho",
      },
      {
        path: "view-in-product",
        name: "view-in-product",
        title: "Xem phiếu nhập kho",
        description: "Xem được tab phiếu nhập kho ở trang Kho vật tư",
      },
      {
        path: "view-out-product",
        name: "view-out-product",
        title: "Xem phiếu xuất kho",
        description: "Xem được tab phiếu xuất kho ở trang Kho vật tư",
      },
      {
        path: "view-out-error-product",
        name: "view-out-error-product",
        title: "Xem phiếu trả hàng lỗi",
        description: "Xem được tab phiếu trả hàng lỗi ở trang Kho vật tư",
      },
      {
        path: "view-in-error-product",
        name: "view-in-error-product",
        title: "Xem phiếu nhận hàng lỗi",
        description: "Xem được tab phiếu nhận hàng lỗi ở trang Kho vật tư",
      },
      {
        path: "view-out-send-product",
        name: "view-out-send-product",
        title: "Xem phiếu gửi hàng gia công",
        description: "Xem được tab phiếu gửi hàng gia công ở trang Kho vật tư",
      },
      {
        path: "view-in-send-product",
        name: "view-in-send-product",
        title: "Xem phiếu nhận hàng gia công",
        description: "Xem được tab phiếu nhận hàng gia công ở trang Kho vật tư",
      },
      {
        path: "view-check-inventory",
        name: "view-check-inventory",
        title: "Xem phiếu kiểm kho",
        description: "Xem được tab phiếu kiểm kho ở trang Kho vật tư",
      },
      {
        path: "view-change-inventory",
        name: "view-change-inventory",
        title: "Xem phiếu chuyển kho",
        description: "Xem được tab phiếu chuyển kho ở trang Kho vật tư",
      },
      {
        path: "view-inventory-report",
        name: "view-inventory-report",
        title: "Xem tổng hợp tồn kho",
        description: "Xem được tab tổng hợp tồn kho ở trang Kho vật tư",
      },
      {
        path: "create-inventory-from-PO",
        name: "create-inventory-from-PO",
        title: "Tạo nhanh nhập kho từ PO",
        description:
          "Hiển thị nút Tạo phiếu nhập kho ở modal chi tiết mua hàng (PO)",
      },
      {
        path: "create-out-inventory-from-proposal",
        name: "create-out-inventory-from-proposal",
        title: "Tạo nhanh phiếu xuất kho từ đề nghị",
        description:
          "Hiển thị nút Sinh phiếu xuất kho ở modal chi tiết đề nghị",
      },
      {
        path: "create-inventory-out",
        name: "create-inventory-out",
        title: "Tạo phiếu xuất kho",
        description: "Được quyền tạo phiếu xuất kho",
      },
      {
        path: "create-inventory-out-product-error",
        name: "create-inventory-out-product-error",
        title: "Tạo phiếu trả hàng lỗi",
        description: "Được quyền tạo phiếu trả hàng lỗi",
      },
    ],
  },
  {
    title: "Nhà cung cấp",
    icon: <FaTruck />,
    path: "/provider",
    name: "/provider",
    breadcrumb: "Nhà cung cấp",
    element: <ProviderPage title="Nhà cung cấp" />,
    bg: "from-[#ff4456] to-[#ff4a2f]",
  },
  {
    title: "Nhân viên",
    icon: <FaIdCardAlt />,
    path: "/staff",
    name: "/staff",
    breadcrumb: "Nhân viên",
    element: <StaffPage title="Nhân viên" />,
    bg: "from-[#044df0] to-[#00b0ef]",
  },
  {
    title: "Phòng ban",
    icon: <HiUserGroup />,
    path: "/department",
    name: "/department",
    breadcrumb: "Phòng ban",
    element: <DepartmentPage type={DepartmentType.Admin} title="Phòng ban" />,
    bg: "from-[#f4be6d] to-[#e8606c]",
  },
  {
    title: "Hàng hóa - Vật tư",
    icon: <FaBox />,
    path: "/product",
    name: "/product",
    breadcrumb: "Hàng hóa/Vật tư",
    element: <ProductPage title="DS hàng hóa/Vật tư" />,
    bg: "from-[#ffb202] to-[#fec305]",
  },
  {
    title: "Danh sách quyền",
    breadcrumb: "Danh sách quyền",
    icon: <FaKey />,
    path: "/role",
    name: "/role",
    element: <RolePage title="Danh sách quyền" />,
    bg: "from-[#111111] to-[#666]",
  },
  {
    title: "Cấu hình",
    breadcrumb: "Cấu hình",
    icon: <SettingFilled />,
    path: "/configuration",
    name: "/configuration",
    element: <ConfigPage title="Cấu hình" />,
    bg: "from-[#111111] to-[#666]",
  },

  //Payroll

  ...routersForStaging,

  //Start role for checkIn Feature
  {
    title: "Kiểm soát ra vào",
    breadcrumb: "Kiểm soát ra vào",
    group: RouteGroup.checkIn,
    icon: <FaDoorClosed />,
    onClick: () => {
      window.open("/checkin", "_blank");
    },
    name: "/checkIn",
    path: "/checkIn",
    element: <PayrollConfigPage title="Cấu hình" />,
    bg: "from-[#044df0] to-[#00b0ef]",
    children: [
      {
        title: "Thống kê, cấu hình hệ thống",
        breadcrumb: "Thống kê, cấu hình hệ thống",
        path: PermissionName.checkInAdmin,
        name: PermissionName.checkInAdmin,
        description: "Thống kê, cấu hình hệ thống.",
      },
      {
        title: "TVS",
        breadcrumb: "TVS",
        description: "Quản lý lịch sử khai báo",
        path: PermissionName.inspector,
        name: PermissionName.inspector,
      },
      {
        title: "PV",
        breadcrumb: "PV",
        path: PermissionName.bep,
        name: PermissionName.bep,
        description: "Quản lý đơn nước uống",
      },
    ],
  },

  {
    title: "Nhật ký đăng nhập",
    breadcrumb: "Nhật ký đăng nhập",
    icon: <MdLockClock />,
    path: "/session-device",
    name: "/session-device",
    element: <SessionDevicePage title="Nhật ký đăng nhập" />,
    bg: "from-[#2ccc06] to-[#1d8c03]",
  },

  //End role for checkIn Feature

  //Start section for production manager feature
  {
    title: "Khu vực",
    icon: <FaBuilding />,
    path: "/area",
    name: "/area",
    breadcrumb: "Khu vực",
    element: <AreaPage title="Khu vực" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
  },
  {
    title: "Quản lý báo cáo",
    icon: <MdReportProblem />,
    path: PermissionName.QLSXReport,
    name: PermissionName.QLSXReport,
    breadcrumb: "Quản lý báo cáo",
    element: <ReportPage title="Quản lý báo cáo" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
  },
  {
    title: "Tiến hành báo cáo",
    icon: <FaClipboardList />,
    path: PermissionName.QLSXAnnunciator,
    name: PermissionName.QLSXAnnunciator,
    breadcrumb: "Tiến hành báo cáo",
    bg: "from-[#5244dc] to-[#7c71e5]",
    isParent: true,
    group: RouteGroup.productionManager,
    children: [
      {
        title: "Chọn khu vực",
        path: "",
        name: "",
        breadcrumb: "Chọn khu vực",
        element: <ChooseArea showOther title="Tiến hành báo cáo" />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
      {
        title: "Danh sách khai báo",
        path: ":slug",
        name: ":slug",
        breadcrumb: "Danh sách khai báo",
        element: <Declare />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
    ],
  },
  {
    title: "Tiến hành khắc phục",
    count: {
      link: [AppSummaryKey.PRODUCTION_MANAGEMENT, ReportStatus.Pending],
      role: PermissionName.QLSXFixer,
    },
    icon: <GiAutoRepair />,
    path: PermissionName.QLSXFixer,
    name: PermissionName.QLSXFixer,
    breadcrumb: "Tiến hành khắc phục",
    bg: "from-[#5244dc] to-[#7c71e5]",
    isParent: true,
    group: RouteGroup.productionManager,
    children: [
      {
        title: "Chọn khu vực",
        path: "",
        name: "",
        breadcrumb: "Chọn khu vực",
        element: <FixerChooseAreaWrapper />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
      {
        title: "Danh sách khai báo",
        path: ":slug",
        name: ":slug",
        breadcrumb: "Danh sách khai báo",
        element: <RepairPage />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
    ],
  },
  {
    title: "KT Khắc phục ĐKSX",
    icon: <MdOutlineContentPasteSearch />,
    path: PermissionName.QLSXInspector,
    name: PermissionName.QLSXInspector,
    breadcrumb: "KT Khắc phục ĐKSX",
    bg: "from-[#5244dc] to-[#7c71e5]",
    isParent: true,
    group: RouteGroup.productionManager,
    children: [
      {
        title: "Chọn khu vực",
        path: "",
        name: "",
        breadcrumb: "Chọn khu vực",
        element: <CheckerChooseAreaWrapper />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
      {
        title: "Danh sách khai báo",
        path: ":slug",
        name: ":slug",
        breadcrumb: "Danh sách khai báo",
        element: <CensorshipPage />,
        bg: "from-[#5244dc] to-[#7c71e5]",
        group: RouteGroup.productionManager,
      },
    ],
  },
  {
    title: "List sản xuất",
    icon: <MdProductionQuantityLimits />,
    path: PermissionName.listSanXuat,
    name: PermissionName.listSanXuat,
    breadcrumb: "List sản xuất",
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
    element: (
      <NetworkProvider>
        <ProductionListPage title="List sản xuất" />,
      </NetworkProvider>
    ),
  },
  {
    title: "Khách hàng",
    icon: <IoPeopleOutline />,
    path: "/customer",
    name: "/customer",
    breadcrumb: "Khách hàng",
    element: <CustomerPage title="Khách hàng" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
    children: [
      {
        path: PermissionName.viewAllCustomer,
        name: PermissionName.viewAllCustomer,
        title: "Xem được tất cả khách hàng",
        description:
          "Nếu không có quyền này, thì chỉ xem được khách của mình tạo",
      },
      {
        path: PermissionName.capNhatKhachHangCuaMinh,
        name: PermissionName.capNhatKhachHangCuaMinh,
        title: "Cập nhật khách hàng của mình tạo",
        description: "Chỉ được cập nhật khách hàng của mình tạo",
      },
      {
        path: PermissionName.capNhatTatCaKhachHang,
        name: PermissionName.capNhatTatCaKhachHang,
        title: "Cập nhật khách hàng",
        description: "Cập nhật khách hàng",
      },
      {
        path: PermissionName.xoaKhachHang,
        name: PermissionName.xoaKhachHang,
        title: "Xóa khách hàng",
        description: "Xóa khách hàng",
      },
    ],
  },
  {
    title: "Bao bì",
    icon: <GoPackage />,
    path: "/package",
    name: "/package",
    breadcrumb: "Bao bì",
    element: (
      <NetworkProvider>
        <PackageContractPage title="Bao bì" />
      </NetworkProvider>
    ),
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
    children: [
      {
        path: PermissionName.taoBaoBi,
        name: PermissionName.taoBaoBi,
        title: "Tạo bao bì",
        description: "",
      },
    ],
  },
  {
    title: "Quốc gia",
    icon: <AiOutlineGlobal />,
    path: "/nation",
    name: "/nation",
    breadcrumb: "Quốc gia",
    element: <NationPage title="Quốc gia" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
  },
  {
    title: "Quy trình đơn giản",
    icon: <RiFlowChart />,
    path: "/basicProcedure",
    name: "/basicProcedure",
    breadcrumb: "Quy trình đơn giản",
    element: <BasicProcedurePage title="Quy trình đơn giản" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
  },
  {
    title: "Quy trình chi tiết",
    icon: <RiFlowChart />,
    path: "/detailsProcedure",
    name: "/detailsProcedure",
    breadcrumb: "Quy trình chi tiết",
    element: <DetailsProcedurePage title="Quy trình chi tiết" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
  },
  {
    title: "Cấu hình",
    breadcrumb: "Cấu hình",
    icon: <AiTwotoneSetting />,
    path: "/configProductManagement",
    name: "/configProductManagement",
    element: <ConfigProductManagementPage title="Cấu hình" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    group: RouteGroup.productionManager,
    // children: [
    //   {
    //     title: "Cấu hình ngày tháng năm",
    //     path: PermissionName.dateConfig,
    //     name: PermissionName.dateConfig,
    //   },
    // ],
  },
  // {
  //   title: "Cấu hình",
  //   breadcrumb: "Cấu hình",
  //   icon: <SettingFilled />,
  //   path: "/configuration",
  //   name: "/configuration",
  //   element: <ConfigPage title="Cấu hình" />,
  //   bg: "from-[#111111] to-[#666]",
  // },

  //End section for production manager feature

  {
    title: "Thông tin cá nhân",
    breadcrumb: "Thông tin cá nhân",
    icon: <AiTwotoneSetting />,
    path: "/profile",
    name: "/profile",
    element: <ProfilePage title="Thông tin cá nhân" />,
    bg: "from-[#5244dc] to-[#7c71e5]",
    hidden: true,
    isPublic: true,
    // children: [
    //   {
    //     title: "Cấu hình ngày tháng năm",
    //     path: PermissionName.dateConfig,
    //     name: PermissionName.dateConfig,
    //   },
    // ],
  },
  // start business section
  {
    title: "Hợp đồng",
    breadcrumb: "Hợp đồng",
    icon: <FaFileContract />,
    path: "/contract",
    name: "/contract",
    // element: <ContractConfiguration title="Nội dung hợp đồng" />,
    element: <ContractPage title="Hợp đồng " />,
    bg: "from-[#2ccc06] to-[#1d8c03]",
    group: RouteGroup.business,
    children: [
      {
        path: PermissionName.viewAllContract,
        name: PermissionName.viewAllContract,
        title: "Xem tất cả hợp đồng",
        description:
          "Nếu không có quyền này, thì chỉ xem được hợp đồng của mình tạo",
      },
      {
        path: PermissionName.createContract,
        name: PermissionName.createContract,
        title: "Tạo hợp đồng",
      },
      {
        path: PermissionName.updateContract,
        name: PermissionName.updateContract,
        title: "Cập nhật hợp đồng",
      },
      {
        path: PermissionName.deleteContract,
        name: PermissionName.deleteContract,
        title: "Xóa hợp đồng",
      },
      // {
      //   path: PermissionName.viewOnlyContract,
      //   name: PermissionName.viewOnlyContract,
      //   title: "Chỉ xem hợp đồng",
      // },
      {
        path: PermissionName.paymentUpdate,
        name: PermissionName.paymentUpdate,
        title: "Chỉnh sửa thông tin thanh toán",
      },
    ],
  },
  // {
  //   title: "Quy cách đóng gói",
  //   breadcrumb: "Quy cách đóng gói",
  //   icon: <FaBox />,
  //   path: "/packaging",
  //   name: "/packaging",
  //   element: <PackagingPage title="Quy cách đóng gói" />,
  //   bg: "from-[#2ccc06] to-[#1d8c03]",
  //   group: RouteGroup.business,
  // },
  {
    title: "Market",
    breadcrumb: "Market",
    icon: <FaStore />,
    path: "/market",
    name: "/market",
    element: <MarketPage title="Market" />,
    bg: "from-[#2ccc06] to-[#1d8c03]",
    group: RouteGroup.business,
  },
  // {
  //   title: "Bao bì",
  //   icon: <GoPackage />,
  //   path: "/package-contract",
  //   name: "/package-contract",
  //   breadcrumb: "Bao bì",
  //   element: (
  //     <NetworkProvider>
  //       <PackageContractPage title="Bao bì" />
  //     </NetworkProvider>
  //   ),
  //   bg: "from-[#2ccc06] to-[#1d8c03]",
  //   group: RouteGroup.business,
  // },
  {
    title: "Báo cáo",
    icon: <FaClipboardList />,
    path: "/dashboard-contract",
    name: "/dashboard-contract",
    breadcrumb: "Báo cáo",
    element: <ContractDashboardPage title="Báo cáo" />,
    bg: "from-[#2ccc06] to-[#1d8c03]",
    group: RouteGroup.business,
  },
  {
    title: "Cấu hình",
    breadcrumb: "Cấu hình",
    icon: <AiTwotoneSetting />,
    path: "/config-contract",
    name: "/config-contract",
    element: <ContractMainPage title="Cấu hình" />,

    bg: "from-[#2ccc06] to-[#1d8c03]",
    group: RouteGroup.business,
    children: [
      {
        path: PermissionName.updateContractConfig,
        name: PermissionName.updateContractConfig,
        title: "Chỉnh sửa cấu hình",
      },
    ],
  },
  // end business section
  //start colde storage section
  {
    title: "Sản phẩm kho lạnh",
    breadcrumb: "Sản phẩm kho lạnh",
    icon: <ProductOutlined />,
    path: "/cold-storage-product",
    name: "/cold-storage-product",
    element: <ColdStorageProductPage title="Sản phẩm kho lạnh" />,
    bg: "from-[#d1d5db] to-[#6b7280]",
    group: RouteGroup.cold_storage,
  },
  {
    title: "Kho vật tư",
    breadcrumb: "Kho vật tư",
    icon: <IoStorefrontOutline />,
    path: "/cold-storage-kho-vat-tu",
    name: "/cold-storage-kho-vat-tu",
    element: <ColdStorageWareHouse title="Kho vật tư" />,
    bg: "from-[#d1d5db] to-[#6b7280]",
    group: RouteGroup.cold_storage,
    children: [
      {
        path: "view-all-inventory",
        name: "view-all-inventory",
        title: "Xem tất cả phiếu kho",
        description: "Được phép xem tất cả phiếu kho ở trang Kho lạnh",
      },
      {
        path: "view-inventory-product",
        name: "view-inventory-product",
        title: "Xem danh sách kho",
        description: "Xem được danh sách kho ở trang Kho lạnh",
      },
      {
        path: "view-in-product",
        name: "view-in-product",
        title: "Xem phiếu nhập kho",
        description: "Xem được tab phiếu nhập kho ở trang Kho lạnh",
      },
      {
        path: "view-out-product",
        name: "view-out-product",
        title: "Xem phiếu xuất kho",
        description: "Xem được tab phiếu xuất kho ở trang Kho lạnh",
      },
      {
        path: "view-change-inventory",
        name: "view-change-inventory",
        title: "Xem phiếu chuyển kho",
        description: "Xem được tab phiếu chuyển kho ở trang Kho lạnh",
      },
      {
        path: "view-inventory-report",
        name: "view-inventory-report",
        title: "Xem tổng hợp tồn kho",
        description: "Xem được tab tổng hợp tồn kho ở trang Kho lạnh",
      },
    ],
  },
  {
    title: "Quản lý chuyến",
    breadcrumb: "Quản lý chuyến",
    icon: <CarOutlined />,
    path: "/cold-storage-logistic",
    name: "/cold-storage-logistic",
    element: <LogisticPage title="Quản lý chuyến" />,
    bg: "from-[#d1d5db] to-[#6b7280]",
    group: RouteGroup.cold_storage,
  },
  // end colde storage section
  {
    path: "/other-role",
    name: "/other-role",
    title: "Quyền khác",
    hidden: true,
    children: [
      {
        path: "payment-update",
        name: "payment-update",
        title: "Quyền cập nhật thanh toán",
        description:
          "Được quyền đánh dấu đã đặt mua, đã thanh toán, đã hoàn thành ở các đề nghị, phiếu mua hàng",
      },
      // {
      //   path: "lam-mau-update",
      //   name: "lam-mau-update",
      //   title: "Quyền cập nhật lệnh làm mẫu",
      //   description: "Quyền cập nhật lệnh làm mẫu",
      // },
      {
        path: "view-provider-info",
        name: "view-provider-info",
        title: "Xem nhà cung cấp",
        description:
          "Bên cạnh người tạo, admin, kế toán. User nào có quyền này cũng được phép xem nhà cung cấp",
      },
      {
        path: PermissionName.viewAllProductionConditionReport,
        name: PermissionName.viewAllProductionConditionReport,
        title: "Xem tất cả báo cáo điều kiện sản xuất",
        description:
          "Quyền người dùng có thể xem tất cả báo cáo tại các menu tiến hành, xử lý báo cáo (báo cáo, khắc phục, KT Khắc phục ĐKSX)",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.deleteProductionConditionReport,
        name: PermissionName.deleteProductionConditionReport,
        title: "Xóa báo cáo điểu kiện sản xuất",
        description:
          "Quyền người dùng có thể xóa bất ký báo cáo điều kiện sản xuất nào",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.viewAllProductionListContent,
        name: PermissionName.viewAllProductionListContent,
        title: "Xem được tất cả nội dung các menu quản lý sản xuất",
        description:
          "Xem được tất cả nội dung cho dù không được gắn theo dõi, người duyệt. ( List sản xuất, đề nghị bao bì, quy trình chi tiết, quy trình đơn giản )",
        group: RouteGroup.productionManager,
      },

      {
        path: PermissionName.xemTatCaBaoBi,
        name: PermissionName.xemTatCaBaoBi,
        title: "Xem được tất cả nội dung menu quản lý bao bì",
        description:
          "Xem tất cả nội dung của menu bao bì, không được tạo dữ liệu",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaListSanXuat,
        name: PermissionName.xemTatCaListSanXuat,
        title: "Xem được tất cả nội dung menu LIST sản xuất",
        description:
          "Xem tất cả nội dung của menu LIST sản xuất, không được tạo dữ liệu",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaQuyTrinhChiTiet,
        name: PermissionName.xemTatCaQuyTrinhChiTiet,
        title: "Xem được tất cả nội dung menu Quy trình chi tiết",
        description:
          "Xem tất cả nội dung của menu Quy trình chi tiết, không được tạo dữ liệu",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaQuyTrinhDonGian,
        name: PermissionName.xemTatCaQuyTrinhDonGian,
        title: "Xem được tất cả nội dung menu Quy trình đơn giản",
        description:
          "Xem tất cả nội dung của menu Quy trình đơn giản, không được tạo dữ liệu",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaKhachHang,
        name: PermissionName.xemTatCaKhachHang,
        title: "Xem được tất cả nội dung menu Khách hàng",
        description:
          "Xem tất cả nội dung của menu Khách hàng, không được tạo dữ liệu",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.capNhatTatCaKhachHang,
        name: PermissionName.capNhatTatCaKhachHang,
        title: "Cập nhật được tất cả nội dung menu Khách hàng",
        description: "Cập nhật tất cả nội dung của menu Khách hàng",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaBaoBiDuocTag,
        name: PermissionName.xemTatCaBaoBiDuocTag,
        title:
          "Chỉ xem nội dung menu quản lý bao bì khi là người tạo hoặc được tag ",
        description:
          "Chỉ xem nội dung menu quản lý bao bì khi là người tạo hoặc được tag, được tạo nội dung",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaListSanXuatDuocTag,
        name: PermissionName.xemTatCaListSanXuatDuocTag,
        title:
          "Chỉ xem nội dung menu LIST sản xuất khi là người tạo hoặc được tag",
        description:
          "Chỉ xem nội dung menu LIST sản xuất khi là người tạo hoặc được ta, được tạo nội dung",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaQuyTrinhChiTietDuocTag,
        name: PermissionName.xemTatCaQuyTrinhChiTietDuocTag,
        title:
          "Chỉ xem nội dung menu Quy trình chi tiết khi là người tạo hoặc được tag ",
        description:
          "Chỉ xem nội dung menu Quy trình chi tiết khi là người tạo hoặc được tag, được tạo nội dung",
        group: RouteGroup.productionManager,
      },
      {
        path: PermissionName.xemTatCaQuyTrinhDonGianDuocTag,
        name: PermissionName.xemTatCaQuyTrinhDonGianDuocTag,
        title:
          "Chỉ xem nội dung menu Quy trình đơn giản khi là người tạo hoặc được tag ",
        description:
          "Chỉ xem nội dung menu Quy trình đơn giản khi là người tạo hoặc được tag, được tạo nội dung",
        group: RouteGroup.productionManager,
      },
      {
        title: "Chỉnh sửa mã quy trình",
        breadcrumb: "Chỉnh sửa mã quy trình",
        path: PermissionName.editProcedureCode,
        name: PermissionName.editProcedureCode,
        description: "Chỉnh sửa mã quy trình.",
        group: RouteGroup.business,
      },
    ],
  },
];

const routes: Route[] = [
  {
    element: <AdminLayout />,
    children: adminRoutes,
    path: "/",
  },
  {
    path: "/login",
    element: <LoginPage title="Đăng nhập" />,
  },
  {
    path: "/test/canvas",
    element: <TestCanvasPage />,
  },
  {
    path: "/module/importExcel",
    element: <ImportExcelModule />,
  },
  {
    path: "*",
    element: <NotFoundPage />,
  },
];

export { routes };
