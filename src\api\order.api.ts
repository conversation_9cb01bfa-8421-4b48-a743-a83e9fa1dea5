import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const orderApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/order",
      params,
    }),
  detail: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/order",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}`,
      data,
      method: "patch",
    }),
  estimate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/order/estimate",
      data,
      method: "post",
    }),
  delivering: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}/delivering`,
      method: "patch",
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}/complete`,
      method: "patch",
    }),
  cancel: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/order/${id}/cancel`,
      method: "patch",
    }),
};
