import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const allowanceGroupApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/allowanceGroup",
      params,
    }),
  findOne: (id: number, params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/allowanceGroup/" + id,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/allowanceGroup",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/allowanceGroup/${id}`,
      method: "patch",
      data,
    }),
  updateDetails: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/allowanceGroup/${id}/details`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/allowanceGroup/${id}`,
      method: "delete",
    }),
};
