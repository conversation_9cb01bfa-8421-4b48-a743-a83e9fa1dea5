import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const procedureApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/procedure",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}`,
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}/approve`,
      method: "patch",
      data,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/procedure",
      data,
      method: "post",
    }),
  publishNo: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}/publishNo`,
      method: "patch",
    }),

  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}`,
      method: "delete",
    }),
  reject: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/procedure/${id}/reject`,
      method: "delete",
    }),
  summary: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/procedure/summary/status",
      params,
    }),
};
