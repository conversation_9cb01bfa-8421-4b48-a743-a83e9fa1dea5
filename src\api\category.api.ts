import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const categoryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/category",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/category/${id}`,
    }),

  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/category",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/category/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/category/${id}`,
      method: "delete",
    }),
};
