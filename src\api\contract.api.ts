import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const contractApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/contract",
      params,
    }),
  findOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}`,
    }),
  followStaff: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}/followStaff`,
      method: "patch",
      data,
    }),
  summary: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/summary/status`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/contract",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}`,
      method: "delete",
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}/complete`,
      method: "patch",
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}/approve`,
      method: "patch",
      data,
    }),
  reject: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/contract/${id}/reject`,
      method: "delete",
    }),
};
