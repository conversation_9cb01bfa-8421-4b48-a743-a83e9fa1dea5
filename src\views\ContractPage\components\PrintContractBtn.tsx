import { contractContentApi } from "@/api/contractContent.api";
import { Contract, ContractType, ContractTypeTrans } from "@/types/contract";
import { ContractContent } from "@/types/contractContent";
import { PaymentTermType } from "@/types/paymentTerm";
import { PurchaseOrder } from "@/types/purchaseOrder";
import ContractPDF from "@/utils/PDF/ContractPDF";
import ReportOrderPDF from "@/utils/PDF/reportOrderPDF";
import { PrinterOutlined } from "@ant-design/icons";
import { Button, Dropdown, Form, Menu, Radio, Space } from "antd";
import { useEffect, useRef, useState } from "react";
import ReactToPrint, { useReactToPrint } from "react-to-print";

type Props = {
  data: Contract;
};

export const printStyle = (code: string) => `
@media print {
  @page {
    size: A4 portrait;
    margin: 0.2cm 0.2cm 2cm 0.2cm;
    @bottom-center {
      content: "";
      border-top: 10px solid #6BA2D6;
      margin-top: 3px;
      padding-bottom: 14px;
    }
    @bottom-right {
      content: counter(page);
      border-top: 10px solid #6BA2D6;
      margin-right: 1cm;
      margin-top: 3px;
      padding-bottom: 14px;
      color: #666;
    }
    @bottom-left {
      content: "";
      border-top: 10px solid #6BA2D6;
      margin-top: 3px;
      margin-left: 1cm;
      padding-bottom: 14px;
      font-size: 14px;
    }
  }

  .footer-center {
    position: running(footer-center);
    border-top: 2px solid #6BA2D6;
    padding-top: 5px;
    text-align: center;
    font-size: 12px;
    color: #666;
  }

  .footer-right {
    position: running(footer-right);
    border-top: 1px solid #ddd;
    padding-top: 3px;
    text-align: right;
    font-size: 12px;
    color: #666;
  }

  .print-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    font-size: 12px;
    color: #666;
    padding-top: 5px;
    border-top: 2px solid #6BA2D6;
    background: white;
    text-align: center;
    z-index: 1000;
  }

  body {
    print-color-adjust: exact;
    -webkit-print-color-adjust: exact;
  }
}
`;

export const PrintContractBtn = ({ data }: Props) => {
  const [component, setComponent] = useState<React.ReactElement>();
  const componentRef = useRef(null);
  const printButtonRef = useRef<HTMLDivElement>(null);

  const [enUnit, setEnUnit] = useState<"box" | "unit">("unit");
  const [typeContract, setTypeContract] = useState(ContractType.General);

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    onAfterPrint: () => (document.title = `Hợp đồng - Khang An Foods`),
  });

  // Sửa lại hàm này, bỏ dataInput, chỉ truyền unitType
  const handleSetComponent = (
    printType: "vi" | "en" | "all",
    unitType: "box" | "unit",
    typeContract?: string
  ) => {
    const printComponent = ContractPDF.getPrintComponent({
      data,
      printType,
      unitType, // truyền đúng prop unitType
      typeContract,
      contents,
    });

    setComponent(
      <div className="hidden">
        <style type="text/css" media="print">
          {printStyle(data.code)}
        </style>
        <div ref={componentRef}>
          <div>{printComponent}</div>
        </div>
      </div>
    );
  };

  const handlePrintContract = (
    printType: "vi" | "en" | "all",
    unitType: "box" | "unit",
    typeContract?: string
  ) => {
    let title = "";
    let printUnit = unitType === "box" ? "Thùng" : "Đơn vị tính";
    if (printType === "vi") {
      printUnit =
        unitType === "box" ? "Thùng" : data.contractProducts[0]?.unit?.name;
      title = `Hợp đồng - ${data.code} - ${printUnit}`;
    } else if (printType === "en") {
      title = `Contract - ${data.code} - ${printUnit}`;
    } else if (printType === "all") {
      title = `Hợp đồng (Contract) - ${data.code} - ${printUnit}`;
    }
    document.title = title;
    handleSetComponent(
      typeContract === ContractType.Inbound ? "vi" : printType,
      unitType,
      typeContract
    ); // truyền trực tiếp unitType
    setTimeout(() => {
      handlePrint();
    }, 100);
  };

  const ContractTypeMap = Object.entries(ContractTypeTrans).map(
    ([value, label]) => ({
      value: value as ContractType,
      label,
    })
  );

  const [contents, setContents] = useState<ContractContent[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      const res = await contractContentApi.findAll({
        limit: 100,
        page: 1,
        contractType: (typeContract as ContractType) ?? ContractType.General,
      });

      setContents([...(res?.data?.contractConfigs as ContractContent[])]);
    };
    fetchData();
  }, [typeContract]);

  const menu = (
    <Menu>
      <Menu.Item
        key="en-custom"
        disabled
        style={{ background: "#fafafa", padding: 0 }}
      >
        <Space
          style={{
            display: "flex",
            alignItems: "start",
            // width: "100%",
            padding: "8px 16px",
          }}
        >
          <Radio.Group
            value={typeContract}
            onChange={(e) => setTypeContract(e.target.value)}
            style={{ width: 200, marginRight: 2 }}
          >
            {ContractTypeMap.map((contractType) => (
              <Radio value={contractType.value}>{contractType.label}</Radio>
            ))}
          </Radio.Group>
          <Radio.Group
            value={enUnit}
            onChange={(e) => setEnUnit(e.target.value)}
            style={{ width: 140, marginRight: 8 }}
          >
            <Radio value="box">Thùng</Radio>
            <Radio value="unit">Đơn vị tính</Radio>
          </Radio.Group>
          <Button
            icon={<PrinterOutlined />}
            type="primary"
            size="small"
            onClick={() => handlePrintContract("en", enUnit, typeContract)}
          >
            In hợp đồng
          </Button>
        </Space>
      </Menu.Item>
    </Menu>
  );

  return (
    <>
      {component}
      <Dropdown overlay={menu} trigger={["click"]}>
        <Button
          ref={printButtonRef}
          className="font-medium text-xs md:text-base py-1 md:px-4 px-2"
        >
          <PrinterOutlined />
          In hợp đồng
        </Button>
      </Dropdown>
    </>
  );
};

export default PrintContractBtn;
