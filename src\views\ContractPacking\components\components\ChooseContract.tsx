import { useContract } from "@/hooks/useContract";
import { PermissionName } from "@/router";
import { userStore } from "@/store/userStore";
import { AutoComplete } from "antd";
import { debounce } from "lodash";
import { observer } from "mobx-react";
import { useEffect } from "react";

type Props = {
  onChange?: (value: string, option?: any) => void;
};

const ChooseContract = ({ onChange }: Props) => {
  const { query, fetchData, contracts } = useContract({
    initQuery: {
      page: 1,
      limit: 100,
      isGetOwn: userStore.checkRoleByName(PermissionName.viewAllContract)
        ? false
        : true,
    },
  });

  const debounceSearchContract = debounce((search: string) => {
    query.page = 1;
    query.search = search;
    fetchData({ ...query, page: 1, search: search });
  }, 300);

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <AutoComplete
      options={contracts.map((contract) => ({
        label: contract.name,
        value: contract.name,
        item: contract,
      }))}
      // value={}
      allowClear
      onChange={(value, option) => {
        onChange?.(value, option);
      }}
      onSearch={debounceSearchContract}
      filterOption={false}
      placeholder="Nhập số hợp đồng..."
    />
  );
};

export default observer(ChooseContract);
