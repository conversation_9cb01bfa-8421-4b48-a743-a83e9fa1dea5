import { Card, DatePicker, Select, Space, Spin, Table } from "antd";
import { useEffect, useState } from "react";
import "../Dashboard.scss";
import { dashboardApi } from "@/api/dashboard.api";
import { formatVND } from "@/utils";
import { Pagination } from "@/components/Pagination";
import { settings } from "../../../../settings";
import dayjs from "dayjs";
import { useContract } from "@/hooks/useContract";
import { QueryParam } from "@/types/query";
import { useCustomer } from "@/hooks/useCustomer";
import SearchProposal from "@/components/SearchProposal/SearchProposal";
import { contractApi } from "@/api/contract.api";
import { ContractUnits } from "@/types/contract";

const { Column } = Table;

interface ContractReportData {
  key: string;
  contractId: number;
  customerName: string;
  endCustomer: string;
  contractNumber: string;
  poNumber: string;
  productName: string;
  specification: string;
  packing: string;
  termsOfDelivery: string;
  price: number;
  deliveryTime: string;
  rowSpan?: number;
  sttNumber?: number;
  unitId: number;
}

export const SeasonalPrice = () => {
  const {
    contracts,
    loading: loadingContract,
    total: totalContract,
    fetchData: fetchContracts,
    setQuery,
    query
  } = useContract({
    initQuery: {
      page: 1,
      limit: 10,
      getAll: true,
    },
  });

  const { customers, fetchCustomer } = useCustomer({
    initQuery: { page: 1, limit: 100 },
  });

  // State để lưu danh sách hợp đồng cho filter
  const [contractOptions, setContractOptions] = useState<any[]>([]);
  // State để lưu contract được chọn chi tiết
  const [selectedContract, setSelectedContract] = useState<any>(null);
  // State để theo dõi việc load contract detail
  const [loadingContractDetail, setLoadingContractDetail] = useState(false);

  useEffect(() => {
    fetchCustomer();
    // Chỉ fetch contracts khi không có contractId được chọn
    if (!query.contractId) {
      fetchContracts();
    }
  }, []);

  useEffect(() => {
    // Nếu có contractId được chọn, fetch chi tiết contract đó
    if (query.contractId) {
      fetchContractDetail(query.contractId);
    } else {
      // Nếu không có contractId, fetch danh sách contracts
      fetchContracts(query);
      setSelectedContract(null);
    }
  }, [query]);

  // Hàm fetch chi tiết contract
  const fetchContractDetail = async (contractId: number) => {
    setLoadingContractDetail(true);
    try {
      // Giả sử bạn có API để lấy chi tiết contract
      const response = await contractApi.findOne(contractId); // Thay thế bằng API thực tế của bạn
      setSelectedContract(response.data);
    } catch (error) {
      console.error('Error fetching contract detail:', error);
      setSelectedContract(null);
    } finally {
      setLoadingContractDetail(false);
    }
  };

  // Effect để tạo danh sách options cho contract filter
  useEffect(() => {
    if (contracts && contracts.length > 0) {
      const uniqueContracts = contracts.reduce((acc: any[], contract: any) => {
        const existing = acc.find(item => item.id === contract.id);
        if (!existing) {
          acc.push({
            id: contract.id,
            name: contract.name || contract.code,
            code: contract.code
          });
        }
        return acc;
      }, []);

      setContractOptions(uniqueContracts);
    }
  }, [contracts]);

  const transformContractData = (contractsData: any[]): ContractReportData[] => {
    // Nếu có contract được chọn chi tiết, chỉ xử lý contract đó
    const dataToProcess = selectedContract ? [selectedContract] : contractsData;

    const transformedData: ContractReportData[] = [];
    let index = 0;
    let sttCounter = 1;

    dataToProcess.forEach(contract => {
      const products = contract.contractProducts || [];

      console.log("products", contract);

      if (products.length === 0) {
        transformedData.push({
          key: `${contract.id}_${index++}`,
          contractId: contract.id,
          customerName: contract.customer?.name || '',
          endCustomer: contract.customer?.endName || '',
          contractNumber: contract.name || contract.code,
          poNumber: contract.po || '',
          productName: '',
          specification: '',
          packing: '',
          termsOfDelivery: contract.termsOfDelivery || '',
          price: 0,
          deliveryTime: '',
          rowSpan: 1,
          sttNumber: sttCounter++,
          unitId: contract.unit?.id || 0
        });
      } else {
        const deliveryTimeMap = new Map();

        contract.contractPos?.forEach((pos: any) => {
          pos.poDetails?.forEach((detail: any) => {
            const productId = detail.contractProduct?.id;
            if (productId && !deliveryTimeMap.has(productId)) {
              deliveryTimeMap.set(productId, pos.etdDetail || '');
            }
          });
        });

        products.forEach((product: any, productIndex: number) => {
          const deliveryTime = deliveryTimeMap.get(product.id) || '';

          transformedData.push({
            key: `${contract.id}_${product.id}_${index++}`,
            contractId: contract.id,
            customerName: productIndex === 0 ? (contract.customer?.name || '') : '',
            endCustomer: productIndex === 0 ? (contract.customer?.endName || '') : '',
            contractNumber: productIndex === 0 ? (contract.name || contract.code) : '',
            poNumber: productIndex === 0 ? (contract.po || '') : '',
            productName: product.productName || '',
            specification: product.specification || product.specificationEn || '',
            packing: product.packing || '',
            termsOfDelivery: productIndex === 0 ? (contract.termsOfDelivery || '') : '',
            price: product.unitPrice || 0,
            deliveryTime: deliveryTime,
            rowSpan: productIndex === 0 ? products.length : 0,
            sttNumber: productIndex === 0 ? sttCounter++ : undefined,
            unitId: product.unit?.id || 0
          });
        });
      }
    });

    return transformedData;
  };

  const renderCell = (text: any, record: ContractReportData, dataIndex: string) => {
    const obj: any = {
      children: text,
      props: {},
    };

    const mergeColumns = ['customerName', 'endCustomer', 'contractNumber', 'poNumber', 'termsOfDelivery'];

    if (mergeColumns.includes(dataIndex)) {
      obj.props.rowSpan = record.rowSpan || 0;
    }

    return obj;
  };

  const renderSTT = (text: any, record: ContractReportData) => {
    const obj: any = {
      children: record.sttNumber || '',
      props: {},
    };

    obj.props.rowSpan = record.rowSpan || 0;

    return obj;
  };

  // Handler cho thay đổi contract filter
  const handleContractChange = (value: number | undefined) => {
    setQuery({
      ...query,
      page: 1,
      contractId: value,
      // Reset các filter khác khi chọn contract cụ thể
      customerId: value ? undefined : query.customerId,
      search: value ? undefined : query.search,
    });
  };

  const renderUnit = (id: number) => {
    if (!id) return "";

    const unit = ContractUnits.find((item) => item.id === id);
    if (unit) return `/${unit.nameEn}`;
    return ""
  }

  return (
    <Card size="small">
      <div className="chart-container">
        <div className="">
          <Space>
            <div className="chart-filter">
              <Space style={{ width: "100%", justifyContent: "flex-end" }}>

                <div className="filter-item">
                  <label>Tìm kiếm</label>
                  <br />
                  <SearchProposal
                    //@ts-ignore
                    disabled={!!query.contractId} // Disable khi đã chọn contract
                    onChange={(search: string) =>
                      setQuery({ ...query, search, page: 1 })
                    }
                  />
                </div>
                <div className="filter-item">
                  <label>Thời gian</label>
                  <br />
                  <DatePicker.RangePicker
                    allowClear
                    onChange={(dates) => {
                      if (dates?.length) {
                        const fromAt = dates[0]?.startOf("day").unix();
                        const toAt = dates[1]?.endOf("day").unix();

                        setQuery({
                          ...query,
                          page: 1,
                          fromAt,
                          toAt,
                        });
                      } else {
                        setQuery({
                          ...query,
                          page: 1,
                          fromAt: undefined,
                          toAt: undefined,
                        });
                      }
                    }}
                    value={
                      query.fromAt && query.toAt
                        ? [dayjs.unix(query.fromAt), dayjs.unix(query.toAt)]
                        : undefined
                    }
                    style={{ width: 250 }}
                    ranges={{
                      "Hôm nay": [dayjs().startOf("day"), dayjs().endOf("day")],
                      "Tuần này": [dayjs().startOf("week"), dayjs().endOf("week")],
                      "Tháng này": [dayjs().startOf("month"), dayjs().endOf("month")],
                    }}
                    format={settings.dateFormat}
                  />
                </div>
                <div className="filter-item">
                  <label>Khách hàng</label>
                  <br />
                  <Select
                    allowClear
                    disabled={!!query.contractId} // Disable khi đã chọn contract
                    placeholder="Chọn khách hàng"
                    className="w-[200px]"
                    options={customers.map((customer) => ({
                      value: customer.id,
                      label: customer.name,
                    }))}
                    onChange={(value) => {
                      setQuery({ ...query, page: 1, customerId: value });
                    }}
                    value={query.customerId}
                  />
                </div>
                <div className="filter-item">
                  <label>Hợp đồng</label>
                  <br />
                  <Select
                    allowClear
                    placeholder="Chọn hợp đồng"
                    className="w-[200px]"
                    showSearch
                    filterOption={(input, option) =>
                      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                    }
                    options={contractOptions.map((contract) => ({
                      value: contract.id,
                      label: contract.name || contract.code,
                    }))}
                    onChange={handleContractChange}
                    value={query.contractId}
                  />
                </div>
              </Space>
            </div>
          </Space>
        </div>

        <Spin spinning={loadingContract || loadingContractDetail}>
          <Table
            scroll={{ y: 400, x: 1200 }}
            size="small"
            locale={{ emptyText: "Không có dữ liệu" }}
            dataSource={transformContractData(contracts)}
            pagination={false}
            sticky
            bordered
          >
            <Column align="center" width={35} title="STT" dataIndex="stt" key="stt"
              render={renderSTT}
            />
            <Column title="Tên Khách hàng" dataIndex="customerName" key="customerName" width={150}
              render={(text, record: ContractReportData) => renderCell(text, record, "customerName")}
            />
            <Column title="Tên khách hàng cuối cùng" dataIndex="endCustomer" key="endCustomer" width={120}
              render={(text, record: ContractReportData) => renderCell(text, record, "endCustomer")}
            />
            <Column title="Số HĐ" dataIndex="contractNumber" key="contractNumber" width={120}
              render={(text, record: ContractReportData) => renderCell(text, record, "contractNumber")}
            />
            <Column title="Số PO" dataIndex="poNumber" key="poNumber" width={100}
              render={(text, record: ContractReportData) => renderCell(text, record, "poNumber")}
            />
            <Column title="Tên hàng" dataIndex="productName" key="productName" width={200} />
            <Column title="Quy cách" dataIndex="specification" key="specification" width={200}
              render={(text) => (
                <div style={{ whiteSpace: 'pre-wrap', maxHeight: '100px', overflow: 'auto' }}>{text}</div>
              )}
            />
            <Column title="Đóng gói" dataIndex="packing" key="packing" width={150} />
            <Column title="Điều khoản giao hàng" dataIndex="termsOfDelivery" key="termsOfDelivery" width={150}
              render={(text, record: ContractReportData) => renderCell(text, record, "termsOfDelivery")}
            />
            <Column title="Giá" key="price" dataIndex="price" width={100}
              render={(text, record: ContractReportData) => record.productName && <span>{`${formatVND(text)}$${renderUnit(record.unitId)}`}</span>}
            />
            <Column title="Thời gian giao hàng" dataIndex="deliveryTime" key="deliveryTime" width={150} />
          </Table>

          {/* Chỉ hiển thị pagination khi không có contract được chọn */}
          {!query.contractId && (
            <Pagination
              autoScrollToTop={false}
              total={totalContract}
              onChange={({ page, limit }) => setQuery({ ...query, page, limit })}
              defaultPageSize={query.limit}
              currentPage={query.page}
            />
          )}
        </Spin>

        <div className="text-center">
          <label style={{ fontSize: 18 }}>Báo cáo giá sản phẩm</label>
        </div>
      </div>
    </Card>
  );
};