import { PurchaseOrder } from "@/types/purchaseOrder";
import moment from "moment";
import { formatVND } from "..";
import { getLargeDate } from "../date";
import { CTY_KE_TOAN_MAIL } from "./ReportOrderComponent";
import { stringToDate, unixToDate } from "../dateFormat";
import {
  Contract,
  ContractDetail,
  ContractDetailETDType,
  ContractProduct,
  ContractUnits,
  Unit,
} from "@/types/contract";
import dayjs from "dayjs";
import React, { useMemo } from "react";
import { enterRow } from "./enterRow";
import getQuantityOfEachItemFromPO from "./getQuantity";

type UnitItem = {
  nameVi: string;
  nameEn: string;
  quantityOnUnit: number;
  contractQuantity: number;
};

// type for new contract data response

export const Highlight = ({
  isBold = false,
  children,
}: {
  isBold?: boolean;
  children: React.ReactNode;
}) => (
  <span className={["text-red-500", isBold ? "font-bold" : ""].join(" ")}>
    {children}
  </span>
);

export const PO_DETAIL_COLUMN_SIZE = 90;
export const RULE_CLASS_NAME = "mt-0";

export const ContractBodyPDF = ({
  data,
  printType,
}: {
  data: Contract;
  printType: "vi" | "en" | "all";
}) => {
  function getUnitName(item: UnitItem, printType: "vi" | "en" | "all") {
    if (printType === "vi") return item.nameVi;
    if (printType === "en") return item.nameEn;
    return `${item.nameVi} (${item.nameEn})`;
  }

  function renderPackingDescription(
    data: UnitItem[],
    printType: "vi" | "en" | "all" = "all"
  ) {
    // Sắp xếp từ cấp lớn đến nhỏ (theo contractQuantity, quantityOnUnit nếu có)
    const levels = [...data].sort((a, b) => {
      // Nếu có contractQuantity thì ưu tiên sắp xếp
      if (a.contractQuantity && !b.contractQuantity) return -1;
      if (!a.contractQuantity && b.contractQuantity) return 1;
      return 0;
    });

    const parts: string[] = [];

    for (let i = 0; i < levels.length; i++) {
      const current = levels[i];
      const parent = levels[i - 1];

      if (i === 0) {
        parts.push(
          `${current.contractQuantity} ${getUnitName(current, printType)}`
        );
      } else if (current.quantityOnUnit) {
        parts.push(
          `${current.quantityOnUnit} ${getUnitName(
            current,
            printType
          )}/${getUnitName(parent, printType)}`
        );
      }
    }

    // Tính tổng đơn vị nhỏ nhất
    let totalUnits = levels[0].contractQuantity;
    for (let i = 1; i < levels.length; i++) {
      totalUnits *= levels[i].quantityOnUnit || 1;
    }

    return `${parts.join(" × ")}`;
  }

  const productsGroupByName = useMemo(() => {
    const result: Record<string, { contractProducts: ContractProduct[] }> = {};

    data.contractDetails?.forEach((item) => {
      const name = item.productName.toLowerCase();
      if (!result[name]) {
        result[name] = {
          contractProducts: [],
        };
      }
      result[name].contractProducts.push(
        data.contractProducts?.find((it) => it.poCode === item.poCode)!
      );
    });

    return result;
  }, [data]);

  const TEXTS = {
    vi: {
      note: "Ghi chú",
      processProvidedBy: "Quy trình cung cấp bởi",
      producedBy: "Nơi sản xuất",
      destinationCountry: "Quốc gia nơi đến",
      salesInCharge: "Người liên hệ",
      processProvidedByValue: "QLCL",
      salesInChargeValue: "Ms. Đông",
      notFound: "Không tìm thấy",
      itemName: "Tên mặt hàng",
      processCode: "Mã quy trình",
      specifications: "Đặc điểm kỹ thuật",
      coldContainerThermometer: "Nhiệt kế cont lạnh (cái)",
      packing: "Đóng gói",
      quantity: "Số lượng",
      deliveryTime: "Thời gian giao hàng",
      serialNumber: "STT",
      container: "Thùng",
    },
    en: {
      note: "Note",
      processProvidedBy: "Process flowchart provided by",
      producedBy: "Produced by",
      destinationCountry: "Destination country",
      salesInCharge: "Contact Person",
      processProvidedByValue: "QLCL",
      salesInChargeValue: "Ms. Đông",
      notFound: "Not found",
      itemName: "Item name",
      processCode: "Process code",
      specifications: "Specifications",
      coldContainerThermometer: "Cold container thermometer (pcs)",
      packing: "Packing",
      quantity: "Quantity",
      deliveryTime: "Delivery time",
      serialNumber: "No",
      container: "Cartons",
    },
  };

  const t = (key: keyof typeof TEXTS.vi) => {
    if (printType === "vi") return TEXTS.vi[key];
    if (printType === "en") return TEXTS.en[key];
    if (printType === "all") return `${TEXTS.vi[key]} (${TEXTS.en[key]})`;
    return "";
  };

  const unitName = (unit: Unit) => {
    return ContractUnits.find((item) => item.id === unit?.id)?.nameEn;
  };

  const numberCarton = (data: ContractProduct) => {
    return Math.ceil((data?.quantity ?? 0) / Math.max(data.quantityUnit, 1));
  };

  return (
    <div className="po-inland-body text-[13px]">
      <div className="text-[13px] w-full border ">
        <table className="w-full border-collapse border border-black border-solid table-fixed text-lg">
          <thead>
            <tr>
              <th className="border border-black border-solid w-[40px]">
                {t("serialNumber")}
              </th>
              <th className="border border-black border-solid border-r-0 text-left">
                <span>{t("itemName")}</span>
              </th>
            </tr>
          </thead>
          <tbody>
            {Object.values(data.contractProducts)?.map((it, index) => (
              <>
                <tr>
                  <td
                    className="border border-black border-solid text-center align-center"
                    rowSpan={2}
                  >
                    {index + 1}
                  </td>
                  <td
                    colSpan={2}
                    className="text-left border border-black border-solid align-left"
                  >
                    <strong className="uppercase">{it.productName}</strong>
                  </td>
                </tr>
                <tr>
                  <td className="border border-black border-solid p-2 !w-[100px]">
                    <ul className="list-none mt-2">
                      <li>
                        * {t("processCode")}: <strong>{it.code}</strong>
                      </li>
                      <li>
                        * {t("specifications")}:{" "}
                        <strong>
                          {it.specification
                            ?.split("\n")
                            .map((line: any, index: number) => (
                              <React.Fragment key={index}>
                                {line}
                                <br />
                              </React.Fragment>
                            ))}
                        </strong>
                      </li>
                      <li>
                        * {t("coldContainerThermometer")}:{" "}
                        <strong>
                          {it.thermometer ? `${it.thermometer}` : "0"}
                        </strong>
                      </li>
                    </ul>
                  </td>
                  <td className="border border-black align-top p-0 w-1/2">
                    <table className="w-full border-collapse table-fixed text-[13px] text-lg">
                      <tbody>
                        <tr>
                          <td className="border border-black border-solid p-2 w-1/4 border-l-0">
                            <span>{t("packing")}</span>
                          </td>
                          <td className="border border-black border-solid p-2">
                            {it.packing}
                          </td>
                        </tr>
                        <tr>
                          <td className="border border-black border-solid p-2 border-l-0">
                            <span>{t("quantity")}</span>
                          </td>
                          <td className="border border-black border-solid p-2">
                            {formatVND(numberCarton(it) ?? 0)} {t("container")}{" "}
                            (±10%)
                            <br />
                          </td>
                        </tr>
                        <tr>
                          <td className="border border-black border-solid p-2 border-l-0 border-b-0">
                            <span>{t("deliveryTime")}</span>
                          </td>
                          <td className="border border-black border-solid p-2 border-b-0">
                            {(() => {
                              const etdGroups: Record<string, number> = {};

                              data?.contractPos.forEach((item) => {
                                const poDetails = item.poDetails?.filter(
                                  (po) => po.contractProduct.id === it.id
                                );

                                const sum = poDetails.reduce(
                                  (acc, po) => acc + po.quantity,
                                  0
                                );

                                if (sum === 0) return;

                                let key = "";

                                if (printType === "vi") {
                                  key = item.etdDetail || "N/A";
                                } else {
                                  key = item.etdDetailEn || "N/A";
                                }

                                const unit = Math.max(it.quantityUnit, 1);
                                etdGroups[key] =
                                  (etdGroups[key] || 0) + sum / unit;
                              });

                              return Object.entries(etdGroups).map(
                                ([etd, total], index) => (
                                  <p key={index}>
                                    - {formatVND(Math.ceil(total))}{" "}
                                    {t("container")}: {etd}
                                  </p>
                                )
                              );
                            })()}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </>
            ))}
          </tbody>
        </table>

        <div className="flex gap-2 ml-4">
          <div className="text-lg w-[100px]">
            <div>{t("note")}:</div>
          </div>
          <div className="w-full">
            <table
              border={1}
              cellPadding="2"
              style={{ borderCollapse: "collapse", width: "100%" }}
              className="text-lg"
            >
              <tbody>
                <tr>
                  <td style={{ width: "80%" }}>{t("processProvidedBy")}</td>
                  <td colSpan={2} style={{ width: "20%" }}>
                    {t("processProvidedByValue")}
                  </td>
                </tr>
                <tr>
                  <td style={{ width: "80%" }}>{t("producedBy")}</td>
                  <td colSpan={2} style={{ width: "40%" }}>
                    {data.productBy &&
                      Array.isArray(JSON.parse(data.productBy)) &&
                      JSON.parse(data.productBy)?.join(" - ")}
                  </td>
                </tr>
                <tr>
                  <td style={{ width: "60%" }}>{t("destinationCountry")}</td>
                  <td colSpan={2} style={{ width: "40%" }}>
                    {data.market?.name}
                  </td>
                </tr>
                <tr>
                  <td style={{ width: "60%" }}>{t("salesInCharge")}</td>
                  <td colSpan={2} style={{ width: "40%" }}>
                    {data.contactPersonName}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Chữ ký */}
      </div>
    </div>
  );
};
