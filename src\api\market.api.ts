import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const marketApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/market",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/market",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/market/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/market/${id}`,
      method: "delete",
    }),
};
