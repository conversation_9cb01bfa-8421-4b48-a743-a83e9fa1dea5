import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const productionOrderApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productionOrder",
      params,
    }),
  summaryStatus: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productionOrder/summary/status",
      params,
    }),
  findOne: (id: any, params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}`,
      params,
    }),
  followStaff: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/followStaff`,
      method: "patch",
      data,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productionOrder",
      data,
      method: "post",
    }),

  estimate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productionOrder/estimate",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}`,
      method: "patch",
      data,
    }),
  processing: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/processing`,
      method: "patch",
      data,
    }),
  complete: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/complete`,
      method: "patch",
      data,
    }),
  cancel: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/cancel`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}`,
      method: "delete",
    }),
  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/approve`,
      method: "patch",
      data,
    }),

  reject: (id: number, data?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productionOrder/${id}/reject`,
      method: "delete",
      data,
    }),
};
