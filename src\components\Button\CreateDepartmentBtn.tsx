import {
  DepartmentModal,
  DepartmentModalRef,
} from "@/views/Department/components/DepartmentModal";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useRef } from "react";

type Props = {
  onSuccess: () => void;
};

const CreateDepartmentBtn = ({ onSuccess }: Props) => {
  const modal = useRef<DepartmentModalRef>();
  const handleOnOpenCreateModal = () => {
    modal.current?.handleOpen("create");
  };

  return (
    <>
      <Button
        title="Thêm phòng ban"
        onClick={handleOnOpenCreateModal}
        type="primary"
        size="small"
      >
        <PlusOutlined></PlusOutlined>
      </Button>
      <DepartmentModal ref={modal} onSubmitOk={onSuccess}></DepartmentModal>
    </>
  );
};

export default CreateDepartmentBtn;
