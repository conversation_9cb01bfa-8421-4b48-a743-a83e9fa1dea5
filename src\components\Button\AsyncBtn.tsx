import { Button, ButtonProps, Popconfirm } from "antd";
import { useState } from "react";

type Props = {
  onSubmit: () => Promise<void>;
  typeText?: string;
};

const AsyncBtn = ({
  onSubmit,
  onClick,
  typeText = "đề nghị",
  children,
  ...props
}: Props & ButtonProps) => {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      await onSubmit();
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  return (
    <Popconfirm
      placement="topLeft"
      title={`Xác nhận đóng ${typeText} này?`}
      okText="Ok"
      cancelText="Không"
      onConfirm={handleClick}
    >
      <Button loading={loading} {...props}>
        {children}
      </Button>
    </Popconfirm>
  );
};

export default AsyncBtn;
