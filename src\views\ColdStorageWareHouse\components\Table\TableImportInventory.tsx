import { departmentApi } from "@/api/department.api";
import { productApi } from "@/api/product.api";
import { proposalApi } from "@/api/proposal.api";
import { UploadExcelLocal } from "@/components/FileUpload/UploadLocal";
import { InputNumberVN } from "@/components/Input/InputNumberVN";
import { ProductSelectorRef } from "@/components/ProductSelector/ProductSelector";
import { useDepartment } from "@/hooks/useDepartMent";
import { useProduct } from "@/hooks/useProduct";
import { useProposal } from "@/hooks/useProposal";
import { useTaxConfig } from "@/hooks/useTaxConfig";
import { useUnit } from "@/hooks/useUnit";
import { Department, DepartmentType } from "@/types/department";
import { ModalStatus } from "@/types/modal";
import { Product, Module } from "@/types/product";
import {
  Proposal,
  ProposalDetail,
  ProposalStatus,
  ProposalType,
} from "@/types/proposal";
import {
  PurchaseOrder,
  PurchaseOrderDetail,
  PurchaseOrderType,
} from "@/types/purchaseOrder";
import { formatVND } from "@/utils";
import { getDateExcel } from "@/utils/dateFormat";
import purchaseExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/purchaseExportDemol";
import { calcMoneyItem2 } from "@/utils/money";
import { getFinalNumber } from "@/utils/number";
import {
  getBaoBiProposalLink,
  getDepartmentLink,
  getProposalLink,
} from "@/utils/url";
import { ProductItem } from "@/views/Product/components/ProductItem";
import {
  ProductModal,
  ProductModalRef,
} from "@/views/Product/components/ProductModal";
import {
  CopyOutlined,
  DeleteOutlined,
  FileFilled,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Button,
  DatePicker,
  Empty,
  Input,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from "antd";
import Column from "antd/es/table/Column";
import clsx from "clsx";
import dayjs from "dayjs";
import { debounce, isNumber } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { purchaseApi } from "@/api/purchase.api";
import { Quote, QuoteDetail } from "@/types/quote";
import { quoteApi } from "@/api/quote.api";
import quoteExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/quoteExportDemo";
import { StockSelector } from "@/components/Selector/StockSelector";
import { ColdStorageSelector } from "@/components/Selector/ColdStorageSelector";
import { useStock } from "@/hooks/useStock";
import { Stock } from "@/types/stock";
import { InventoryDetail, InventoryType } from "@/types/inventoryDetail";
import { settings } from "../../../../../settings";
import coldStorageProductExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/coldStorageProductExportDemo";
import { stockApi } from "@/api/stock.api";
import { useContract } from "@/hooks/useContract";
import {
  ContractDetail,
  ContractPackingDetail,
  ContractUnits,
} from "@/types/contract";

export interface TableImportInventoryRef {
  getValue: () => InventoryDetail[];
  setValue: (inventoryDetails: InventoryDetail[]) => void;
  refetchTableData?: () => void;
}
export interface TableImportInventoryDetails {
  products: Partial<Product>[];
  quantity: number;
  realQuantity: number;
}
export const TableImportInventory = observer(
  React.forwardRef(
    (
      {
        readOnly = false,
        disableAction = false,
        status,
        hiddenPrivateInfo = false,
        onRefresh,
        visibleDelete,
        contractId,
        type,
        typeContract = false,
      }: {
        readOnly?: boolean;
        disableAction?: boolean;
        status: ModalStatus;
        hiddenPrivateInfo: boolean;
        onRefresh?: () => void;
        visibleDelete?: boolean;
        isRefQuote?: boolean;
        contractId?: number;
        type?: InventoryType;
        typeContract?: boolean;
      },
      ref
    ) => {
      const [data, setData] = useState<Partial<InventoryDetail>[]>([]);

      const { fetchData, units } = useUnit({
        initQuery: { page: 1, limit: 0, module: Module.Freeze },
      });
      console.log("ContractId nhận đc là", contractId);
      const productModalRef = useRef<ProductModalRef>();
      // const quantityLogModalRef = useRef<QuantityLogModalRef>();
      const productSelectorRef = useRef<ProductSelectorRef>();
      const [isCheckAll, setIsCheckAll] = useState<boolean>(false);
      const [quote, setQuote] = useState<Quote>();
      // const modalEditNoteRef = useRef<ModalEditNoteRef>();
      const {
        fetchData: fetchProducts,
        fetchDataIsContract,
        products,
        loading,
        query,
        total,
        setProducts,
      } = useProduct({
        initQuery: {
          page: 1,
          limit: 50,
          module: Module.Freeze,
          isContract: false,
        },
      });
      const {
        fetchData: fetchColdStock,
        loading: loadingColdStock,
        query: queryColdStock,
        stocks,
      } = useStock({
        initQuery: { page: 1, limit: 50, module: Module.Freeze },
      });

      const { fetchOne, fetchOneLoading, selectedContract } = useContract({
        initQuery: { page: 1, limit: 50, module: Module.Freeze },
      });

      const [loadingTable, setLoadingTable] = useState(false);

      const moneyValue = useRef({
        moneyTax: 0,
        subTotal: 0,
        amount: 0,
      });

      const groupByPoCode = (items: ContractDetail[]): ContractDetail[] => {
        const grouped = Object.values(
          items.reduce<Record<string, ContractDetail>>((acc, item) => {
            const key = item.productName;
            if (!acc[key]) {
              acc[key] = { ...item };
            } else {
              acc[key].quantity += item.quantity;
            }
            return acc;
          }, {})
        );
        return grouped;
      };

      useEffect(() => {
        fetchData();
        fetchColdStock();
      }, []);

      useEffect(() => {
        fetchDataIsContract(contractId);
      }, [contractId]);

      console.log("products", products);

      useEffect(() => {
        const fetchData = async () => {
          if (
            data &&
            Array.isArray(data) &&
            data.length > 0 &&
            status === "update"
          ) {
            setData(data);
          } else if (data && contractId) {
            const contract = await fetchOne(contractId);
            if (
              contract &&
              contract.contractProducts &&
              contract.contractProducts.length > 0
            ) {
              const finalData = contract.contractProducts.map(
                (item: any, index: number) => {
                  const id = products.find(
                    (product) => product.name === item?.productName
                  )?.id;

                  const itemUnit = ContractUnits.find(
                    (contract) => contract.id === item.unit?.id
                  );

                  return {
                    product: {
                      ...item,
                      id: item.id,
                      name: item.productName,
                      code: "",
                      key: index,
                    },
                    unit: itemUnit ?? units?.[0],
                    productId: id,
                    unitId: itemUnit?.id ?? units?.[0]?.id,
                    quantity: 0,
                    // date: dayjs().unix()
                  };
                }
              );
              setData(finalData);
            } else {
              setData([]);
            }
          }
        };

        fetchData();
      }, [products]);

      const handlePaste = (e: any) => {
        return message.warning("Đang phát triển");
        if (e.target.tagName && e.target.tagName.match(/(input|textarea)/i)) {
          // Do not handle past when an input element is currently focused
          return;
        }
        // Get clipboard data as text
        const clipboardData = e.clipboardData.getData("text");

        // Simplified parsing of the TSV data with hard-coded columns
        const rows = clipboardData.replaceAll("\r", "").split("\n");
        handleFillTable(rows);
      };

      const handleFillTable = async (rows: any) => {
        const res = await productApi.findAll({ page: 1, limit: 100 });
        const products = res.data.products;
        const result = rows.map((row: any) => {
          const cells = row.split("\t");
          const productName = cells[0];

          const findProduct = products.find(
            (product: Product) => product.name == productName
          );
          const findUnit = units.find((item) => item.name == cells[2]);

          return {
            name: productName,
            description: cells[1],
            unitId: findUnit?.id || undefined,
            quantity: cells[3],
            // dateMoment: cells[4] ? dayjs(cells[4], "DD/MM/YYYY") : undefined,
            productId: findProduct?.id || undefined,
          };
        });

        setData([...data, ...result]);
      };

      useImperativeHandle(
        ref,
        () => {
          return {
            getValue() {
              return data;
            },
            setValue(inventoryDetails: InventoryDetail[]) {
              const finalData = inventoryDetails?.map((item) => ({
                ...item,
                productId: item?.product?.id,
                unitId: item?.unit?.id,
                stockId: item?.stock?.id,
              }));

              //@ts-ignore
              setData(finalData);
              // setQuote(quote);
              // setIsCheckAll(isCheckAll);
              // fetchData(quote?.quoteDetails.map((item) => item.unit));
              // fetchProducts(quote?.quoteDetails.map((item) => item.product));
            },
            refetchTableData: () => {
              setData([]);
            },
          };
        },
        [data]
      );
      const addRow = () => {
        setData([...data, { quantity: 1 }]);
      };

      const handleImportExcel = async (excelData: any[]) => {
        console.log(excelData);

        const codes = excelData.map((item) =>
          item["Mã hàng"]?.toString()?.trim()
        );
        console.log({ codes });
        const promises = [productApi.codes({ codes })];

        const [resProduct, resDepartment] = await Promise.all(promises);

        const products = resProduct.data.products;
        if (products.length) {
          fetchProducts(products);
        }
        // const stock = await stockApi
        //@ts-ignore
        const result: PurchaseOrderDetail[] = excelData.map((item) => {
          const productName: string = item["Mã hàng"] || "";
          const quantity: number = (item["Số lượng"] as number) || 0;
          console.log(item);
          const findProduct = products.find(
            (product: Product) =>
              product.code.toLowerCase() ==
              productName?.toString()?.trim().toLowerCase()
          );

          // const findStock = stocks?.find((s) => (s.id = item["Mã kho lạnh"]));
          console.log({ stocks });
          // console.log({ findStock });
          const dateOfImport =
            type == InventoryType.In
              ? item["Ngày nhập kho"]
              : item["Ngày xuất kho"];
          const convertDate = dayjs(dateOfImport, "DD/MM/YYYY");
          return {
            description: item["Diễn giải"],
            productId: findProduct ? findProduct.id : undefined,
            name: productName ? productName : undefined,
            product: findProduct,
            unitId: findProduct?.unit?.id,
            unit: findProduct?.unit,
            quantity: item["Số lượng"],
            date: convertDate,
            // stockId: findStock?.id,
            // stock: findStock,
          };
        });
        const currentData = data || [{}];
        //@ts-ignore
        setData([...currentData, ...result]);
      };

      // const handleGetOneProposal = async (id: number) => {
      //   const res = await proposalApi.findOne(id);
      //   const finalData = res.data.proposalDetails.map(
      //     (item: any, index: number) => ({
      //       ...item,
      //       key: index,
      //       dateMoment: dayjs(item.date, "YYYY-MM-DD"),
      //       providerId: item?.provider?.id || item.providerId,
      //     })
      //   );

      //   setData(finalData);
      // };

      // const calcMoneyItem = (row: PurchaseOrderDetail) => {
      //   row.subTotalPrice = getFinalNumber(
      //     (row.quantity || 0) * (row.price || 0)
      //   );
      //   row.moneyTax = getFinalNumber(
      //     ((row.taxPercent || 0) / 100) * row.subTotalPrice
      //   );
      //   row.totalPrice = getFinalNumber(row.subTotalPrice + row.moneyTax);
      // };

      /**
       * Mỗi lần đổi department là fetch lại danh sách
      //  */
      // const onOpenProposalSelect = (
      //   isOpen: boolean,
      //   currentDepartment: Department
      // ) => {
      //   if (!currentDepartment && !isBaoBi) {
      //     return message.info("Vui lòng chọn phòng ban đặt hàng trước");
      //   }
      //   if (isOpen) {
      //     queryProposal.departmentId = currentDepartment?.id;
      //     fetchProposals();
      //   }
      // };

      const handleDeleteDetail = (row: QuoteDetail) => {
        Modal.confirm({
          title: "Xác nhận xóa?",
          content: `Xác nhận xóa "${row.product?.code}"?`,
          cancelText: "Không",
          okText: "Xóa",
          okType: "danger",
          async onOk() {
            try {
              setLoadingTable(true);
              await quoteApi.deleteDetail(row.id);
              onRefresh?.();
              message.success("Xóa thành công");
            } finally {
              setLoadingTable(false);
            }
          },
        });
      };

      const debounceProductSearch = useCallback(
        debounce((keyword) => {
          query.search = keyword;
          fetchDataIsContract(contractId);
        }, 300),
        [query]
      );
      const getImportFile = async () => {
        // const promises = [
        //   productApi.findAll({ page: 1, limit: 100, isBlocked: false }),
        //   departmentApi.findAll({ page: 1, limit: 100 }),
        // ];

        // const [resProduct, resDepartment] = await Promise.all(promises);

        coldStorageProductExportDemo();
      };

      // const openProposal = (proposal: Proposal) => {
      //   if (isBaoBi) {
      //     window.open(getBaoBiProposalLink("view", proposal));
      //   } else {
      //     window.open(getProposalLink("update", proposal));
      //   }
      // };

      const createNewProduct = () => {
        productModalRef.current?.handleOpen("create");
      };
      return (
        <>
          <Space
            style={{
              marginTop: "10px",
              marginBottom: "10px",
              width: "100%",
              justifyContent: readOnly ? "end" : "space-between",
              alignItems: "start",
            }}
          >
            {!readOnly && (
              <Space hidden={true} align="start">
                <UploadExcelLocal
                  onUploadOK={handleImportExcel}
                  sheetPosition={0}
                />
                <Button
                  onClick={() => {
                    getImportFile();
                  }}
                  icon={<FileFilled />}
                >
                  Tải file import mẫu
                </Button>
              </Space>
            )}
          </Space>

          <div onPaste={handlePaste}>
            <Table
              className="table-striped-rows"
              tableLayout="fixed"
              bordered
              loading={loadingTable && fetchOneLoading}
              dataSource={data}
              size="small"
              pagination={false}
              locale={{
                emptyText: readOnly
                  ? "Danh sách trống"
                  : `Chọn vào đây và paste dữ liệu tương đương hoặc ấn vào dấu "+" để thêm dòng`,
              }}
              scroll={{ x: "max-content", y: 400 }}
            >
              {!readOnly && (
                <Column
                  align="center"
                  width={80}
                  fixed="left"
                  title={
                    <Tooltip placement="top" title="Thêm dòng">
                      <Button
                        className="bg-black"
                        type="primary"
                        ghost
                        icon={<PlusOutlined />}
                        onClick={addRow}
                      ></Button>
                    </Tooltip>
                  }
                  key="name"
                  render={(text, record: QuoteDetail, index: number) => (
                    <div className="flex gap-2 justify-center">
                      <Popconfirm
                        title="Dòng này sẽ bị xóa. Tiếp tục?"
                        onConfirm={() => {
                          data.splice(index, 1);
                          setData([...data]);
                        }}
                      >
                        <DeleteOutlined className="text-red-500 text-lg" />
                      </Popconfirm>

                      <CopyOutlined
                        onClick={() => {
                          const newRecord = { ...record, id: undefined };
                          data.push(newRecord);
                          setData([...data]);
                        }}
                        className="text-blue-500 text-lg"
                      />
                    </div>
                  )}
                />
              )}
              <Column
                width={50}
                title="STT"
                align="center"
                key="stt"
                render={(text, record, index) => <>{index + 1}</>}
              />
              <Column
                width={200}
                title={
                  <Space className="w-full justify-between">
                    <div className="flex">
                      Tên kho lạnh <span className="text-red-500">*</span>
                    </div>{" "}
                    {/* {!readOnly && (
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlusOutlined />}
                        onClick={createNewProduct}
                      ></Button>
                    )} */}
                  </Space>
                }
                key="stockId"
                render={(text, record: ProposalDetail) =>
                  readOnly ? (
                    <div>
                      {record.stock?.code}-{record.stock?.name}
                    </div>
                  ) : (
                    <>
                      {console.log("record stock", record.stock)}

                      <Select
                        allowClear
                        onChange={(id, option: any) => {
                          record.stockId = id;
                          record.stock = option?.stock;
                          setData([...data]);
                        }}
                        showSearch
                        filterOption={false}
                        onSearch={debounceProductSearch}
                        style={{ width: "200px", maxWidth: 200 }}
                        options={stocks.map((item) => ({
                          // label: `${item?.code} - ${item?.name}`,
                          label: `${item.code}-${item?.name}`,
                          value: item?.id,
                          stock: item,
                        }))}
                        value={record.stock?.id}
                        popupMatchSelectWidth={false}
                      />
                    </>

                    // <ColdStorageSelector
                    // allowClear
                    // onChange={(id:number, option: any) => {
                    //   record.productId = id;
                    //   record.product = option?.product;
                    //   record.unit = option?.product?.unit;
                    //   record.unitId = option?.product?.unit?.id;
                    //   record.description = option.product.note || "";
                    //   setData([...data]);
                    // }}
                    // />
                    // <Input
                    //   readOnly={readOnly}
                    //   value={record?.name}
                    //   onChange={(e) => {
                    //     record.name = e.target.value;
                    //     setData([...data]);
                    //   }}
                    // />
                  )
                }
              />
              <Column
                width={150}
                title={
                  <Space className="w-full justify-between">
                    <div>
                      Mã hàng
                      <span className="text-red-500">*</span>
                    </div>{" "}
                  </Space>
                }
                key="age"
                render={(text, record: QuoteDetail) => {
                  let product;
                  if (status === "view") {
                    product = products.find(
                      (item) => item?.id === record?.product?.id
                    );
                  } else {
                    product = products.find(
                      (item) =>
                        item?.contractProduct?.id === record?.product?.id
                    );
                  }

                  return (
                    <span className="max-w-[150px]">{product?.code ?? ""}</span>
                  );
                }}
              />
              <Column
                width={250}
                title={
                  <Space className="w-full justify-between">
                    <div className="flex">
                      Tên hàng <span className="text-red-500">*</span>
                    </div>{" "}
                    {!readOnly && (
                      <Button
                        type="primary"
                        size="small"
                        icon={<PlusOutlined />}
                        onClick={createNewProduct}
                      ></Button>
                    )}
                  </Space>
                }
                render={(text, record: ProposalDetail) => {
                  return readOnly ? (
                    <ProductItem productItem={record.product} />
                  ) : (
                    <Select
                      allowClear
                      onChange={(id, option: any) => {
                        record.productId = id;
                        record.product = { ...option?.product, id: id };
                        // record.unit = units?.[0];
                        // record.unitId = units?.[0]?.id;
                        setData([...data]);
                      }}
                      showSearch
                      filterOption={false}
                      onSearch={debounceProductSearch}
                      style={{ width: "100%", maxWidth: 250 }}
                      options={products.map((item) => ({
                        label: `${item?.code} - ${item?.name}`,
                        value: item?.contractProduct?.id,
                        product: item,
                      }))}
                      value={record.product?.id}
                      popupMatchSelectWidth={false}
                    />
                    // <Input
                    //   readOnly={readOnly}
                    //   value={record?.name}
                    //   onChange={(e) => {
                    //     record.name = e.target.value;
                    //     setData([...data]);
                    //   }}
                    // />
                  );
                }}
              />

              {/* {typeContract &&
                <Column
                  width={175}
                  title="Tên hàng trong hợp đồng"
                  key="age"
                  render={(text, record: QuoteDetail) => record?.productNameContract}
                />
              } */}
              <Column
                width={100}
                title={<>Đơn vị tính</>}
                key="unit"
                render={(text, record: InventoryDetail) => {
                  return readOnly ? (
                    <>
                      {
                        ContractUnits.find((ut) => ut.id === record.unit?.id)
                          ?.nameEn
                      }
                    </>
                  ) : (
                    <Select
                      allowClear
                      onChange={(id, option: any) => {
                        record.unit = option?.unit;
                        record.unitId = id;
                        setData([...data]);
                      }}
                      showSearch
                      filterOption={false}
                      onSearch={debounceProductSearch}
                      style={{ width: "100%" }}
                      options={ContractUnits.map((item) => ({
                        label: item?.nameEn,
                        value: item?.id,
                        unit: item,
                      }))}
                      value={record.unit?.id}
                      popupMatchSelectWidth={false}
                    />
                  );
                }}
              />
              <Column
                width={150}
                title={
                  <div>
                    {`Ngày ${type == InventoryType.In ? "nhập" : "xuất"} kho`}{" "}
                    <span className="text-red-500">*</span>
                  </div>
                }
                dataIndex="age"
                key="age"
                render={(value, record: InventoryDetail) =>
                  readOnly ? (
                    dayjs(record.date).isValid() &&
                    dayjs(record.date).format(settings.dateFormat)
                  ) : (
                    <DatePicker
                      allowClear={false}
                      style={{ width: "100%" }}
                      format={settings.dateFormat}
                      value={
                        record.date && dayjs(record.date).isValid()
                          ? dayjs(record.date, "YYYY-MM-DD")
                          : undefined
                      }
                      onChange={(v) => {
                        record.date = v?.format("YYYY-MM-DD") || "";
                        setData([...data]);
                      }}
                    />
                  )
                }
              />

              {/* <Column
                width={150}
                title="Diễn giải"
                key="note"
                render={(text, record: InventoryDetail) =>
                  readOnly ? (
                    record.note
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.note}
                      onChange={(e) => {
                        record.note = e.target.value;
                        setData([...data]);
                      }}
                    />
                  )
                }
              /> */}

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Trọng lượng <span className="text-red-500">*</span>
                  </>
                }
                key="weight"
                render={(text, record: PurchaseOrderDetail) =>
                  readOnly ? (
                    formatVND(record.weight)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.weight}
                      onChange={(value) => {
                        record.weight = value;
                        setData([...data]);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Số lượng <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={(text, record: PurchaseOrderDetail) =>
                  readOnly ? (
                    formatVND(record.quantity)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.quantity}
                      onChange={(value) => {
                        record.quantity = value;
                        record.realQuantity = value;
                        setData([...data]);
                      }}
                    />
                  )
                }
              />
              <Column
                width={150}
                title="Ghi chú"
                key="note"
                render={(text, record: InventoryDetail) =>
                  readOnly ? (
                    record.note
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.note}
                      onChange={(e) => {
                        record.note = e.target.value;
                        setData([...data]);
                      }}
                    />
                  )
                }
              />

              {/* <Column
                align="right"
                width={120}
                title={
                  <>
                    Số lượng thực tế <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={(text, record: InventoryDetail) =>
                  readOnly ? (
                    formatVND(record.quantity)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.realQuantity}
                      onChange={(value) => {
                        record.realQuantity = value;
                        setData([...data]);
                      }}
                    />
                  )
                }
              /> */}
            </Table>
          </div>

          <ProductModal
            ref={productModalRef}
            onSubmitOk={() => {
              productSelectorRef.current?.refreshData();
              fetchDataIsContract(contractId);
            }}
          />
          {/* <QuantityLogModal
            ref={quantityLogModalRef}
            onSubmitOk={() => onRefresh?.()}
          />

          <ModalEditNote
            ref={modalEditNoteRef}
            onClose={() => ""}
            onSubmitOk={() => ""}
          /> */}
        </>
      );
    }
  )
);
