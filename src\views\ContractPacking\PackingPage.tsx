import {
  CopyOutlined,
  DeleteOutlined,
  ExportOutlined,
  EyeOutlined,
  FolderFilled,
} from "@ant-design/icons";
import {
  Badge,
  Button,
  DatePicker,
  Popconfirm,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from "antd";
import { debounce } from "lodash";
import { useCallback, useEffect, useRef, useState } from "react";

import { proposalApi } from "@/api/proposal.api";
import { Pagination } from "@/components/Pagination";
import SearchProposal from "@/components/SearchProposal/SearchProposal";
import TableContent from "@/components/Table/TableContent";
import { useProposal } from "@/hooks/useProposal";
import { userStore } from "@/store/userStore";
import {
  Proposal,
  ProposalStatus,
  ProposalStatusObject,
  ProposalStatusTrans,
  ProposalType,
} from "@/types/proposal";
import { getTitle } from "@/utils";
import { checkRole } from "@/utils/checkRole";
import {
  onRangeChangeUnix,
  rangePresets,
  unixToFullDate,
} from "@/utils/dateFormat";
import { ProposalLocal } from "@/utils/proposalLocal";
import { FilterProposal } from "@/views/SuggestionPage/SuggestionPage";
import ModalSuggestionDetail, {
  modalDetailRef,
} from "@/views/SuggestionPage/components/ModalSuggestionDetail";

import { DraffModal, DraffModalRef } from "@/components/DraffModal/DraffModal";
import FilterTable from "@/components/Filter/FilterTable";
import { getInitQueryForPackageModule } from "@/hooks/useProductionOrder";
import { useStaff } from "@/hooks/useStaff2";
import { RangeValue } from "@/types/antd";
import { getExportData } from "@/utils/MyExcel";
import { exportPackageProposal } from "@/utils/proposal/exportPackageProposal";
import { Dayjs } from "dayjs";
import {
  PackageModal,
  PackageModalRef,
} from "./components/components/PackageModal";
import { settings } from "../../../settings";
import CreatePackageProposal from "./components/components/CreatePackageProposal";
import { useSearchParams } from "react-router-dom";
import { exportPackageContract } from "@/utils/proposal/exportPackageContract";
import { observer } from "mobx-react";

const { Column } = Table;

export const PackagePage = observer(({ title = "" }: { title?: string }) => {
  const [pageState, setPageState] = useState(false);
  const {
    fetchData: fetchStaff,
    staffs,
    query: queryStaff,
  } = useStaff({
    initQuery: {
      page: 1,
      limit: 20,
    },
  });
  const [searchParams, setSearchParams] = useSearchParams();
  const { fetchData, proposals, loading, query, total } = useProposal({
    initQuery: {
      page: 1,
      limit: 50,
      types: [ProposalType.Contract],
      // isHiddenComplete: true,
      // status: ProposalStatus.New,
    },
  });

  const [summaryByStatuses, setSummaryByStatuses] = useState<
    {
      label: string;
      total: number;
      visible: boolean;
      bgColor: string;
      status: ProposalStatus | undefined;
    }[]
  >(() => {
    return [
      {
        label: "Tất cả",
        total: 0,
        visible: true,
        bgColor: "",
        status: undefined,
      },
      {
        label: ProposalStatusTrans[ProposalStatus.New],
        total: 0,
        visible: true,
        bgColor: ProposalStatusObject[ProposalStatus.New].bgColor,
        status: ProposalStatus.New,
      },
      {
        label: ProposalStatusTrans[ProposalStatus.Processing],
        total: 0,
        visible: true,
        bgColor: ProposalStatusObject[ProposalStatus.Processing].bgColor,
        status: ProposalStatus.Processing,
      },

      {
        label: ProposalStatusTrans[ProposalStatus.Reject],
        total: 0,
        visible: true,
        bgColor: ProposalStatusObject[ProposalStatus.Reject].bgColor,
        status: ProposalStatus.Reject,
      },

      {
        label: ProposalStatusTrans[ProposalStatus.Approved],
        total: 0,
        visible: true,
        bgColor: ProposalStatusObject[ProposalStatus.Approved].bgColor,
        status: ProposalStatus.Approved,
      },
      {
        label: ProposalStatusTrans[ProposalStatus.Complete],
        total: 0,
        visible: true,
        bgColor: ProposalStatusObject[ProposalStatus.Complete].bgColor,
        status: ProposalStatus.Complete,
      },
    ];
  });

  const fetchSummary = async () => {
    const summaryData = await proposalApi
      .summary({ ...query, ...getInitQueryForPackageModule() })
      .then(({ data }) => data as { status: ProposalStatus; total: number }[]);
    setSummaryByStatuses((prev) => {
      prev.forEach((summary) => {
        const findStatus = summaryData.find(
          (item) => item.status == summary.status
        );
        if (findStatus) {
          summary.total = findStatus.total;
        } else {
          summary.total = 0;
        }
      });

      return [...prev];
    });
  };

  useEffect(() => {
    fetchStaff();
  }, []);
  useEffect(() => {
    if (proposals) {
      fetchSummary();
    }

    return () => {};
  }, [proposals]);

  useEffect(() => {
    query.isGetOwn = userStore.managerProductionRole.xemTatCaBaoBi
      ? false
      : userStore.managerProductionRole.xemTatCaBaoBiDuocTag;
  }, [userStore.managerProductionRole]);

  const draffModalRef = useRef<DraffModalRef>();

  const deleteRole = checkRole(
    "delete-proposal",
    userStore.info.role?.permissions
  );

  const proposalModalRef = useRef<PackageModalRef>();

  const proposalDetailModal = useRef<modalDetailRef>();

  useEffect(() => {
    document.title = getTitle(title);
    fetchData();
    const contractId = searchParams.get("contractId");
    const contractDetailId = searchParams.get("contractDetailId");
    if (contractId && contractDetailId) {
      proposalModalRef.current?.handleOpen(
        "copy",
        undefined,
        +contractId,
        +contractDetailId
      );
      searchParams.delete("contractId");
      searchParams.delete("contractDetailId");
      setSearchParams(searchParams);
    }
  }, []);

  const debounceSearch = useCallback(
    debounce((keyword: string) => {
      query.page = 1;
      query.search = keyword;
      fetchData();
    }, 300),
    []
  );

  const handleDelete = async (id: number) => {
    await proposalApi.delete(id);
    message.success("Đã xóa");
    fetchData();
  };

  const handleChangeFilterGroup = (
    value: boolean,
    type:
      | "myProposal"
      | "isNeedInspec"
      | "isNeedFollow"
      | "isOverdue"
      | "isHiddenComplete"
  ) => {
    Object.assign(query, {
      page: 1,
    });
    if (type != "myProposal") {
      Object.assign(query, {
        [type]: value ? true : undefined,
      });

      if (type == "isNeedFollow" || type == "isNeedInspec") {
        let check = type == "isNeedFollow" ? "isNeedInspec" : "isNeedFollow";
        Object.assign(query, {
          staffId: value || query[check] ? userStore.info.id : undefined,
        });
      }
    } else {
      Object.assign(query, {
        createdStaffId: value ? userStore.info.id : undefined,
      });
    }
    fetchData();
  };

  const onTableChange = useCallback(
    (page: number, filters: any) => {
      const staff = filters?.staffName?.[0] || "";
      const status = filters?.status?.[0] || "";

      query.createdStaffId = staff || undefined;
      query.status = status;
      query.page = 1;

      fetchData();
    },
    [query]
  );

  const handleChangeDate = useCallback(
    (value: RangeValue<Dayjs>) => {
      const [fromAt, toAt] = onRangeChangeUnix(value);
      query.fromAt = fromAt;
      query.toAt = toAt;
      query.page = 1;
      fetchData();
    },
    [query]
  );

  return (
    <div className="shadow-sm p-4 bg-white rounded-sm">
      <div className="filter-container mb-1">
        <Space wrap align="end">
          <div className="flex md:gap-2 flex-col md:flex-row items-end">
            <SearchProposal onChange={debounceSearch} />
            <div className="flex items-end gap-2">
              <div>
                <p className="font-semibold mb-1">Ngày tạo</p>
                <DatePicker.RangePicker
                  presets={rangePresets}
                  format={settings.dateFormat}
                  onChange={handleChangeDate}
                />
              </div>
            </div>
          </div>
          {userStore.managerProductionRole.taoBaoBi && (
            <CreatePackageProposal
              onCreateProposal={(proposal) => {
                proposalModalRef.current?.handleOpen(
                  "copy",
                  proposal as Proposal
                );
              }}
              onUpdateProposal={(proposal) => {
                proposalModalRef.current?.handleOpen(
                  "update",
                  proposal as Proposal
                );
              }}
            />
          )}

          {ProposalLocal.getData(false).filter(
            (v: Proposal) => v.type == ProposalType.BaoBi
          )?.length > 0 && (
            <Badge
              count={
                ProposalLocal.getData(false).filter(
                  (v: Proposal) => v.type == ProposalType.BaoBi
                )?.length
              }
            >
              <Button
                icon={<FolderFilled />}
                type="primary"
                onClick={() => draffModalRef.current?.handleOpen()}
              >
                Danh sách lưu nháp
              </Button>
            </Badge>
          )}

          <Popconfirm
            onConfirm={async () => {
              console.log(query);

              // const data = await getExportData<Proposal>({
              //   api: proposalApi.findAll,
              //   dataField: "proposals",
              //   limit: query.limit,
              //   query: {
              //     types: [ProposalType.Contract],
              //     isMapInspec: true,
              //     // ...getInitQueryForPackageModule(),
              //     ...query,
              //   },
              // });

              const { data } = await proposalApi.findAll({
                ...query,
                types: [ProposalType.Contract],
                isMapInspec: true,
              });

              await exportPackageContract(data.proposals);
            }}
            title="Bạn có chắc chắn muốn tiếp tục thao tác?"
          >
            <Button type="primary">
              <ExportOutlined></ExportOutlined>
              Xuất Excel
            </Button>
          </Popconfirm>
        </Space>
        <FilterProposal
          className="!mt-4 !mb-0"
          handleChangeMyProposal={(checked) => {
            handleChangeFilterGroup(checked, "myProposal");
          }}
          // handleChangeNeedInspec={(checked) => {
          //   handleChangeFilterGroup(checked, "isNeedInspec");
          // }}
          handleChangeFollow={(checked) => {
            handleChangeFilterGroup(checked, "isNeedFollow");
          }}
          // handleChangeOverdue={(checked) => {
          //   handleChangeFilterGroup(checked, "isOverdue");
          // }}
          // handleChangeCompleteProposal={(checked) => {
          //   handleChangeFilterGroup(checked, "isHiddenComplete");
          // }}
          myProposalCheck={query.createdStaffId == userStore.info.id}
          isNeedInspec={query.isNeedInspec}
          isNeedFollow={query.isNeedFollow}
          isOverdue={query.isOverdue}
          isHiddenComplete={query.isHiddenComplete}
        />
      </div>

      {/* <Tabs
          onChange={(v) => {
            query.status = v;
            query.page = 1;
            fetchData();
          }}
          activeKey={query.status}
        >
          {summaryByStatuses.map((v) => (
            <Tabs.TabPane
              tab={
                <Space>
                  {v.label}
                  <Badge
                    style={{
                      background: v.bgColor,
                    }}
                    count={v.total}
                  />
                </Space>
              }
              key={v.status}
            ></Tabs.TabPane>
          ))}
        </Tabs> */}

      <Table
        size="small"
        className="table-striped-rows mt-4"
        loading={loading}
        pagination={false}
        rowKey="id"
        //@ts-ignore
        onChange={onTableChange}
        dataSource={proposals}
        scroll={{ x: 1000 }}
      >
        <Column
          fixed="left"
          title="Mã đề nghị"
          dataIndex="code"
          key="proposal.code"
          width={100}
          render={(text, record: Proposal) => (
            <TableContent
              text={record.code}
              onClick={() => {
                proposalDetailModal.current?.viewDetail?.(record);
              }}
            ></TableContent>
          )}
        />
        <Column
          title="Số hợp đồng"
          dataIndex="name"
          key="proposal.name"
          width={200}
          render={(text, record: Proposal) => (
            <TableContent
              text={record.name}
              onClick={() => {
                proposalDetailModal.current?.viewDetail?.(record);
              }}
            ></TableContent>
          )}
        />

        <Column
          title="Tên sản phẩm"
          dataIndex="productName"
          key="proposal.productName"
        />
        {/* <Column
            align="right"
            title="Số lượng quy đổi ( khay / 1 thùng)"
            dataIndex="quantityOnUnit"
            key="proposal.quantityOnUnit"
          /> */}
        <Column
          align="right"
          title="Số lượng quy cách đóng gói"
          dataIndex="count"
          key="proposal.count"
          render={(value, record: Proposal) => record.proposalDetails.length}
        />

        <Column
          width={150}
          filterDropdown={(props) => (
            <FilterTable
              onSearch={(v) => {
                queryStaff.search = v;
                fetchStaff();
              }}
              {...props}
              options={staffs}
              fieldNames={{ label: "name", value: "id" }}
              type="select"
            />
          )}
          title="Tên người đề nghị"
          key="staffName"
          render={(text, record: Proposal) => {
            return (
              <TableContent
                text={[
                  record?.createdStaff?.name,
                  record?.createdStaff?.code
                    ? `(${record?.createdStaff?.code})`
                    : undefined,
                ]
                  .filter(Boolean)
                  .join(" - ")}
              />
            );
          }}
        ></Column>
        <Column
          // filterDropdown={(props) => <FilterTable {...props} type="date" />}
          width={150}
          title={"Ngày đề nghị"}
          key="createdAt"
          render={(text, record: Proposal) => {
            return <TableContent text={unixToFullDate(record?.createdAt)} />;
          }}
        ></Column>

        <Column
          width={100}
          align="right"
          title="Thao tác"
          key="action"
          render={(text, record: Proposal) => {
            const canEdit =
              record.status == ProposalStatus.New ||
              record.status == ProposalStatus.Temp;

            const canDelete =
              ((record?.status === "NEW" ||
                record?.status === ProposalStatus.Temp) &&
                record.createdStaff?.id === userStore.info.id) ||
              //Có quyền xoá hoặc là admin
              deleteRole ||
              userStore.info.role?.isAdmin;

            return (
              <Space>
                {canDelete && (
                  <Popconfirm
                    placement="topLeft"
                    title={`Xác nhận xóa đề nghị này?`}
                    okText="Ok"
                    cancelText="Không"
                    onConfirm={() =>
                      handleDelete(
                        //@ts-ignore
                        record?.id || 0
                      )
                    }
                  >
                    <Tooltip title="Xóa đề nghị" placement="bottom">
                      <DeleteOutlined className="text-red-500 text-[15px] cursor-pointer outline-none" />
                    </Tooltip>
                  </Popconfirm>
                )}

                <Space>
                  <Tooltip title="Xem chi tiết">
                    <EyeOutlined
                      onClick={() =>
                        proposalDetailModal.current?.viewDetail?.(record)
                      }
                      className="text-lg text-blue-600 cursor-pointer outline-none"
                    />
                  </Tooltip>
                </Space>

                {/* {userStore.managerProductionRole.taoBaoBi && (
                  <Tooltip title="Sao chép đề nghị">
                    <Spin spinning={false}>
                      <CopyOutlined
                        style={{ fontSize: 15 }}
                        onClick={() =>
                          proposalModalRef.current?.handleOpen("copy", record)
                        }
                        className="text-lg text-blue-600 cursor-pointer outline-none"
                      />
                    </Spin>
                  </Tooltip>
                )} */}
              </Space>
            );
          }}
        />
      </Table>

      <Pagination
        total={total}
        currentPage={query.page}
        onChange={({ page, limit }) => {
          query.page = page;
          query.limit = limit;
          fetchData();
        }}
        defaultPageSize={query.limit}
      />
      <PackageModal
        ref={proposalModalRef}
        onSubmitOk={(isDraff = false) => {
          if (isDraff) {
            setPageState((state) => !state);
          } else {
            proposalDetailModal.current?.refreshData();
            fetchData();
          }
        }}
      />

      <ModalSuggestionDetail
        onEdit={(data) => {
          proposalModalRef.current?.handleOpen("update", data);
        }}
        fetchData={fetchData}
        onOk={() => {
          fetchData();
          proposalModalRef.current?.close();
        }}
        onCancel={() => {}}
        ref={proposalDetailModal}
      />

      <DraffModal
        type={ProposalType.BaoBi}
        ref={draffModalRef}
        onSubmitOk={() => {
          fetchData();
        }}
      />
    </div>
  );
});
