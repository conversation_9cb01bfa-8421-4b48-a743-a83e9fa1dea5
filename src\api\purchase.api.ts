import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const purchaseApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/purchaseOrder",
      params,
    }),
  findOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}`,
    }),
  getDetails: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/details`,
      params,
    }),
  getByProvider: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/products/purchased`,
      params,
    }),
  code: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/code`,
      params,
    }),
  checkCode: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/code/check`,
      params,
    }),
  checkUpdate: (id: number, params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/checkUpdate`,
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/purchaseOrder",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}`,
      method: "patch",
      data,
    }),
  updateInfo: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/info`,
      method: "patch",
      data,
    }),

  updateDeadline: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/deadline`,
      method: "patch",
      data,
    }),

  followStaff: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/followStaff`,
      method: "patch",
      data,
    }),
  status: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/status`,
      method: "patch",
      data,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/complete`,
      method: "patch",
    }),
  updateDetails: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/details`,
      method: "patch",
      data,
    }),
  inventory: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/inventory`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}`,
      method: "delete",
    }),
  summary: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/summary/status`,
      params,
    }),
  deleteDetail: (purchaseOrderDetailId: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/detail/${purchaseOrderDetailId}`,
      method: "delete",
    }),

  approve: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/approve`,
      method: "patch",
      data,
    }),

  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/purchaseOrder/${id}/reject`,
      method: "delete",
      data,
    }),
};
