import { DeleteOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Popconfirm } from "antd";

type Props = {
  disabled: boolean;
  handleDelete: () => void;
};

const DeleteBatchBtn = ({ disabled, handleDelete }: Props) => {
  return (
    <div>
      <Popconfirm
        onConfirm={handleDelete}
        title="Bạn có chắc chắn muốn thực hiện thao tác này?"
      >
        <Button
          disabled={disabled}
          danger
          type="primary"
          icon={<DeleteOutlined />}
        >
          Xóa hàng loạt
        </Button>
      </Popconfirm>
    </div>
  );
};

export default DeleteBatchBtn;
