import { FileAttach } from "@/types/fileAttach";
import { Staff, StaffGroupAction } from "@/types/staff";
import { unixToDate, unixToFullDate } from "@/utils/dateFormat";
import {
  CheckCircleFilled,
  CheckOutlined,
  EditFilled,
  EyeOutlined,
  FileFilled,
  HistoryOutlined,
} from "@ant-design/icons";
import {
  Button,
  Col,
  Divider,
  Image,
  Modal,
  Popconfirm,
  Popover,
  Space,
  Tag,
  Tooltip,
  Typography,
  message,
} from "antd";
import { UploadFile } from "antd/lib/upload";
import React, { useImperativeHandle, useMemo, useRef, useState } from "react";
import {
  CommentSection,
  CommentSectionType,
} from "../../../components/Comment/CommentSection";

import { purchaseApi } from "@/api/purchase.api";
import AsyncBtn from "@/components/Button/AsyncBtn";
import {
  SignalSelectorModal,
  SignalSelectorModalRef,
} from "@/components/Selector/SignalSelector";
import { SignalWarning } from "@/components/SignalWarning/SignalWarning";
import TableListStaffGroupAction, {
  StaffListFeature,
} from "@/components/StaffGroupComponents/TableListStaffGroupAction";
import { permissionStore } from "@/store/permissionStore";
import { userStore } from "@/store/userStore";
import { ProposalStatus } from "@/types/proposal";
import {
  InspectStatus,
  InspectStatusTrans,
  PurchaseOrder,
  PurchaseOrderStatus,
  PurchaseOrderType,
} from "@/types/purchaseOrder";
import { checkRole } from "@/utils/auth";
import { checkApproved } from "@/utils/checkInspect";
import { getCreateInventoryLinkWithPurchase } from "@/utils/url";
import { useForm } from "antd/es/form/Form";
import { observer } from "mobx-react";
import { FaCartPlus, FaWarehouse } from "react-icons/fa6";
import {
  Contract,
  ContractDetail,
  ContractPackingDetail,
  ContractProduct,
  ContractStatus,
  ContractType,
  ContractTypeTrans,
} from "@/types/contract";
import { contractApi } from "@/api/contract.api";
import { TableContract, TableContractRef } from "./TableContract";
import {
  ContractContentModal,
  ContractContentModalRef,
} from "./ModalContractContents";
import ContractPackageDetailTable from "@/views/ProductionManager/Packaging/components/ContractPackageDetailTable";
import PrintPurchaseBtn from "@/views/PurchasePage/components/PrintPurchaseBtn";
import PrintContractBtn from "./PrintContractBtn";
import { useNavigate } from "react-router-dom";
import PrintAnnexContractBtn from "./PrintAnnex";
import PrintProductionBtn from "./PrintProductionBtn";
import { formatUSD, formatVND, formatVND2FDs } from "@/utils";
import ContractPDF from "@/utils/PDF/ContractPDF";
import ModalActivityLog, { ModalActivityLogRef } from "./ModalActivityLog";
import {
  TableContractProduct,
  TableContractProductRef,
} from "./TableContractProduct";
import { TableContractDetail } from "./TableContractDetail";
import { PermissionName } from "@/router";
import { ContractPaymentModalRef, ModalContractPayment } from "./ModalContractPayment";

interface ModalDetailContractProps {
  fetchData: () => void;
  onEdit: (detail: Contract) => void;
  onOk: () => void;
  onCancel: () => void;
}

export interface ModalDetailContractRef {
  refreshData: () => void;
  viewDetail: (detail: Contract) => void;
}

const ModalContractDetail = React.forwardRef(
  ({ fetchData, onEdit, onOk, onCancel }: ModalDetailContractProps, ref) => {
    const [detailData, setDetailData] = useState<Contract>();
    const [visible, setVisible] = useState(false);
    const contractContentModalRef = useRef<ContractContentModalRef>(null);
    const contractPaymentModalRef = useRef<ContractPaymentModalRef>(null);
    const [contractProducts, setContractProducts] = useState<
      Partial<ContractProduct & { date: string }>[]
    >([]);
    const [contractDetailsList, setContractDetailsList] = useState<
      Partial<ContractDetail>[][]
    >([]);
    const [contractDetails, setContractDetails] = useState<ContractDetail[]>(
      []
    );
    const modalActivityLogRef = useRef<ModalActivityLogRef>();
    const signalSelectorModalRef = useRef<SignalSelectorModalRef>();
    const tableContractRef = useRef<TableContractRef>();
    const navigate = useNavigate();
    const [fileListInvoice, setFileListInvoice] = useState<UploadFile[]>([]);
    const [isPreviewVisible, setPreviewVisible] = useState<boolean>(false);
    const [form] = useForm();
    const tableContractProductRef = useRef<TableContractProductRef>();
    const [dataProducts, setDataProduct] = useState([]);

    // const canUpdateStatus = checkRole(
    //   "payment-update",
    //   permissionStore.permissions
    // );

    // const isViewMoneyInfoRole = checkRole(
    //   "view-money-info",
    //   userStore.info.role?.permissions
    // );

    // const previewPdfRef = useRef<PreviewPDFModal>();

    // const typeText = typeTextTrans[type as PurchaseOrderType];

    /**
     * Cần được duyệt thì mới có thể nhập kho
     */
    // const canEditTable = useMemo(() => {
    //   if (detailData) {
    //     const { isAllInspec, inspecStaffs, inspecHistories } = detailData;
    //     return checkApproved(isAllInspec, inspecStaffs, inspecHistories);
    //   }
    //   return false;
    // }, [detailData]);

    /**
     * Ẩn các thông tin tiền, NCC với người theo dõi. Admin với kế toán xem được
     */
    // const showMoneyInfo = useMemo(() => {
    //   if (!detailData) return true;

    //   const isFollowStaff = detailData?.followStaffs
    //     .map((item) => item.id)
    //     .includes(userStore.info.id as number);

    //   const isInspectStaff = detailData?.inspecStaffs
    //     .map((item) => item.id)
    //     .includes(userStore.info.id as number);

    //   const isViewMoneyInfoRole = checkRole(
    //     "view-money-info",
    //     userStore.info.role?.permissions
    //   );

    //   const result =
    //     isFollowStaff ||
    //     isInspectStaff ||
    //     userStore.info.role?.isAdmin ||
    //     userStore.info.role?.isAccounting ||
    //     userStore.info.id == detailData?.createdStaff.id ||
    //     isViewMoneyInfoRole;

    //   return result;
    // }, [detailData, userStore.info.role]);

    // const hiddenProviderInfo = useMemo(() => {
    //   if (!detailData) return true;

    //   const isFollowStaff = detailData?.followStaffs
    //     .map((item) => item.id)
    //     .includes(userStore.info.id as number);

    //   const isInspectStaff = detailData?.inspecStaffs
    //     .map((item) => item.id)
    //     .includes(userStore.info.id as number);

    //   const isViewProviderInfoRole = checkRole(
    //     "view-provider-info",
    //     userStore.info.role?.permissions
    //   );

    //   return (
    //     !isInspectStaff &&
    //     !userStore.info.role?.isAdmin &&
    //     !userStore.info.role?.isAccounting &&
    //     userStore.info.id != detailData?.createdStaff.id &&
    //     !isViewProviderInfoRole
    //   );
    // }, [detailData, userStore.info.role]);

    const canCompleteProposal = useMemo(() => {
      /* Chỉ admin, người tạo, người duyệt được hoàn thành */
      const isInspectStaff = detailData?.inspecStaffs.some(
        (item) => item.id == userStore.info.id
      );
      const userCanComplete =
        (detailData?.createdStaff?.id == userStore.info.id ||
          userStore.info.role?.isAdmin ||
          isInspectStaff) &&
        detailData?.status !== ContractStatus.Complete;

      if (detailData?.isAllInspec) {
        //Tât cả đều duyệt
        const isAllComplete =
          detailData?.inspecHistories?.length ==
          detailData?.inspecStaffs.length &&
          detailData.inspecHistories.every(
            (item) => item.status == ProposalStatus.Complete
          );

        return userCanComplete && isAllComplete;
      } else {
        const isSomeOneComplete = detailData?.inspecHistories.some(
          (item) => item.status == ProposalStatus.Complete
        );
        return isSomeOneComplete && userCanComplete;
      }
    }, [detailData]);

    // const canCreateBaoBi = useMemo(() => {
    //   return (

    //     detailData?.status == ContractStatus.Approved
    //   );
    // }, [detailData]);

    useImperativeHandle(
      ref,
      () => {
        return {
          async refreshData() {
            if (detailData && visible) fetchDetail(detailData.id);
          },
          async viewDetail(detail: Contract) {
            if (detail) {
              fetchDetail(detail.id);
              setVisible(true);
            }
          },
        };
      },
      [detailData]
    );

    // const handleCreateInventory = () => {
    //   window.open(getCreateInventoryLinkWithPurchase(detailData), "_blank");
    // };

    const fetchDetail = async (id: number) => {
      const { data } = await contractApi.findOne(id);
      setDetailData(data);
      //@ts-ignore
      setContractProducts(
        data?.contractPos || []
      );

      setDataProduct(data?.contractProducts || [])
      console.log("data", data);
      if (data?.invoiceFiles?.length) {
        setFileListInvoice(
          data?.invoiceFiles.map((item: FileAttach) => ({
            name: item.name || "",
            uid: item.url,
            url: item.url,
            id: item.id,
          }))
        );

        form.setFieldValue(
          "invoiceFiles",
          data?.invoiceFiles.map((item: FileAttach) => ({
            name: item.name || "",
            uid: item.url,
            url: item.url,
            id: item.id,
          }))
        );
      } else {
        setFileListInvoice([]);
      }

      const contractProducts = data?.contractProducts || [];
      const allContractDetails = data?.contractDetails || [];

      const groupedDetailsList = contractProducts.map(
        (product: ContractProduct) => {
          const poCode = product.poCode;
          // Lọc ra tất cả contractDetails có poCode trùng
          const detailsForThisProduct = allContractDetails.filter(
            (detail: ContractDetail) => detail.poCode === poCode
          );
          return detailsForThisProduct;
        }
      );

      // // Set lại contractDetailsList
      setContractDetailsList(groupedDetailsList);
      setContractDetails(allContractDetails);

      setTimeout(() => {
        //Vì state type chưa set kịp nên phải timeout để modal được set value
        return tableContractRef.current?.setValue(data);
      }, 100);
    };

    const handleOpenFile = (item: FileAttach) => {
      const isPdf = item.mimetype.includes("pdf");

      if (isPdf) {
        // previewPdfRef.current?.handleOpen(item);
        return;
      } else {
        window.open(item.url, "_blank");
      }
    };

    const notHaveSignal = useMemo(
      () => !userStore.info.signatureImage && !userStore.info.signatureImage2,
      [userStore.info]
    );

    const handleApproval = async (id: number, signImage: string) => {
      const res = await contractApi.approve(id, { signImage });
      fetchDetail(detailData?.id || 0);
      onOk();
    };

    const handleReject = async (id: number) => {
      const res = await contractApi.reject(id);
      fetchDetail(detailData?.id || 0);
      onOk();
    };

    const mergedList = detailData?.inspecStaffs?.map((staff) => {
      const approvalStatus = detailData?.inspecHistories?.find(
        (approved) => approved.staff.id === staff.id
      );
      return {
        ...staff,
        status: approvalStatus ? approvalStatus.status : "NEW",
      };
    });

    const handleCompleteContract = async () => {
      await contractApi.complete(detailData?.id as number);
      fetchDetail(detailData?.id || 0);
      onOk();
      message.success(`Đã đóng hợp đồng`);
    };

    const contractDetailsGroupByName = useMemo(() => {
      const map = new Map<string, ContractDetail>();

      contractDetails?.forEach((item) => {
        const key = item.productName?.trim().toLowerCase() || "";
        if (!key) return;

        if (map.has(key)) {
          // Nếu đã có, cộng dồn quantity
          const exist = map.get(key)!;
          exist.quantity = (exist.quantity || 0) + (item.quantity || 0);
          // Nếu muốn cộng dồn các trường khác, xử lý thêm ở đây
        } else {
          // Clone để không ảnh hưởng object gốc
          map.set(key, { ...item });
        }
      });

      return Array.from(map.values());
    }, [contractDetails]);

    const onClickPackagingProposal = (contractDetail: ContractDetail) => {
      if (contractDetail && contractDetail.id) {
        window.open(
          `/package?contractId=${detailData?.id}&contractDetailId=${contractDetail.id}`,
          "_blank"
        );
      }
    };

    const transformContract = (data: any) => {
      return {
        contract: {
          termsOfDelivery: data.termsOfDelivery,
          requiredDocuments: data.requiredDocuments,
          paymentTerm: data.paymentTerm,
          contactPersonName: data.contactPersonName,
          total: data.total,
          name: data.name,
          code: data.code,
          description: data.description,
          deadlineDate: "", // cần bổ sung nếu có
          procedureCode: data.procedureCode,
          isAllInspec: data.isAllInspec,
          po: data.po,
          signAt: data.signAt,
          totalPrice: data.totalPrice,
          totalQuantity: data.totalQuantity,
          productBy: data.productBy,
        },
        contractProducts: data.contractProducts,
        contractPos: data.contractPos,
        followStaffIds: data.followStaffs.map((staff: any) => staff.id),
        inspecStaffIds: data.inspecStaffs.map((staff: any) => staff.id),
        fileAttachIds: [],
        paymentTermId: 0,
        customerId: data.customer.id,
        marketId: data.market.id,
        contractContents: Array.from(
          new Map(data.contractContents.map((item: any) => [item.type, item])).values()
        ),
      };
    }

    const onSumitPayment = async () => {
      if (detailData) {
        await contractApi.update(detailData.id, transformContract(detailData as any));
        message.success("Cập nhật thông tin thanh toán thành công");
      }
    };

    return (
      <div>
        <Modal
          width={5000}
          title="Thông tin hợp đồng"
          open={visible}
          onOk={onOk}
          onCancel={() => {
            onCancel();
            setVisible(false);
          }}
          footer={null}
          style={{ top: 10 }}
          destroyOnClose
          afterClose={() => setDetailData(undefined)}
          zIndex={100}
        >
          <div className="w-full flex flex-col md:flex-row justify-between mb-2">
            <h2 className="text-lg m-0">{detailData?.name} </h2>
            <Space split={<Divider type="vertical" />}>
              <div className="flex gap-1 md:gap-2 flex-wrap md:flex-nowrap">
                {canCompleteProposal && (
                  <AsyncBtn
                    typeText={"hợp đồng"}
                    className="font-medium text-green-500  border-green-500 focus-visible:outline-none"
                    hidden
                    icon={<CheckOutlined />}
                    onSubmit={handleCompleteContract}
                  >
                    Hoàn thành hợp đồng
                  </AsyncBtn>
                )}

                {detailData?.status == ContractStatus.Complete && (
                  <div className="bg-green-500 text-white hover:text-white flex items-center rounded-md font-semibold px-3">
                    <Space>
                      <CheckCircleFilled /> <div>Đã đóng hợp đồng</div>
                    </Space>
                  </div>
                )}

                <Button
                  className="font-medium text-xs md:text-base py-1 md:px-4 px-2 text-blue-500 border-blue-500 focus-visible:outline-none"
                  hidden
                  icon={<HistoryOutlined />}
                  onClick={() =>
                    modalActivityLogRef.current?.handleOpen(detailData?.id || 0)
                  }
                >
                  Lịch sử thay đổi
                </Button>
                {/* <Button
                  className="font-medium text-xs md:text-base py-1 md:px-4 px-2 border-none"
                  disabled={detailData?.status !== ContractStatus.New}
                  hidden
                  type="primary"
                  icon={<EyeOutlined />}
                  onClick={() => {
                    if (detailData?.contractContents)
                      contractContentModalRef.current?.handleUpdate(
                        detailData?.contractContents
                      );
                  }}
                >
                  Xem nội dung hợp đồng
                </Button> */}
                {((userStore.info.role?.isAdmin ||
                  detailData?.createdStaff?.id === userStore.info.id) &&
                  userStore.checkRoleByName(PermissionName.updateContract)
                ) && (
                    <Button
                      className="font-medium text-xs md:text-base py-1 md:px-4 px-2 border-none"
                      disabled={detailData?.status !== ContractStatus.New}
                      hidden
                      type="primary"
                      icon={<EditFilled />}
                      onClick={() => {
                        if (detailData) {
                          onEdit(detailData);
                        }
                      }}
                    >
                      Chỉnh sửa
                    </Button>
                  )}

                {userStore.checkRoleByName(PermissionName.paymentUpdate) && (
                  <Button
                    className="font-medium text-xs md:text-base py-1 md:px-4 px-2 border-none"
                    disabled={detailData?.status !== ContractStatus.New}
                    hidden
                    type="primary"
                    icon={<EditFilled />}
                    onClick={() => {
                      if (!detailData?.contractContents?.find((content) => content.type === "PAYMENT")) {
                        message.error("Không có thông tin thanh toán mặc định")
                      }
                      else {
                        if (detailData?.contractContents) {
                          contractPaymentModalRef.current?.handleUpdate(
                            detailData?.contractContents
                          );
                        }
                      }
                    }}
                  >
                    Chỉnh sửa thông tin thanh toán
                  </Button>
                )}

                {detailData && (
                  <PrintContractBtn data={detailData}></PrintContractBtn>
                )}
                {detailData && (
                  <PrintProductionBtn data={detailData}></PrintProductionBtn>
                )}
                {detailData && <PrintAnnexContractBtn data={detailData} />}
                {/* {detailData && isViewMoneyInfoRole && (
                  <PrintPurchaseBtnV2 data={detailData}></PrintPurchaseBtnV2>
                )} */}
                {/* {detailData?.type == PurchaseOrderType.PO &&
                  canUpdateStatus && ( */}
                {/* <PurchaseStatusBtn
                      detailData={detailData}
                      onOK={() => {
                        fetchDetail(detailData?.id || 0);
                        onOk();
                      }}
                    /> */}
                {/* )} */}
              </div>
              <span className="text-blue-500 text-sm md:text-lg font-semibold">
                #{detailData?.code}
              </span>
            </Space>
          </div>

          {/* {detailData && (
            <>
              <ReportOrderHeader data={detailData}></ReportOrderHeader>
              <POInlandBodyPDF data={detailData} />
              <ReportOrderFooter data={detailData}></ReportOrderFooter>
            </>
          )} */}

          {/* {detailData && <PrintPage data={detailData} />}{" "} */}
          <div className="flex flex-col text-xs md:text-sm md:flex-row gap-3 mb-2">
            <div className="flex flex-col gap-1 w-[calc(100%-16px)] md:w-1/2 bg-slate-50 p-2 md:p-4 rounded-md">
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Người tạo:</span>
                <span>{detailData?.createdStaff?.name}</span>
              </div>

              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Tên hợp đồng:</span>
                <span>{detailData?.name}</span>
              </div>
              {/* <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Mã hợp đồng:</span>
                <span>{detailData?.code}</span>
              </div> */}
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Số PO:</span>
                <span>{detailData?.po}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Ngày ký hợp đồng:
                </span>
                <span>{unixToDate(detailData?.signAt || 0)}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Khách hàng:</span>
                <span>{detailData?.customer?.name}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Thị trường:</span>
                <span>{detailData?.market?.name}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">Nơi sản xuất:</span>
                <span>
                  {Array.isArray(JSON.parse(detailData?.productBy || "[]")) &&
                    JSON.parse(detailData?.productBy || "[]")?.join(" - ")}
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Điều kiện thanh toán:
                </span>
                <span>{detailData?.paymentTerm}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Điều kiện giao hàng:
                </span>
                <span>{detailData?.termsOfDelivery}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Yêu cầu tài liệu:
                </span>
                <span>
                  {detailData?.requiredDocuments?.split('\n').map((line: any, index: number) => (
                    <React.Fragment key={index}>
                      {line}
                      <br />
                    </React.Fragment>
                  ))}
                </span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Loại hợp đồng:
                </span>
                <span>{ContractTypeTrans[detailData?.type || ContractType.General]}</span>
              </div>
              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Thời gian tạo:
                </span>
                <span>{unixToFullDate(detailData?.createdAt || 0)}</span>
              </div>
              {/* {!hiddenProviderInfo && (
                <div className="flex gap-2 items-center">
                  <span className="font-medium min-w-[120px]">
                    Nhà cung cấp:
                  </span>
                  <span>
                    {detailData?.provider?.code} - {detailData?.provider?.name}
                  </span>
                </div>
              )} */}

              <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Cập nhật gần nhất:
                </span>
                <span>
                  {detailData?.createdAt == detailData?.updatedAt
                    ? "--"
                    : unixToFullDate(detailData?.updatedAt || 0)}
                </span>
              </div>

              {/* <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Ngày giao hàng:
                </span>
                <span>
                  {detailData?.deadlineDate ? (
                    moment(detailData.deadlineDate).format(settings.dateFormat)
                  ) : (
                    <span>--</span>
                  )}
                </span>

                {detailData?.deadlineDate &&
                moment().format("YYYY-MM-DD") < detailData.deadlineDate ? (
                  <span>
                    (còn{" "}
                    {getExpiredDate(
                      moment(detailData.deadlineDate).endOf("day").unix() || 0
                    )}
                    )
                  </span>
                ) : (
                  <></>
                )}

                {detailData?.status === PurchaseOrderStatus.Pending &&
                  (userStore.info.id == detailData.createdStaff?.id ||
                    userStore.info.role?.isAdmin) && (
                    <Tooltip title="Chỉnh sửa ngày giao hàng">
                      <EditFilled
                        className="text-blue-500"
                        onClick={() =>
                          deadlineModalRef.current?.handleOpen(detailData)
                        }
                      />
                    </Tooltip>
                  )}
              </div> */}

              {/* <div className="flex gap-2 items-center">
                <span className="font-medium min-w-[120px]">
                  Điều khoản thanh toán:
                </span>
                <span>{detailData?.paymentTermName}</span>
              </div> */}
              <div>
                <div className="flex gap-2 items-center justify-between">
                  <div className="font-medium min-w-[120px] ">
                    <span>Mô tả:</span>
                  </div>
                </div>
                <p>{detailData?.description}</p>
              </div>
              <Divider style={{ margin: 0 }} />

              <div>
                <div className="flex gap-2 items-center ">
                  <span className="font-medium min-w-[120px]">
                    Tổng giá trị:
                  </span>
                  <span>{formatVND2FDs(detailData?.totalPrice)}</span>
                </div>
              </div>

              <div>
                <div className="flex gap-2 items-center ">
                  <span className="font-medium min-w-[120px]">
                    Tổng số lượng:
                  </span>
                  <span>{formatVND(detailData?.totalQuantity)}</span>
                </div>
              </div>
              {/* <div className="flex gap-2 items-center">
                <span className="font-medium">Lưu ý:</span>
                <span>{detailData?.note || "--"}</span>
              </div> */}
            </div>

            <div className="flex flex-col gap-2 bg-slate-50 p-2 md:p-4 rounded-md w-[calc(100%-16px)] md:w-1/2">
              <div>
                <Space className="w-full justify-between mb-1">
                  <h1 className="text-sm md:text-base m-0">
                    Trạng thái duyệt:
                  </h1>
                  <Tag color="blue" className="text-[10px] md:text-xs">
                    <b>Yêu cầu:</b>{" "}
                    {detailData?.isAllInspec
                      ? "Tất cả cùng duyệt"
                      : "Chỉ cần 1 người duyệt"}
                  </Tag>
                </Space>
                <div className="flex flex-col gap-1">
                  {mergedList?.map((data: Staff) => {
                    return (
                      <div
                        className="flex items-center justify-between w-full"
                        key={data.id}
                      >
                        <div className="flex gap-1">
                          <Tooltip
                            title={`${data.name} (${data.code})`}
                            placement="left"
                          >
                            <span className="font-medium min-w-[100px] md:min-w-[200px] !max-w-[100px] md:max-w-[150px] overflow-hidden whitespace-nowrap text-ellipsis">
                              {data.name} (
                              <span className="text-blue-700">{data.code}</span>
                              )
                            </span>
                          </Tooltip>

                          <Tag
                            className="text-[10px] md:text-xs"
                            color={
                              (data?.status === InspectStatus.Pending && "") ||
                              (data?.status === InspectStatus.Reject &&
                                "red") ||
                              (data?.status === InspectStatus.Complete &&
                                "green") ||
                              ""
                            }
                          >
                            {/* @ts-ignore */}
                            {InspectStatusTrans[data.status]}
                          </Tag>
                        </div>

                        <div className="flex gap-2 mr-2">
                          {data.id === userStore.info.id &&
                            data.status === ContractStatus.New && (
                              <>
                                {notHaveSignal && <SignalWarning />}
                                <Popconfirm
                                  disabled={notHaveSignal}
                                  placement="top"
                                  title={`Xác nhận duyệt yêu cầu này?`}
                                  okText="Xác nhận"
                                  cancelText="Hủy"
                                  onConfirm={() => {
                                    if (
                                      userStore.info.signatureImage2 &&
                                      userStore.info.signatureImage
                                    ) {
                                      signalSelectorModalRef.current?.handleOpen();
                                    } else {
                                      handleApproval(
                                        detailData?.id || 0,
                                        userStore.info.signatureImage2 ||
                                        userStore.info.signatureImage ||
                                        ""
                                      );
                                    }
                                  }}
                                >
                                  <Button
                                    disabled={notHaveSignal}
                                    size="small"
                                    className="!text-[10px] md:!text-sm font-medium !px-1 md:!px-[7px]"
                                    type="primary"
                                  >
                                    Duyệt
                                  </Button>
                                </Popconfirm>

                                <Popconfirm
                                  placement="top"
                                  title={`Từ chối yêu cầu này?`}
                                  okText="Xác nhận"
                                  cancelText="Hủy"
                                  onConfirm={() =>
                                    handleReject(detailData?.id || 0)
                                  }
                                >
                                  <Button
                                    size="small"
                                    className="!text-[10px] md:!text-sm font-medium !px-1 md:!px-[7px]"
                                  >
                                    Từ chối
                                  </Button>
                                </Popconfirm>
                              </>
                            )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div>
                <h1 className="text-sm md:text-base m-0">Người theo dõi:</h1>
                <div className="flex">
                  <Popover
                    content={
                      <div className="line-clamp-2">
                        {detailData?.followStaffs &&
                          detailData?.followStaffs?.length > 0 &&
                          detailData?.followStaffs?.map((item, index) => {
                            return (
                              <div className="font-medium min-w-[100px] md:min-w-[200px] !max-w-[100px] md:max-w-[150px] overflow-hidden cursor-pointer">
                                {item?.name}{" "}
                                <span className="text-blue-700">
                                  {" "}
                                  ({item?.code})
                                  {index < detailData.followStaffs.length - 1 &&
                                    ", "}
                                </span>
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <div className="line-clamp-2">
                      {detailData?.followStaffs &&
                        detailData?.followStaffs?.length > 0 &&
                        detailData?.followStaffs?.map((item, index) => {
                          return (
                            <span className="font-medium min-w-[100px] md:min-w-[200px] !max-w-[100px] md:max-w-[150px] overflow-hidden">
                              {item?.name}
                              <span className="text-blue-700">
                                ({item?.code})
                                {index < detailData.followStaffs.length - 1 &&
                                  ", "}
                              </span>
                            </span>
                          );
                        })}
                    </div>
                  </Popover>
                </div>
              </div>
              {/* {detailData?.status == ContractStatus.Approved && (
                <div>
                  <Button
                    onClick={() => {
                      navigate(
                        `/package?contractId=${detailData?.id}`
                      );
                    }}
                    size="small"
                    type="primary"
                    icon={
                      <div className="translate-y-0.5">
                        <FaCartPlus />
                      </div>
                    }
                  >
                    Sinh đề nghị bao bì
                  </Button>
                </div>
              )} */}
              {/* {checkRole(
                "create-inventory-from-PO",
                userStore.info.role?.permissions
              ) &&
                detailData &&
                checkApproved(
                  detailData?.isAllInspec,
                  detailData?.inspecStaffs,
                  detailData?.inspecHistories
                ) && (
                  <div>
                    <Button
                      onClick={handleCreateInventory}
                      type="primary"
                      icon={
                        <div className="translate-y-[2px]">
                          <FaWarehouse />
                        </div>
                      }
                    >
                      Tạo phiếu nhập kho
                    </Button>
                  </div>
                )} */}
            </div>
          </div>
          <div>
            <div className="text-[16px] font-semibold">Danh sách hàng hóa:</div>
            <div className="">
              <TableContractDetail
                status="view"
                readOnly
                contractDetails={dataProducts}
                statusProject={detailData?.status}
                onClickPackagingProposal={onClickPackagingProposal}
              />
            </div>
            <div className="mt-5 text-[16px] font-semibold">Danh sách PO:</div>
            <TableContractProduct
              contractProducts={contractProducts}
              status={"update"}
              ref={tableContractProductRef}
              hiddenPrivateInfo={false}
              readOnly
              approved={detailData?.status == ContractStatus.Approved}
              onClickPackagingProposal={onClickPackagingProposal}
            />

            {contractProducts?.map((poDetail, index) => (
              <div
                key={index}
                style={{
                  marginTop: 20,
                  // padding: 10,
                }}
              >
                <div className="text-[16px] font-semibold">
                  Hàng hóa của PO: {poDetail.po}
                </div>
                <TableContract
                  status="update"
                  listProducts={contractDetailsGroupByName}
                  contractProduct={poDetail}
                  contractDetails={poDetail?.poDetails?.map((detail) => (
                    {
                      ...detail,
                      productName: detail.contractProduct?.productName,
                      poCode: poDetail.po
                    }
                  )) as any}
                  readOnly
                // handleChangeContractDetails={(
                //   updatedDetails: Partial<ContractDetail>[]
                // ) => handleChangeContractDetails(index, updatedDetails)}
                />
              </div>
            ))}
          </div>

          {/* <Col span={24}>
            <Divider className="my-0 mb-4"></Divider>
            <section>
              {Array.from(
                new Map(
                  contractDetails
                    .filter((p) => p.productName && p.productName.trim() !== "")
                    .map((p) => [p.productName.trim(), p])
                ).values()
              ).map((product, index) => (
                <div key={index} style={{ marginTop: 10 }}>
                  <div className="flex items-end justify-between">
                    <Typography.Title level={5}>
                      Quy cách đóng gói cho SP: {product.productName}
                    </Typography.Title>

                    {detailData?.status === ContractStatus.Approved && (
                      <Button
                        onClick={() => {
                          onClickPackagingProposal?.(product);
                        }}
                        className="mb-2"
                        size="small"
                        type="primary"
                        icon={
                          <div className="translate-y-0.5">
                            <FaCartPlus />
                          </div>
                        }
                      >
                        Sinh đề nghị bao bì
                      </Button>
                    )}
                  </div>

                  <ContractPackageDetailTable
                    readonly={true}
                    details={product.contractPackingDetails || []}
                  />
                </div>
              ))}

              <ContractContentModal
                ref={contractContentModalRef}
                onClose={() => {}}
                onSubmitOk={() => {}}
              ></ContractContentModal>
            </section>
          </Col> */}
          {/* <div className="relative mx-auto w-[900px]">
            {detailData &&
              ContractPDF.getPrintComponent({
                data: detailData,
                printType: "en",
              })}
          </div> */}
        </Modal>
        <ModalActivityLog ref={modalActivityLogRef} />
        {/* <PreviewPDFModal
          ref={previewPdfRef}
          onClose={() => ""}
          onSubmitOk={() => ""}
        /> */}
        {/* <DeadlineModal
          onSubmitOk={() => fetchDetail(detailData?.id || 0)}
          ref={deadlineModalRef}
        /> */}
        <SignalSelectorModal
          ref={signalSelectorModalRef}
          type={"CONTRACT"}
          selectedItem={detailData as Contract}
          onSubmitOk={() => {
            fetchDetail(detailData?.id || 0);
            onOk();
          }}
        />
        <ContractContentModal
          readOnly={true}
          ref={contractContentModalRef}
          onClose={() => { }}
          onSubmitOk={() => { }}
        ></ContractContentModal>

        <ModalContractPayment
          ref={contractPaymentModalRef}
          onClose={() => { }}
          onSubmitOk={onSumitPayment}
        ></ModalContractPayment>
      </div>
    );
  }
);

export default observer(ModalContractDetail);
