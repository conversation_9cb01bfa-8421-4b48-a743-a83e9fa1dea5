import { departmentApi } from "@/api/department.api";
import { productApi } from "@/api/product.api";
import { proposalApi } from "@/api/proposal.api";
import { UploadExcelLocal } from "@/components/FileUpload/UploadLocal";
import { InputNumberVN } from "@/components/Input/InputNumberVN";
import { ProductSelectorRef } from "@/components/ProductSelector/ProductSelector";
import { useDepartment } from "@/hooks/useDepartMent";
import { useProduct } from "@/hooks/useProduct";
import { useProposal } from "@/hooks/useProposal";
import { useTaxConfig } from "@/hooks/useTaxConfig";
import { useUnit } from "@/hooks/useUnit";
import { Department, DepartmentType } from "@/types/department";
import { ModalStatus } from "@/types/modal";
import { Product } from "@/types/product";
import {
  Proposal,
  ProposalDetail,
  ProposalStatus,
  ProposalType,
} from "@/types/proposal";
import {
  PurchaseOrder,
  PurchaseOrderDetail,
  PurchaseOrderType,
} from "@/types/purchaseOrder";
import { formatUSD, formatVND, formatVND2FDs } from "@/utils";
import { getDateExcel } from "@/utils/dateFormat";
import purchaseExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/purchaseExportDemol";
import { calcMoneyItem2 } from "@/utils/money";
import { getFinalNumber } from "@/utils/number";
import {
  getBaoBiProposalLink,
  getDepartmentLink,
  getProposalLink,
} from "@/utils/url";
import { ProductItem } from "@/views/Product/components/ProductItem";
import {
  ProductModal,
  ProductModalRef,
} from "@/views/Product/components/ProductModal";
import {
  CopyOutlined,
  DeleteOutlined,
  FileFilled,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Button,
  Checkbox,
  DatePicker,
  Empty,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from "antd";
import Column from "antd/es/table/Column";
import clsx from "clsx";
import dayjs from "dayjs";
import { debounce, isNumber } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { purchaseApi } from "@/api/purchase.api";
import { Quote, QuoteDetail } from "@/types/quote";
import { settings } from "../../../../settings";
import { quoteApi } from "@/api/quote.api";
import quoteExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/quoteExportDemo";
import {
  Contract,
  ContractDetail,
  ContractDetailETDTypeTrans,
  ContractProduct,
} from "@/types/contract";
import moment from "moment";
import { PiListPlusBold } from "react-icons/pi";
import { usePaymentInfo } from "@/hooks/usePaymentInfo";
import { useTermOfPayment } from "@/hooks/useTermOfPayment";
import { FaCartPlus } from "react-icons/fa6";

export interface TableContractProductRef {
  getValue: () => {
    contractProducts: ContractProduct[];
  };
  setValue: (contract: Partial<Contract>) => void;
}

export const validateContractProducts = (
  products: Partial<ContractProduct & { date: string }>[]
) => {
  for (const [i, item] of products.entries()) {
    if (!item.poCode) return `Dòng ${i + 1}: Chưa nhập mã PO`;
    if (!item.totalFCL) return `Dòng ${i + 1}: Chưa nhập số lượng FCL`;
    if (!item.contType) return `Dòng ${i + 1}: Chưa chọn loại cont`;
    if (!item.date) return `Dòng ${i + 1}: Chưa nhập thời gian giao hàng`;
    if (!item.amount) return `Dòng ${i + 1}: Chưa nhập tổng giá trị`;
    if (!item.quantity) return `Dòng ${i + 1}: Chưa nhập tổng số lượng`;
  }
  return null;
};

export const TableContractProduct = observer(
  React.forwardRef(
    (
      {
        readOnly = false,
        disableAction = false,
        status,
        hiddenPrivateInfo = false,
        visibleDelete,
        contractProducts,
        onRefresh,
        onChangeTotalAmount,
        onChangeTotalQuantity,
        onAddRow,
        onDeleteContractProduct,
        onChangeContractProduct,
        approved = false,
        onClickPackagingProposal,
      }: {
        readOnly?: boolean;
        disableAction?: boolean;
        status: ModalStatus;
        hiddenPrivateInfo: boolean;
        visibleDelete?: boolean;
        contractProducts?: Partial<ContractProduct & { date: string }>[];
        onRefresh?: () => void;
        onChangeTotalAmount?: (total: number) => void;
        onChangeTotalQuantity?: (total: number) => void;
        onChangeContractProduct?: (
          index: number,
          product: Partial<ContractProduct & { date: string }>
        ) => void;
        onDeleteContractProduct?: (index: number) => void;

        onAddRow?: (duplicate?: number) => void;
        approved?: boolean;
        onClickPackagingProposal?: (contract: ContractDetail) => void;
      },
      ref
    ) => {
      const {
        fetchData,
        units,
        query: queryUnit,
      } = useUnit({
        initQuery: { page: 1, limit: 50 },
      });

      const {
        fetchData: fetchPaymentTerm,
        paymentTerms,
        query: queryPaymentTerm,
      } = useTermOfPayment({
        initQuery: { page: 1, limit: 50 },
      });

      const productModalRef = useRef<ProductModalRef>();
      // const quantityLogModalRef = useRef<QuantityLogModalRef>();
      const productSelectorRef = useRef<ProductSelectorRef>();
      const [isCheckAll, setIsCheckAll] = useState<boolean>(false);
      const [contract, setContract] = useState<Contract>();
      // const modalEditNoteRef = useRef<ModalEditNoteRef>();
      const {
        fetchData: fetchProducts,
        products,
        loading,
        query,
        total,
      } = useProduct({
        initQuery: { page: 1, limit: 50 },
      });

      const [loadingTable, setLoadingTable] = useState(false);
      const [POCodeStart, setPOCodeStart] = useState<number>();

      const moneyValue = useRef({
        moneyTax: 0,
        subTotal: 0,
        amount: 0,
      });

      const optionsCont = [
        {
          value: "20FT",
          label: "20FT",
        },
        {
          value: "40FT",
          label: "40FT",
        },
        {
          value: "0",
          label: "Combined shipment",
        },
      ];
      useEffect(() => {
        fetchData();
        fetchProducts();
        fetchPaymentTerm();
      }, []);

      useImperativeHandle(
        ref,
        () => {
          return {
            // getValue() {
            //   const finalData = data.map((item) => ({
            //     ...item,
            //   }));
            //   return {
            //     contractProducts: finalData,
            //   };
            // },
            // setValue(contract: Contract) {
            //   console.log("contract: ", contract);
            //   const finalData = contract?.contractProducts?.map((item) => ({
            //     ...item,
            //   }));
            //   //@ts-ignore
            //   setData(finalData);
            //   setContract(contract);
            //   setIsCheckAll(isCheckAll);
            // },
          };
        },
        [contractProducts]
      );

      const calculateTotalAmount = (data: Partial<ContractDetail>[]) => {
        const totalAmount = data.reduce(
          (sum, item) => sum + (item.amount || 0),
          0
        );
        moneyValue.current.amount = totalAmount;
        return totalAmount;
      };

      const calculateTotalQuantity = (data: Partial<ContractDetail>[]) => {
        const totalQuantity = data.reduce(
          (sum, item) => sum + (item.quantity || 0),
          0
        );
        return totalQuantity;
      };

      // const updateData = (newData: Partial<ContractDetail>[]) => {
      //   setData(newData);
      //   const total = calculateTotalAmount(newData);
      //   if (typeof onChangeTotalAmount === "function") {
      //     onChangeTotalAmount(total);
      //   }
      // };

      // const updateQuantity = (newData: Partial<ContractDetail>[]) => {
      //   const total = calculateTotalQuantity(newData);
      //   if (typeof onChangeTotalQuantity === "function") {
      //     onChangeTotalQuantity(total);
      //   }
      // };

      const addRow = (customData?: Partial<ContractProduct>[]) => {
        // if (customData) {
        //   setData([...customData]);
        // } else {
        //   setData([...data, { quantity: 0 }]);
        //   // updateQuantity([...data]);
        // }
        onAddRow?.();
      };

      const handleImportExcel = async (excelData: any[]) => {
        console.log(excelData);

        const codes = excelData.map((item) =>
          item["Mã hàng"]?.toString()?.trim()
        );
        console.log({ codes });
        const promises = [productApi.codes({ codes })];

        const [resProduct, resDepartment] = await Promise.all(promises);

        const products = resProduct.data.products;
        if (products.length) {
          fetchProducts(products);
        }
        //@ts-ignore
        const result: PurchaseOrderDetail[] = excelData.map((item) => {
          const productName: string = item["Mã hàng"] || "";
          const quantity: number = (item["Số lượng"] as number) || 0;

          const findProduct = products.find(
            (product: Product) =>
              product.code.toLowerCase() ==
              productName?.toString()?.trim().toLowerCase()
          );

          return {
            description: item["Diễn giải"],
            productId: findProduct ? findProduct.id : undefined,
            name: productName ? productName : undefined,
            product: findProduct,
            unitId: findProduct?.unit?.id,
            unit: findProduct?.unit,
            quantity: item["Số lượng"],
          };
        });
        // const currentData = data || [{}];
        //@ts-ignore
        setData([...currentData, ...result]);
      };

      // const handleGetOneProposal = async (id: number) => {
      //   const res = await proposalApi.findOne(id);
      //   const finalData = res.data.proposalDetails.map(
      //     (item: any, index: number) => ({
      //       ...item,
      //       key: index,
      //       dateMoment: dayjs(item.date, "YYYY-MM-DD"),
      //       providerId: item?.provider?.id || item.providerId,
      //     })
      //   );

      //   setData(finalData);
      // };

      // const calcMoneyItem = (row: PurchaseOrderDetail) => {
      //   row.subTotalPrice = getFinalNumber(
      //     (row.quantity || 0) * (row.price || 0)
      //   );
      //   row.moneyTax = getFinalNumber(
      //     ((row.taxPercent || 0) / 100) * row.subTotalPrice
      //   );
      //   row.totalPrice = getFinalNumber(row.subTotalPrice + row.moneyTax);
      // };

      /**
       * Mỗi lần đổi department là fetch lại danh sách
      //  */
      // const onOpenProposalSelect = (
      //   isOpen: boolean,
      //   currentDepartment: Department
      // ) => {
      //   if (!currentDepartment && !isBaoBi) {
      //     return message.info("Vui lòng chọn phòng ban đặt hàng trước");
      //   }
      //   if (isOpen) {
      //     queryProposal.departmentId = currentDepartment?.id;
      //     fetchProposals();
      //   }
      // };

      const handleDeleteDetail = (row: ContractDetail) => {
        Modal.confirm({
          title: "Xác nhận xóa?",
          //   content: `Xác nhận xóa "${row.product?.code}"?`,
          cancelText: "Không",
          okText: "Xóa",
          okType: "danger",
          async onOk() {
            try {
              setLoadingTable(true);
              await quoteApi.deleteDetail(row.id);
              onRefresh?.();
              message.success("Xóa thành công");
            } finally {
              setLoadingTable(false);
            }
          },
        });
      };

      const debounceProductSearch = useCallback(
        debounce((keyword) => {
          query.search = keyword;
          fetchProducts();
        }, 300),
        []
      );

      const debounceUnitSearch = useCallback(
        debounce((keyword) => {
          queryUnit.search = keyword;
          fetchData();
        }, 300),
        []
      );

      const getImportFile = async () => {
        // const promises = [
        //   productApi.findAll({ page: 1, limit: 100, isBlocked: false }),
        //   departmentApi.findAll({ page: 1, limit: 100 }),
        // ];

        // const [resProduct, resDepartment] = await Promise.all(promises);

        quoteExportDemo();
      };

      // const openProposal = (proposal: Proposal) => {
      //   if (isBaoBi) {
      //     window.open(getBaoBiProposalLink("view", proposal));
      //   } else {
      //     window.open(getProposalLink("update", proposal));
      //   }
      // };

      const createNewProduct = () => {
        productModalRef.current?.handleOpen("create");
      };
      // console.log("detail data: ", data);

      return (
        <>
          <Space
            style={{
              marginTop: "10px",
              marginBottom: "10px",
              width: "100%",
              justifyContent: readOnly ? "end" : "space-between",
              alignItems: "start",
            }}
          >
            {/* {!readOnly && (
              <Space hidden={true} align="start">
                <UploadExcelLocal
                  onUploadOK={handleImportExcel}
                  sheetPosition={0}
                />
                <Button
                  onClick={() => {
                    getImportFile();
                  }}
                  icon={<FileFilled />}
                >
                  Tải file import mẫu
                </Button>
              </Space>
            )} */}
          </Space>

          <div>
            <Table
              className="table-striped-rows"
              tableLayout="fixed"
              bordered
              loading={loadingTable}
              dataSource={contractProducts}
              size="small"
              pagination={false}
              locale={{
                emptyText: readOnly
                  ? "Danh sách trống"
                  : `Ấn vào dấu "+" để thêm dòng`,
              }}
              scroll={{ y: 400 }}
            >
              {!readOnly && (
                <Column
                  align="center"
                  width={80}
                  title={
                    <Space>
                      <Tooltip placement="top" title="Thêm dòng">
                        <Button
                          className="bg-black"
                          type="primary"
                          ghost
                          icon={<PlusOutlined />}
                          onClick={() => {
                            addRow();
                          }}
                        ></Button>
                      </Tooltip>
                    </Space>
                  }
                  key="name"
                  render={(text, record: ContractDetail, index: number) => (
                    <div className="flex gap-2 justify-center">
                      <Popconfirm
                        title="Dòng này sẽ bị xóa. Tiếp tục?"
                        onConfirm={() => {
                          onDeleteContractProduct?.(index);
                        }}
                      >
                        <DeleteOutlined className="text-red-500 text-lg" />
                      </Popconfirm>
                      <CopyOutlined
                        onClick={() => {
                          const newRecord = { ...record, id: undefined };
                          onAddRow?.(index);
                          // contractDetails.push(newRecord);
                          // handleChangeContractDetails?.([...contractDetails]);
                        }}
                        className="text-blue-500 text-lg"
                      />
                    </div>
                  )}
                />
              )}

              <Column
                width={100}
                title={
                  <Space className="w-full justify-between">
                    <div>
                      Mã PO
                      <span className="text-red-500">*</span>
                    </div>{" "}
                  </Space>
                }
                key="age"
                render={(text, record: ContractProduct, index) =>
                  readOnly ? (
                    record.po
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.poCode}
                      onChange={(e) => {
                        record.poCode = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Số lượng FCL <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={(text, record: ContractProduct, index) =>
                  readOnly ? (
                    formatVND(record.totalFCL)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.totalFCL}
                      onChange={(value) => {
                        record.totalFCL = value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />
              <Column
                align="right"
                width={75}
                title={
                  <>
                    Loại cont <span className="text-red-500">*</span>
                  </>
                }
                key="contType"
                render={(text, record: ContractProduct, index) =>
                  readOnly ? (
                    record.contType
                  ) : (
                    <Select
                      className="w-full"
                      options={optionsCont}
                      value={record.contType}
                      onChange={(value) => {
                        record.contType = value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />

                    // <InputNumberVN
                    //   readOnly={readOnly}
                    //   style={{ width: "100%", textAlign: "right" }}
                    //   value={record?.contNumber}
                    //   onChange={(value) => {
                    //     record.contNumber = value;
                    //     onChangeContractProduct?.(index, record);
                    //   }}
                    // />
                  )
                }
              />
              <Column
                width={150}
                title={
                  <>
                    Thời gian giao hàng
                    <span className="text-red-500">*</span>
                  </>
                }
                key="date"
                render={(
                  text,
                  record: ContractProduct & { date: string },
                  index
                ) =>
                  readOnly ? (
                    record.etdDetail
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.date}
                      onChange={(e) => {
                        record.date = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />
              <Column
                width={150}
                title={
                  <>
                    Thời gian giao hàng (EN)
                    <span className="text-red-500">*</span>
                  </>
                }
                key="date"
                render={(
                  text,
                  record: ContractProduct & { date: string },
                  index
                ) =>
                  readOnly ? (
                    record.etdDetailEn
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.etdDetailEn}
                      onChange={(e) => {
                        record.etdDetailEn = e.target.value;
                        onChangeContractProduct?.(index, record);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Tổng giá trị <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={
                  (text, record: ContractProduct, index) =>
                    // readOnly ? (
                    formatVND2FDs(record?.amount)
                  // ) : (
                  //   <InputNumberVN
                  //     readOnly={readOnly}
                  //     style={{ width: "100%", textAlign: "right" }}
                  //     value={record?.price}
                  //     onChange={(value) => {
                  //       record.price = value;
                  //       onChangeContractProduct?.(index, record);
                  //     }}
                  //   />
                  // )
                }
              />
              <Column
                align="right"
                width={100}
                title={
                  <>
                    Tổng số lượng <span className="text-red-500">*</span>
                  </>
                }
                key="quantity"
                render={
                  (text, record: ContractProduct, index) =>
                    // readOnly ? (
                    formatVND(record.quantity)
                  // ) : (
                  //   <InputNumberVN
                  //     readOnly={readOnly}
                  //     style={{ width: "100%", textAlign: "right" }}
                  //     value={record?.quantity}
                  //     onChange={(value) => {
                  //       record.quantity = value;
                  //       onChangeContractProduct?.(index, record);
                  //     }}
                  //   />
                  // )
                }
              />

              {/* {visibleDelete && (
                <>
                  <Column
                    fixed="right"
                    width={100}
                    title={<>Xóa</>}
                    align="center"
                    key="isComplete"
                    render={(_text, record: ContractDetail, index) => (
                      <Popconfirm
                        title="Dòng này sẽ bị xóa. Tiếp tục?"
                        onConfirm={() => {
                          data.splice(index, 1);
                          setData([...data]);
                        }}
                      >
                        <DeleteOutlined className="text-red-500 text-lg" />
                      </Popconfirm>
                    )}
                  />
                </>
              )} */}
            </Table>
          </div>

          {/* <QuantityLogModal
            ref={quantityLogModalRef}
            onSubmitOk={() => onRefresh?.()}
          />

          <ModalEditNote
            ref={modalEditNoteRef}
            onClose={() => ""}
            onSubmitOk={() => ""}
          /> */}
        </>
      );
    }
  )
);
