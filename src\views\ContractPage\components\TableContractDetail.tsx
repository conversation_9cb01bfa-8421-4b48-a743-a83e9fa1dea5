import { departmentApi } from "@/api/department.api";
import { productApi } from "@/api/product.api";
import { proposalApi } from "@/api/proposal.api";
import { UploadExcelLocal } from "@/components/FileUpload/UploadLocal";
import { InputNumberVN } from "@/components/Input/InputNumberVN";
import { ProductSelectorRef } from "@/components/ProductSelector/ProductSelector";
import { useDepartment } from "@/hooks/useDepartMent";
import { useProduct } from "@/hooks/useProduct";
import { useProposal } from "@/hooks/useProposal";
import { useTaxConfig } from "@/hooks/useTaxConfig";
import { useUnit } from "@/hooks/useUnit";
import { Department, DepartmentType } from "@/types/department";
import { ModalStatus } from "@/types/modal";
import { Product } from "@/types/product";
import {
  Proposal,
  ProposalDetail,
  ProposalStatus,
  ProposalType,
} from "@/types/proposal";
import {
  PurchaseOrder,
  PurchaseOrderDetail,
  PurchaseOrderType,
} from "@/types/purchaseOrder";
import { formatVND, formatVND2FDs } from "@/utils";
import { getDateExcel } from "@/utils/dateFormat";
import purchaseExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/purchaseExportDemol";
import { calcMoneyItem2 } from "@/utils/money";
import { getFinalNumber } from "@/utils/number";
import {
  getBaoBiProposalLink,
  getDepartmentLink,
  getProposalLink,
} from "@/utils/url";
import { ProductItem } from "@/views/Product/components/ProductItem";
import {
  ProductModal,
  ProductModalRef,
} from "@/views/Product/components/ProductModal";
import {
  CopyOutlined,
  DeleteOutlined,
  FileFilled,
  PlusOutlined,
} from "@ant-design/icons";
import {
  Button,
  Checkbox,
  DatePicker,
  Empty,
  Input,
  InputNumber,
  Modal,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
  Tooltip,
  message,
} from "antd";
import Column from "antd/es/table/Column";
import clsx from "clsx";
import dayjs from "dayjs";
import { debounce, isNumber } from "lodash";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

import { purchaseApi } from "@/api/purchase.api";
import { Quote, QuoteDetail } from "@/types/quote";
import { settings } from "../../../../settings";
import { quoteApi } from "@/api/quote.api";
import quoteExportDemo from "@/utils/importExcel/Excel/downloadExcelDemo/quoteExportDemo";
import {
  Contract,
  ContractDetail,
  ContractDetailETDTypeTrans,
  ContractProduct,
  ContractStatus,
  ContractUnits,
} from "@/types/contract";
import moment from "moment";
import { PiListPlusBold } from "react-icons/pi";
import { userStore } from "@/store/userStore";
import { PermissionName } from "@/router";
import TextArea from "antd/es/input/TextArea";
import { useCustomerProduct } from "@/hooks/useCustomerProduct";
import { customerProductApi } from "@/api/customerProduct.api";
import { FaCartPlus } from "react-icons/fa6";

export interface TableContractDetailRef {
  getValue: () => {
    contractDetails: ContractDetail[];
    amount: number;
  };
  setValue: (contract: Partial<Contract>) => void;
}
export const validateContractDetails = (
  details: Partial<ContractDetail>[],
  status?: ModalStatus
) => {
  for (const [i, item] of details.entries()) {
    if (!item.productName) return `Dòng ${i + 1}: Chưa nhập tên mặt hàng`;
    if (!item.specification)
      return `Dòng ${i + 1}: Chưa nhập đặc điểm kỹ thuật`;
    if (!item.specificationEn)
      return `Dòng ${i + 1}: Chưa nhập đặc điểm kỹ thuật (EN)`;
    if (!item.unitId && !item.unit)
      return `Dòng ${i + 1}: Chưa chọn đơn vị tính`;
    if (!item.quantity) return `Dòng ${i + 1}: Chưa nhập số lượng`;
    if (!item.unitPrice) return `Dòng ${i + 1}: Chưa nhập đơn giá`;
    if (!item.packing) return `Dòng ${i + 1}: Chưa nhập đóng gói`;
    if (!item.code) return `Dòng ${i + 1}: Chưa nhập mã quy trình`;
    if (item.quantityUnit === undefined || item.quantityUnit === null)
      return `Dòng ${i + 1}: Chưa nhập Đơn vị/1 thùng	`;
    if (status === "create" && !item.thermometer)
      return `Dòng ${i + 1}: Chưa chọn nhiệt kế cont lạnh`;
  }
  return null;
};

export const TableContractDetail = observer(
  React.forwardRef(
    (
      {
        readOnly = false,
        disableAction = false,
        status,
        visibleDelete,
        onRefresh,
        onChangeTotalAmount,
        onChangeTotalQuantity,
        contractDetails,
        handleChangeContractDetails,
        onAddRow,
        customerId,
        statusProject,
        onClickPackagingProposal,
      }: {
        readOnly?: boolean;
        disableAction?: boolean;
        status: ModalStatus;
        visibleDelete?: boolean;
        onRefresh?: () => void;
        onChangeTotalAmount?: (total: number) => void;
        onChangeTotalQuantity?: (total: number) => void;
        handleChangeContractDetails?: (
          details: Partial<ContractDetail>[]
        ) => void;
        contractDetails: Partial<ContractDetail>[];
        onAddRow?: () => void;
        customerId?: string | number;
        statusProject?: any;
        onClickPackagingProposal?: (value: ContractDetail) => void;
      },
      ref
    ) => {
      const isEditProcedureCode = userStore.checkRole(
        PermissionName.editProcedureCode
      );

      const {
        fetchData,
        units,
        query: queryUnit,
      } = useUnit({
        initQuery: { page: 1, limit: 0 },
      });

      const productModalRef = useRef<ProductModalRef>();
      const productSelectorRef = useRef<ProductSelectorRef>();
      const [isCheckAll, setIsCheckAll] = useState<boolean>(false);
      const [contract, setContract] = useState<Contract>();

      // const modalEditNoteRef = useRef<ModalEditNoteRef>();
      const {
        fetchData: fetchProducts,
        products,
        loading,
        query,
        total,
      } = useProduct({
        initQuery: { page: 1, limit: 50 },
      });

      const { customerProducts, fetchCustomerProducts } = useCustomerProduct({
        initQuery: { page: 1, limit: 100 },
      });

      useEffect(() => {
        fetchCustomerProducts({ customerId: customerId ?? -1 });
      }, [customerId]);

      const [loadingTable, setLoadingTable] = useState(false);
      const [POCodeStart, setPOCodeStart] = useState<number>();

      const moneyValue = useRef({
        moneyTax: 0,
        subTotal: 0,
        amount: 0,
      });

      const handleAddRow = () => {
        handleChangeContractDetails?.([
          ...contractDetails,
          {
            productName: "",
            quantity: 0,
            unitPrice: 0,
            amount: 0,
            contractPackingDetails: [
              {
                nameVi: "Thùng",
                nameEn: "Container",
                productCode: "",
                contractQuantity: 0,
                quantityOnUnit: 0,
                note: "",
              },
            ],
          } as any,
        ]);
      };

      const handleChangeCell = (
        index: number,
        field: keyof ContractDetail,
        value: any
      ) => {
        const updated = [...contractDetails];
        updated[index] = { ...updated[index], [field]: value };

        // Tính tổng tiền tự động:
        if (field === "quantity" || field === "unitPrice") {
          const quantity = updated[index].quantity || 0;
          const unitPrice = updated[index].unitPrice || 0;
          updated[index].amount = quantity * unitPrice;
        }

        handleChangeContractDetails?.(updated);
      };

      const handleDeleteDetailRow = (index: number) => {
        const updated = [...contractDetails];
        updated.splice(index, 1);
        handleChangeContractDetails?.(updated);
      };

      useEffect(() => {
        // fetchData();
        fetchProducts();
      }, []);

      const calculateTotalAmount = (data: Partial<ContractDetail>[]) => {
        const totalAmount = data.reduce(
          (sum, item) => sum + (item.amount || 0),
          0
        );
        moneyValue.current.amount = totalAmount;
        return totalAmount;
      };

      const debounceUnitSearch = useCallback(
        debounce((keyword) => {
          queryUnit.search = keyword;
          fetchData();
        }, 300),
        []
      );

      const addRow = (customData?: Partial<ContractProduct>[]) => {
        // if (customData) {
        //   setData([...customData]);
        // } else {
        //   setData([...data, { quantity: 0 }]);
        //   // updateQuantity([...data]);
        // }
        onAddRow?.();
      };

      const updateDataSelectProduct = async (
        id: number | string,
        index: number
      ) => {
        const product = customerProducts.find((el) => el.id == id);
        if (product) {
          const res = await customerProductApi.findOne(product.id);

          const data = res.data;

          contractDetails[index] = {
            ...contractDetails[index],
            productName: data.name,
            thermometer: "20",
            contractPackingDetails: data?.customerProductDetails?.map(
              (item: any) => ({
                nameEn: item.nameEn,
                nameVi: item.nameVi,
                length: item.length,
                width: item.width,
                height: item.height,
                contractQuantity: 0,
                quantityOnUnit: 0,
              })
            ),
          };
          handleChangeContractDetails?.([...contractDetails]);
        }
      };

      return (
        <>
          <Space
            style={{
              marginTop: "10px",
              marginBottom: "10px",
              width: "100%",
              justifyContent: readOnly ? "end" : "space-between",
              alignItems: "start",
            }}
          >
            {/* {!readOnly && (
              <Space hidden={true} align="start">
                <UploadExcelLocal
                  onUploadOK={handleImportExcel}
                  sheetPosition={0}
                />
                <Button
                  onClick={() => {
                    getImportFile();
                  }}
                  icon={<FileFilled />}
                >
                  Tải file import mẫu
                </Button>
              </Space>
            )} */}
          </Space>

          <div>
            <Table
              className="table-striped-rows"
              tableLayout="fixed"
              bordered
              loading={loadingTable}
              dataSource={contractDetails}
              size="small"
              pagination={false}
              locale={{
                emptyText: readOnly
                  ? "Danh sách trống"
                  : `Ấn vào dấu "+" để thêm dòng`,
              }}
              scroll={{ y: 400 }}
            >
              {!readOnly && (
                <Column
                  align="center"
                  width={80}
                  title={
                    <Space>
                      <Tooltip placement="top" title="Thêm dòng">
                        <Button
                          className="bg-black"
                          type="primary"
                          ghost
                          icon={<PlusOutlined />}
                          onClick={() => {
                            handleAddRow();
                            addRow();
                          }}
                        ></Button>
                      </Tooltip>
                    </Space>
                  }
                  key="action"
                  render={(text, record: ContractDetail, index: number) => (
                    <div className="flex gap-2 justify-center">
                      <Popconfirm
                        title="Dòng này sẽ bị xóa. Tiếp tục?"
                        onConfirm={() => {
                          contractDetails.splice(index, 1);
                          handleChangeContractDetails?.([...contractDetails]);
                        }}
                      >
                        <DeleteOutlined className="text-red-500 text-lg" />
                      </Popconfirm>

                      <CopyOutlined
                        onClick={() => {
                          const newRecord = { ...record, id: undefined };
                          contractDetails.push(newRecord);
                          handleChangeContractDetails?.([...contractDetails]);
                        }}
                        className="text-blue-500 text-lg"
                      />
                    </div>
                  )}
                />
              )}

              {/* {
                (status === "create" || status === "update") &&
                <Column
                  width={150}
                  title={
                    <Space className="w-full justify-between">
                      <div className="flex">
                        Chọn mặt hàng <span className="text-red-500"></span>
                      </div>{" "}
                    </Space>
                  }
                  key="name"
                  render={(text, record: ContractDetail, index) =>
                    <Select
                      className="w-full"
                      options={customerProducts.map((product) => ({
                        value: product.id,
                        label: `${product.code} - ${product.name}`
                      }))}
                      onChange={(e) => { updateDataSelectProduct(e, index) }}
                    >

                    </Select>
                    // <Input
                    //   readOnly={readOnly}
                    //   value={record?.productName}
                    //   onChange={(e) => {
                    //     record.productName = e.target.value;
                    //     handleChangeContractDetails?.([...contractDetails]);
                    //   }}
                    // />
                  }
                />
              } */}

              <Column
                width={200}
                title={
                  <Space className="w-full justify-between">
                    <div className="flex">
                      Tên mặt hàng <span className="text-red-500">*</span>
                    </div>{" "}
                  </Space>
                }
                key="name"
                render={(text, record: ContractDetail) =>
                  readOnly ? (
                    record.productName
                  ) : (
                    <Input
                      readOnly={readOnly}
                      value={record?.productName}
                      onChange={(e) => {
                        record.productName = e.target.value;
                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                    />
                  )
                }
              />
              <Column
                width={100}
                align="center"
                title={<>ASC CF - ASC NCF</>}
                key="asc"
                render={(text, record: ContractDetail) => (
                  <div className="flex justify-center gap-6">
                    <Checkbox
                      checked={!!record.ascCF}
                      disabled={readOnly}
                      onChange={(e) => {
                        if (e.target.checked) {
                          record.ascCF = true;
                          record.ascNCF = false;
                        } else {
                          record.ascCF = false;
                        }

                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                    />
                    <Checkbox
                      checked={!!record.ascNCF}
                      disabled={readOnly}
                      onChange={(e) => {
                        if (e.target.checked) {
                          record.ascNCF = true;
                          record.ascCF = false;
                        } else {
                          record.ascNCF = false;
                        }
                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                    />
                  </div>
                )}
              />

              <Column
                width={150}
                title={
                  <>
                    Đặc điểm kỹ thuật
                    <span className="text-red-500">*</span>
                  </>
                }
                key="spec"
                render={(text, record: ContractDetail) =>
                  readOnly ? (
                    <TextArea readOnly={true} value={record?.specification} />
                  ) : (
                    <TextArea
                      readOnly={readOnly}
                      rows={1}
                      value={record?.specification}
                      onChange={(e) => {
                        record.specification = e.target.value;
                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                    />
                  )
                }
              />
              <Column
                width={150}
                title={
                  <>
                    Đặc điểm kỹ thuật (EN)
                    <span className="text-red-500">*</span>
                  </>
                }
                key="spec"
                render={(text, record: ContractDetail) =>
                  readOnly ? (
                    <TextArea readOnly={true} value={record?.specificationEn} />
                  ) : (
                    <TextArea
                      readOnly={readOnly}
                      rows={1}
                      value={record?.specificationEn}
                      onChange={(e) => {
                        record.specificationEn = e.target.value;
                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                    />
                  )
                }
              />
              {status === "create" && (
                <Column
                  width={125}
                  title={
                    <>
                      Nhiệt kế cont lạnh (cái){" "}
                      <span className="text-red-500">*</span>
                    </>
                  }
                  key="thermometer"
                  render={(text, record: ContractDetail, index) =>
                    readOnly ? (
                      <span>{record?.thermometer || 0}</span>
                    ) : (
                      <InputNumberVN
                        min={0}
                        style={{ width: "100%" }}
                        value={
                          record?.thermometer
                            ? Number(record.thermometer)
                            : undefined
                        }
                        onChange={(value) => {
                          record.thermometer = value || undefined;
                          handleChangeContractDetails?.([...contractDetails]);
                        }}
                      />
                    )
                  }
                />
              )}
              <Column
                width={150}
                title={
                  <>
                    Đơn vị tính
                    <span className="text-red-500">*</span>
                  </>
                }
                key="unit"
                render={(text, record: ContractDetail) =>
                  readOnly ? (
                    ContractUnits.find((item) => item.id === record?.unit?.id)
                      ?.nameEn
                  ) : (
                    <Select
                      allowClear
                      onChange={(id, option: any) => {
                        record.unitId = id;
                        record.unit = option?.unit;
                        handleChangeContractDetails?.([...contractDetails]);
                      }}
                      showSearch
                      filterOption={false}
                      onSearch={debounceUnitSearch}
                      style={{ width: "100%" }}
                      options={ContractUnits.map((item) => ({
                        label: `${item?.nameEn}`,
                        value: item?.id,
                        unit: item,
                      }))}
                      value={record.unitId || record.unit?.id}
                      popupMatchSelectWidth={false}
                    />
                    // <Input
                    //   readOnly={readOnly}
                    //   value={record?.name}
                    //   onChange={(e) => {
                    //     record.name = e.target.value;
                    //     setData([...data]);
                    //   }}
                    // />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Số lượng <span className="text-red-500">*</span>
                  </>
                }
                key="quantityFinal"
                render={(text, record: ContractDetail, index) =>
                  readOnly ? (
                    formatVND(record.quantity ?? 0)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.quantityFinal}
                      onChange={(value) => {
                        record.quantityFinal = value;
                        handleChangeCell(index, "quantityFinal", value);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Đơn vị/1 thùng <span className="text-red-500">*</span>
                  </>
                }
                key="quantityUnit"
                render={(text, record: ContractDetail, index) =>
                  readOnly ? (
                    <span>{formatVND(record?.quantityUnit)}</span>
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.quantityUnit}
                      onChange={(value) => {
                        record.quantityUnit = value;
                        handleChangeCell(index, "quantityUnit", value);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Đơn giá <span className="text-red-500">*</span>
                  </>
                }
                key="unitPrice"
                render={(text, record: ContractDetail, index) =>
                  readOnly ? (
                    formatVND(record.unitPrice)
                  ) : (
                    <InputNumberVN
                      readOnly={readOnly}
                      style={{ width: "100%", textAlign: "right" }}
                      value={record?.unitPrice}
                      onChange={(value) => {
                        record.unitPrice = value;
                        record.amount = (record.quantity || 0) * (value || 0);
                        handleChangeCell(index, "unitPrice", value);
                      }}
                    />
                  )
                }
              />

              <Column
                align="right"
                width={100}
                title={
                  <>
                    Đóng gói <span className="text-red-500">*</span>
                  </>
                }
                key="packing"
                render={(text, record: ContractDetail, index) =>
                  readOnly ? (
                    <span>{record?.packing}</span>
                  ) : (
                    <TextArea
                      readOnly={readOnly}
                      rows={1}
                      value={record?.packing}
                      onChange={(e) => {
                        record.packing = e.target.value;
                        handleChangeCell(index, "packing", e.target.value);
                      }}
                    />
                  )
                }
              />

              <Column
                width={100}
                title={
                  <>
                    Mã quy trình
                    <span className="text-red-500">*</span>
                  </>
                }
                key="code"
                render={(text, record: ContractDetail, index) =>
                  readOnly ? (
                    record.code
                  ) : (
                    <Input
                      disabled={!isEditProcedureCode}
                      readOnly={readOnly}
                      value={record?.code}
                      onChange={(e) => {
                        record.code = e.target.value;
                        handleChangeCell(index, "code", e.target.value);
                      }}
                    />
                  )
                }
              />
              {statusProject === ContractStatus.Approved && (
                <Column
                  width={120}
                  title={<>Sinh đề nghị bao bì</>}
                  key="code"
                  render={(text, record: ContractDetail, index) => (
                    <Button
                      onClick={() => {
                        onClickPackagingProposal?.(record);
                      }}
                      className="mb-2"
                      size="small"
                      type="primary"
                      icon={
                        <div className="translate-y-0.5">
                          <PlusOutlined />
                        </div>
                      }
                    />
                  )}
                />
              )}
            </Table>
          </div>

          <ProductModal
            ref={productModalRef}
            onSubmitOk={() => {
              productSelectorRef.current?.refreshData();
              fetchProducts();
            }}
          />
        </>
      );
    }
  )
);
