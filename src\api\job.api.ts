import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const jobApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/job",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/job",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/job/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/job/${id}`,
      method: "delete",
    }),
  deleteBatch: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/job/batch`,
      method: "delete",
      data,
    }),
};
