import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const dateConfigApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dateConfig",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dateConfig",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dateConfig/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dateConfig/${id}`,
      method: "delete",
    }),
};
