import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const holidayApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/holiday",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/holiday",
      data,
      method: "post",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/holiday/import",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/holiday/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/holiday/${id}`,
      method: "delete",
    }),
};
