import { proposalApi } from "@/api/proposal.api";
import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const commentApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/`,
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/comment",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}`,
      method: "patch",
      data,
    }),
  active: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}/active`,
      method: "patch",
    }),
  inactive: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}/inactive`,
      method: "patch",
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/comment/${id}`,
      method: "delete",
    }),
};
