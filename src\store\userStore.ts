import ROneSignal from "react-onesignal";
import { action, makeAutoObservable, toJS } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { checkRole, setToken } from "../utils/auth";
import { Staff } from "../types/staff";
import { settings } from "../../settings";
import { authApi } from "../api/auth.api";
import { oneSignalApi } from "@/api/oneSignal";
import { PermissionName, checkInPermissionName } from "@/router";

export class UserStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "UserStore",
      properties: ["info", "token"],
      storage: window.localStorage,
    });
  }
  isLoggedIn: boolean = false;
  info: Partial<Staff> = {};
  token = "";

  async login(username: string, password: string) {
    const res = await authApi.login({ username, password });
    setToken(res.data.token);
    this.token = res.data.token;
    return res.data;
  }

  assignToken(token: string) {
    setToken(token);
    this.token = token;
  }
  //lấy các quyền menu công đoạn
  get jobPermission() {
    return {
      create: this.checkRole(PermissionName.createJob),
      edit: this.checkRole(PermissionName.editJob),
    };
  }
  //Is develop
  get isDev() {
    return this.info.isDev;
  }
  //lấy các quyền cấu hình hệ số
  get salaryCoefficientPermission() {
    return {
      edit: this.checkRole(PermissionName.editSalaryCoefficient),
    };
  }

  get hasPayrollSalaryRole() {
    return (
      this.checkRole(PermissionName.payrollSalary) &&
      window.location.pathname == PermissionName.payrollSalary
    );
  }
  get hasSearchPayrollSalaryRole() {
    return (
      this.checkRole(PermissionName.searchPayrollSalary) &&
      window.location.pathname == PermissionName.searchPayrollSalary
    );
  }
  get hasViewProviderRole() {
    return (
      this.checkRole("view-provider-info") ||
      userStore.info.role?.isAccounting ||
      userStore.info.role?.isAdmin
    );
  }

  get managerProductionRole() {
    return {
      isAnnunciator: this.checkRole(PermissionName.QLSXAnnunciator),
      hasDeleteReportRole: this.checkRole(
        PermissionName.deleteProductionConditionReport
      ),
      isFixer: this.checkRole(PermissionName.QLSXFixer),
      isInspector: this.checkRole(PermissionName.QLSXInspector),
      viewAllReport: this.checkRole(
        PermissionName.viewAllProductionConditionReport
      ),
      taoBaoBi: this.checkRole(PermissionName.taoBaoBi),
      xemTatCaBaoBi: this.checkRole(PermissionName.xemTatCaBaoBi),
      xemTatCaKhachHang: this.checkRole(PermissionName.xemTatCaKhachHang),
      capKhachHangCuaMinh: this.checkRole(
        PermissionName.capNhatKhachHangCuaMinh
      ),
      capNhatTatCaKhachHang: this.checkRole(
        PermissionName.capNhatTatCaKhachHang
      ),
      xemTatCaListSanXuat: this.checkRole(PermissionName.xemTatCaListSanXuat),
      xemTatCaQuyTrinhChiTiet: this.checkRole(
        PermissionName.xemTatCaQuyTrinhChiTiet
      ),
      xemTatCaQuyTrinhDonGian: this.checkRole(
        PermissionName.xemTatCaQuyTrinhDonGian
      ),

      xemTatCaBaoBiDuocTag: this.checkRole(PermissionName.xemTatCaBaoBiDuocTag),
      xemTatCaListSanXuatDuocTag: this.checkRole(
        PermissionName.xemTatCaListSanXuatDuocTag
      ),
      xemTatCaQuyTrinhChiTietDuocTag: this.checkRole(
        PermissionName.xemTatCaQuyTrinhChiTietDuocTag
      ),
      xemTatCaQuyTrinhDonGianDuocTag: this.checkRole(
        PermissionName.xemTatCaQuyTrinhDonGianDuocTag
      ),
    };
  }

  checkRole(roleName: string) {
    return checkRole(roleName, this.info.role?.permissions);
  }

  checkRoleByName(roleName: string) {
    return this.info.role?.permissions.some(e => e.name == roleName);
  }

  get isCheckInRoleOnly() {
    return userStore.info.role?.permissions.every((p) =>
      checkInPermissionName.includes(p.name as PermissionName)
    );
  }

  async getProfile() {
    const res = await authApi.profile();
    this.info = res.data;
    this.isLoggedIn = true;
  }

  logout = async () => {
    // if (!settings.isDev) {
    //   ROneSignal.getSubscription(() => {
    //     ROneSignal.setSubscription(false);
    //   });
    // }
    const oneSignalId = localStorage.getItem("oneSignalId");
    if (oneSignalId && this.token) {
      await oneSignalApi.unSub({ oneSignalId: oneSignalId });
    }

    this.isLoggedIn = false;
    setToken("");
    this.token = "";
  };
}

const userStore = new UserStore();

export { userStore };
