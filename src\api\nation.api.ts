import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const nationApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/nation",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/nation",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/nation/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/nation/${id}`,
      method: "delete",
    }),
};
