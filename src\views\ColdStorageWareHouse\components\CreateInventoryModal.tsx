import { fileAttachApi } from "@/api/uploadFile.api";
import { userStore } from "@/store/userStore";
import { FileAttachType } from "@/types/file";
import { ModalStatus } from "@/types/modal";
import { v4 as uuidv4 } from "uuid";

import { useProvider } from "@/hooks/useProvider";
import { Proposal, ProposalStatus } from "@/types/proposal";

import { Staff, StaffGroupAction } from "@/types/staff";

import {
  ArrowRightOutlined,
  CloseOutlined,
  ContainerFilled,
  InfoCircleFilled,
  InfoCircleTwoTone,
  SaveFilled,
  WifiOutlined,
} from "@ant-design/icons";
import { useNetworkState } from "@uidotdev/usehooks";
import {
  Button,
  Checkbox,
  DatePicker,
  Flex,
  Form,
  Input,
  Modal,
  Popover,
  Radio,
  Select,
  Spin,
  Tooltip,
  message,
  notification,
} from "antd";
import { useForm, useWatch } from "antd/es/form/Form";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { toJS } from "mobx";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useSearchParams } from "react-router-dom";

import { categoryApi } from "@/api/category.api";
import { inventoryApi } from "@/api/inventory.api";
import { purchaseApi } from "@/api/purchase.api";
import { StockSelector } from "@/components/Selector/StockSelector";
import TableListStaffGroup from "@/components/StaffGroupComponents/TableListStaffGroup";
import { useDepartment } from "@/hooks/useDepartMent";
import { usePurchase } from "@/hooks/usePurchase";
import { Category } from "@/types/category";
import {
  Inventory,
  InventoryDetail,
  InventoryType,
  InventoryTypeTrans,
  categoryByFreezeInventory,
  categoryByInventory,
} from "@/types/inventoryDetail";
import { PurchaseOrder, PurchaseOrderType } from "@/types/purchaseOrder";
import { getTitleModalByType } from "@/utils/inventory";
import { InventoryLocal } from "@/utils/inventoryLocal";
import {
  InventoryCheckForm,
  InventoryCheckFormRef,
} from "@/views/PurchasePage/components/QuantityLog/InventoryCheckForm";
import { QuantityLogFormRef } from "@/views/PurchasePage/components/QuantityLog/InventoryImportForm";
import { BREAKPOINTS } from "@/views/SuggestionPage/SuggestionPage";
import { useBreakpoint } from "use-breakpoint";
import { settings } from "../../../../settings";
import TableErrorReturnSlip, {
  TableErrorReturnSlipRef,
} from "./Table/TableErrorReturnSlip";
import TableInReceiveProduct, {
  TableInReceiveProductRef,
} from "./Table/TableInReceiveProduct";
import {
  TableInventoryChange,
  TableInventoryChangeRef,
} from "./Table/TableInventoryChange";
import TableOutSendProduct, {
  TableOutSendProductRef,
} from "./Table/TableOutSendProduct";
import TableReceiveProductError, {
  TableReceiveProductErrorRef,
} from "./Table/TableReceiveProductError";
import TableShipmentSlip, {
  TableExportInventoryRef,
} from "./Table/TableExportInventory";
import { Stock } from "@/types/stock";
import { DepartmentType } from "@/types/department";
import { requiredRule } from "@/utils/validate-rules";
import { disabledDate } from "@/utils/date";
import ImportInventory, {
  ExportInventoryTypeTrans,
  ImportInventoryTypeTrans,
  InventoryMode,
} from "./WarehouseList/ImportInventory";
import {
  TableImportInventory,
  TableImportInventoryRef,
} from "./Table/TableImportInventory";
import { Module } from "@/types/product";
import { mode } from "mathjs";
import { InventoryImportForm } from "./WarehouseList/components/InventoryImportForm";
import { ColdStorageProductSelector } from "@/components/Selector/ColdStorageProductSelector";
import { ContractSelector } from "@/components/Selector/ContractSelector";
import TableExportInventory from "./Table/TableExportInventory";
import { TableChangeProduct, TableChangeProductRef } from "./Table/TableChangeProduct";

interface ModalCreateShipmentSlipProps {
  fetchData: () => void;
  onOk: (isDraff?: boolean) => void;
  type?: InventoryType;
  onCancel: () => void;
}

export interface ModalCreateInventoryRef {
  handleOpen: (
    type: InventoryType,
    data?: Inventory,
    isOpenTemp?: boolean
  ) => void;
  handleUpdate: (record: Inventory) => void;
  createWithPurchase: (purchase: PurchaseOrder) => void;
  createWithProposal: (proposal: Proposal) => void;
}

const CreateInventoryModal = React.forwardRef(
  ({ fetchData, onOk, onCancel }: ModalCreateShipmentSlipProps, ref) => {
    let [searchParams, setSearchParams] = useSearchParams();
    const tableExportInventoryRef = useRef<TableExportInventoryRef>();
    const tableErrorReturnSlipRef = useRef<TableErrorReturnSlipRef>();
    const tableReceiveProductError = useRef<TableReceiveProductErrorRef>();
    const tableOutSendProductRef = useRef<TableOutSendProductRef>();
    const tableInReceiveProductRef = useRef<TableInReceiveProductRef>();
    const quantityLogFormRef = useRef<QuantityLogFormRef>();
    const inventoryCheckFormRef = useRef<InventoryCheckFormRef>();
    const tableInventoryChangeRef = useRef<TableInventoryChangeRef>();
    const importInventoryRef = useRef<{ getValue: () => any }>(null);
    const tableImportInventoryRef = useRef<TableImportInventoryRef>(null);
    const tableChangeProductRef = useRef<TableChangeProductRef>(null);

    const [form] = useForm();

    const providerId = useWatch("providerId", form);
    const departmentId = useWatch("departmentId", form);
    const fromStockId = useWatch("fromStockId", form);
    const toStockId = useWatch("toStockId", form);
    const contractId = useWatch("contractId", form);

    const [details, setDetails] = useState<any>();
    const [status, setStatus] = useState<ModalStatus>("create");
    const [inspecStaffs, setInspecStaffs] = useState<Staff[]>([]);
    const [supperStaff, setSupperStaff] = useState<Staff>();
    const [followStaffs, setFollowStaffs] = useState<Staff[]>([]);
    const [visible, setVisible] = useState(false);
    const [type, setType] = useState<InventoryType>();
    const [loading, setLoading] = useState<boolean>(false);
    const [isDisabledStock, setIsDisabledStock] = useState(false);
    const fromStockValue = useRef<Stock>();
    const toStockValue = useRef<Stock>();
    const [inventoryTempSelected, setInventoryTempSelected] =
      useState<Inventory>();
    const [isCompletePo, setIsCompletePo] = useState<boolean>(false);
    const [importInventoryMode, setImportInventoryMode] =
      useState<InventoryMode>();
    const {
      fetchData: fetchPurchase,
      purchaseOrders,
      query: queryPurchase,
    } = usePurchase({
      initQuery: {
        page: 1,
        limit: 50,
        isInProcessing: true,
        type: PurchaseOrderType.PO,
      },
    });
    const { breakpoint } = useBreakpoint(BREAKPOINTS, "tablet");
    const [loadingFetchPO, setLoadingFetchPO] = useState(false);

    const {
      fetchData: fetchProviders,
      providers,
      query: queryProvider,
    } = useProvider({
      initQuery: { page: 1, limit: 50 },
    });

    const {
      fetchData: fetchDepartment,
      departments,
      query: queryDepartment,
    } = useDepartment({
      initQuery: {
        page: 1,
        limit: 20,
        type: DepartmentType.Admin,
      },
    });

    //Một số phiếu không có chữ ký thì không được lưu phiếu kho
    const isDisableSaveBtn = useMemo(
      () =>
        settings.requireSignal.includes(type as InventoryType) &&
        !userStore.info.signatureImage2 &&
        !userStore.info.signatureImage,
      [type, userStore.info]
    );

    const network = useNetworkState();

    const [api, contextHolder] = notification.useNotification();
    const netWorkErrorNotification = (action: string) => {
      if (action === "OPEN") {
        api.open({
          message: (
            <div className="flex gap-2">
              <WifiOutlined className="text-red-500" />
              <span className="text-lg text-red-500 font-semibold">
                Không có kết nối mạng!
              </span>
            </div>
          ),
          description:
            "Bạn đang offline, hãy kiểm tra lại đường truyền và tiếp tục thao tác.",
          duration: 0,
          placement: "bottomRight",
        });
      } else {
        api.destroy();
      }
    };

    const netWorkConnectNotification = (action: string) => {
      if (action === "OPEN") {
        api.open({
          message: (
            <div className="flex gap-2">
              <WifiOutlined className="text-green-500" />
              <span className="text-lg text-green-500 font-semibold">
                Kết nối mạng đã được khôi phục!
              </span>
            </div>
          ),
          description: "Đã có kết nối mạng trở lại, bạn có thể tiếp tục.",
          duration: 0,
          placement: "bottomRight",
        });
      } else {
        api.destroy();
      }
    };

    useEffect(() => {
      if (!network.online) {
        netWorkConnectNotification("CLOSE");
        netWorkErrorNotification("OPEN");
      } else {
        netWorkErrorNotification("CLOSE");
        netWorkConnectNotification("OPEN");
      }
    }, [network.online]);

    /**
     * Lấy danh sách nhóm staffs mặc định
     * @param type
     * @param followStaff
     * @param inspecStaff
     */
    const handleGetStaffGroup = async (type: InventoryType) => {
      //@ts-ignore
      const categoryType = categoryByFreezeInventory[type];
      console.log("Categorytype là", categoryType);
      const { data } = await categoryApi.findAll({
        type: categoryType,
      });
      const staffList: Category = data.categories?.[0];
      console.log("Stafflist trả về gì v", staffList);
      setFollowStaffs(staffList?.followStaffs || []);
      setInspecStaffs(staffList?.inspecStaffs || []);
    };

    useImperativeHandle(
      ref,
      () => {
        return {
          /**
           * Fill lại data vào form
           */
          handleOpen(
            type: InventoryType,
            data: Inventory,
            isOpenTemp: boolean
          ) {
            if (type == InventoryType.In) {
              fetchPurchase();
            }

            if (type === InventoryType.Out) {
              // Lấy danh sách phòng ban khi tạo phiếu xuất
              console.log("data lúc này là", data);
              fetchDepartment(data?.department);
            } else {
              // Lấy danh sách nhà cung cấp khi tạo phiếu nhận + trả hàng lỗi
              fetchProviders(data?.provider);
            }
            if (data) {
              switch (type) {
                case InventoryType.In:
                  setTimeout(() => {
                    //phải settimeout để tránh không component chưa xuất hiện kịp
                    quantityLogFormRef.current?.handleOpenAll(data);
                  }, 100);

                  break;
                case InventoryType.Out:
                  setTimeout(() => {
                    tableExportInventoryRef.current?.setValue(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;
                case InventoryType.OutReturnProductError:
                  setTimeout(() => {
                    tableErrorReturnSlipRef.current?.setValue(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;
                case InventoryType.InReceiveProductError:
                  setTimeout(() => {
                    tableReceiveProductError.current?.setValue(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;
                case InventoryType.InReceiveProduct:
                  setTimeout(() => {
                    tableInReceiveProductRef.current?.setValue(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;
                case InventoryType.OutSendProduct:
                  setTimeout(() => {
                    tableOutSendProductRef.current?.setValue(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;
                case InventoryType.Check:
                  setTimeout(() => {
                    inventoryCheckFormRef.current?.handleOpenAll(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;

                case InventoryType.Change:
                  setTimeout(() => {
                    tableInventoryChangeRef.current?.handleOpenAll(
                      //@ts-ignore
                      data
                    );
                  }, 100);
                  break;

                case InventoryType.ChangeProduct:
                  setTimeout(() => {
                    //@ts-ignore
                    tableChangeProductRef.current?.setValue(

                      data
                    );
                  }, 100);
                  break;
              }

              setFollowStaffs(data?.followStaffs);
              setInspecStaffs(data?.inspecStaffs);
              setInventoryTempSelected(data);
              form.setFieldsValue(data);
              setType(type);
              setVisible(true);
              setIsDisabledStock(data.inventoryDetails.length > 0);
            } else {
              fetchDepartment();
              form.resetFields();
              form.setFieldValue("isAllInspec", type == InventoryType.Out);
              setType(type);
              setVisible(true);
              handleGetStaffGroup(type);
              setInventoryTempSelected(undefined);
              setIsDisabledStock(false);
            }
          },

          createWithPurchase(purchase: PurchaseOrder) {
            fetchPurchase(purchase);
            fetchProviders(purchase.provider);
            if (purchase) {
              setInventoryTempSelected(undefined);
              form.setFieldsValue({
                providerId: purchase.provider?.id,
                purchaseOrderId: purchase.id,
                name: `Nhập kho của ${purchase?.provider?.name}`,
              });
              setType(InventoryType.In);
              handleGetStaffGroup(InventoryType.In);
              setVisible(true);
              setTimeout(() => {
                quantityLogFormRef.current?.fillTableWithPurchase(purchase);
              }, 100);
            }
          },

          createWithProposal(proposal: Proposal) {
            if (proposal) {
              setInventoryTempSelected(undefined);
              form.setFieldsValue({
                departmentId: proposal.department?.id,
                proposalId: proposal.id,
                name: `Xuất kho của đề nghị ${proposal.code}`,
              });
              fetchDepartment();
              setType(InventoryType.Out);
              handleGetStaffGroup(InventoryType.Out);
              setVisible(true);
              setTimeout(() => {
                tableExportInventoryRef.current?.fillTableWithProposal(
                  proposal
                );
              }, 100);
            }
          },

          handleUpdate(record?: Inventory) {
            // Lấy danh sách phòng ban khi cập nhật phiếu
            console.log("Record lúc này là", record);
            fetchDepartment();
            fetchProviders(record?.provider);

            queryPurchase.providerId = record?.provider?.id;
            fetchPurchase();

            setIsDisabledStock(!!record?.inventoryDetails.length);
            setStatus("update");
            setDetails(record);
            setVisible(true);
            setType(record?.type);
            form.setFieldsValue({
              ...record,
              providerId: record?.provider?.id,
              departmentId: record?.department?.id,
              checkedAt: record?.checkedAt
                ? dayjs.unix(record?.checkedAt)
                : undefined,
              inspecStaffIds: record?.inspecStaffs.map((item) => item.id),
              purchaseOrderId: record?.purchaseOrder?.id,
              fromStockId: record?.fromStock?.id,
              toStockId: record?.toStock?.id,
              contractId: record?.contract?.id,
            });
            console.log(
              "Data khi bất modal cập nhật phiếu là",
              record?.inventoryDetails
            );
            setImportInventoryMode(record?.mode);
            setInspecStaffs(record?.inspecStaffs || []);
            setFollowStaffs(record?.followStaffs || []);

            switch (record?.type) {
              case InventoryType.In:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  quantityLogFormRef.current?.handleOpenAll(record);
                }, 100);

                setTimeout(() => {
                  if (
                    tableImportInventoryRef.current &&
                    record?.inventoryDetails?.length
                  ) {
                    tableImportInventoryRef.current.setValue(
                      record.inventoryDetails
                    );
                  }
                }, 100); // delay 1 chút để bảng được render
                break;
              case InventoryType.Out:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableExportInventoryRef.current?.setValue(record);
                }, 100);
                break;
              case InventoryType.OutReturnProductError:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableErrorReturnSlipRef.current?.setValue(record);
                }, 100);
                break;

              case InventoryType.InReceiveProductError:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableReceiveProductError.current?.setValue(record);
                }, 100);
                break;
              case InventoryType.OutSendProduct:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableOutSendProductRef.current?.setValue(record);
                }, 100);
                break;
              case InventoryType.InReceiveProduct:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableInReceiveProductRef.current?.setValue(record);
                }, 100);
                break;
              case InventoryType.Check:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  inventoryCheckFormRef.current?.handleOpenAll(record);
                }, 100);
                break;
              case InventoryType.Change:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableInventoryChangeRef.current?.handleOpenAll(record);
                }, 100);
                break;

              case InventoryType.ChangeProduct:
                setTimeout(() => {
                  //phải settimeout để tránh không component chưa xuất hiện kịp
                  tableChangeProductRef.current?.setValue(record);
                }, 100);
                break;

              default:
                break;
            }
          },
        };
      },
      []
    );

    const getTableDetail = () => {
      switch (type) {
        case InventoryType.Out:
          return tableExportInventoryRef.current?.getValue();
        case InventoryType.In:
          // return quantityLogFormRef.current?.getValue();
          return tableImportInventoryRef.current?.getValue() || [];
        case InventoryType.OutReturnProductError:
          return tableErrorReturnSlipRef.current?.getValue();
        case InventoryType.InReceiveProductError:
          return tableReceiveProductError.current?.getValue();
        case InventoryType.OutSendProduct:
          return tableOutSendProductRef.current?.getValue();
        case InventoryType.InReceiveProduct:
          return tableInReceiveProductRef.current?.getValue();
        case InventoryType.Check:
          return inventoryCheckFormRef.current?.getValue();
        case InventoryType.Change:
          return tableInventoryChangeRef.current?.getValue();
        case InventoryType.ChangeProduct:
          return tableChangeProductRef.current?.getValue();
        default:
          return details;
      }
    };
    const getTableData = () => {
      // importInventoryRef.current?.getValue();
      tableImportInventoryRef.current?.getValue();
      console.log(
        "Get table data là",
        tableImportInventoryRef.current?.getValue()
      );
      return tableImportInventoryRef.current?.getValue() || [];
    };
    const validateTable = (
      tableDetails: InventoryDetail[] = [],
      type: InventoryType
    ) => {
      let isValidate = true;
      console.log("Table details là", tableDetails);
      switch (type) {
        case InventoryType.In:
          isValidate = tableDetails.every((item) => {
            return (
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.quantity == "number" &&
              item.date
            );
          });
          break;
        case InventoryType.Check:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number"
          );
          break;
        case InventoryType.Out:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number" &&
              item.date
          );
          break;
        case InventoryType.OutReturnProductError:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number" &&
              item.date
          );
          break;
        case InventoryType.OutSendProduct:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number" &&
              item.date
          );
          break;
        case InventoryType.InReceiveProduct:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number" &&
              item.date &&
              item.afterProductId
          );
          break;
        case InventoryType.InReceiveProductError:
          isValidate = tableDetails.every(
            (item) =>
              item.productId &&
              item.stockId &&
              item.unitId &&
              typeof item.realQuantity == "number" &&
              item.date
          );
          break;

        case InventoryType.Change:
          //Không chuyển quá số lượng tồn kho
          isValidate = tableDetails.every(
            (item) => item.quantity >= item.realQuantity
          );
          break;

        default:
          break;
      }

      return isValidate;
    };

    const checkInspectStaffInventory = async () => {
      console.log("What is inspecStaffs now", inspecStaffs);
      if (
        inspecStaffs.some((item) => item.isThuKho) &&
        inspecStaffs.some((item) => item.isSupper)
      ) {
        handleCreateInventory();
      } else {
        Modal.confirm({
          title: "Người duyệt chưa đủ điều kiện",
          type: "warning",
          content:
            "Danh sách người duyệt cần có thủ kho và ban lãnh đạo. Vui lòng kiểm tra lại!",
          okText: "Chọn lại danh sách người duyệt",
          cancelText: "Vẫn lưu",
          cancelButtonProps: { icon: <SaveFilled /> },
          onCancel: () => handleCreateInventory(),
        });
      }
    };

    const handleCreateInventory = async (isDraff?: boolean) => {
      // console.log(form.getFieldsValue());

      // return;

      const tableDetails = getTableDetail();
      const tableData = getTableData();
      console.log("Data khi lấy đc là", tableData);
      const followStaffIds = followStaffs?.map((item: Staff) => {
        return item.id;
      });
      console.log({ tableDetails });
      // debugger;
      if (tableDetails.length == 0) {
        message.error("Vui lòng điền đầy đủ ít nhất một dòng trong bảng!");
        return;
      }
      console.log("tableDetails", tableDetails);
      
      if (
        !tableDetails.every(
          (item: InventoryDetail) =>
            item.productId && typeof item.quantity == "number" && item.date
        )
      ) {
        message.error("Vui lòng điền đầy đủ thông tin trong bảng!");
        return;
      }

      const inspecStaffIds = inspecStaffs?.map((item: Staff) => {
        return item.id;
      });

      const {
        name,
        desc,
        description,
        fileAttaches,
        departmentId,
        providerId,
        isReceipt,
        purchaseOrderId,
        isAllInspec,
        checkedAt,
        mode,
        contractId,
      } = form.getFieldsValue();
      const payload = {
        inventory: {
          name,
          desc,
          description,
          type,
          checkedAt: checkedAt?.unix(),
          isAllInspec,
          isReceipt,
          module: Module.Freeze,
          mode: mode,
          deadlineDate:
            status == "create"
              ? dayjs().add(30, "day").format("YYYY-MM-DD")
              : undefined, //Deadline mặc định là 30 ngày.
        },
        purchaseOrderId,
        contractId,

        departmentId,
        providerId,
        inventoryDetails: tableDetails || [],
        //@ts-ignore
        // inventoryDetails: data?.importInventoryDetails,
        inspecStaffIds,
        followStaffIds,
        fileAttachIds: [] as number[],
        fromStockId: fromStockId,
        toStockId: toStockId,
        supperSignStaffId: supperStaff?.id,
      };

      if (isDraff) {
        const localData = {
          ...payload,
          name: payload?.inventory?.name,
          desc: payload?.inventory.desc,
          id: inventoryTempSelected?.id || uuidv4(),
          createdAt: dayjs().unix(),
          followStaffs,
          inspecStaffs,
          department: departments?.find(
            (item) => item.id == payload?.departmentId
          ),
          provider: providers?.find((item) => item.id == payload?.providerId),
          fromStock: fromStockValue,
          toStock: toStockValue,
        };

        InventoryLocal.createOrUpdate(localData);

        message.success("Lưu nháp thành công");
        removeSearchParam();
        setVisible(false);
        onOk(isDraff);
        return;
      }

      const isValidated = validateTable(tableDetails, type as InventoryType);
      if (!isValidated)
        return message.warning(
          type == InventoryType.Change
            ? "Số lượng tồn không đủ để chuyển. Vui lòng kiểm tra lại!"
            : "Vui lòng điền các mục cần thiết trong bảng"
        );
      try {
        await form.validateFields();
        //Upload file fileAttachs lên để lấy ids
        let fileAttachIds: number[] = [];
        let oldFileIds: number[] = [];
        if (fileAttaches?.length) {
          //
          let requestArr: any = [];

          fileAttaches.forEach((item: any) => {
            if (item.id) {
              //Nếu file cũ => dùng lại id cũ
              oldFileIds.push(item.id);
            } else {
              //File mới phải gọi api này để lấy id
              requestArr.push(
                fileAttachApi.create({
                  fileAttach: {
                    name: item.name,
                    url:
                      import.meta.env.VITE_API_URL +
                      item?.response?.data?.path || item?.url,
                    type: item.type.includes("image")
                      ? FileAttachType.Image
                      : FileAttachType.File,
                    mimetype: item.type,
                  },
                })
              );
            }
          });

          const res = await Promise.all(requestArr);
          fileAttachIds = res.map((item) => item?.data?.id).concat(oldFileIds);
          payload.fileAttachIds = fileAttachIds;
        }
        console.log("Table details là", tableDetails);
        switch (status) {
          case "create":
            setLoading(true);
            console.log(
              "Payload nhận được là",
              payload,
              tableImportInventoryRef.current?.getValue()
            );

            await inventoryApi.create({
              ...payload,
            });
            if (isCompletePo) {
              await purchaseApi.complete(purchaseOrderId || 0);
            }
            setLoading(false);
            message.success("Thêm mới thành công");
            onUpdateOk();
            break;

          case "update":
            setLoading(true);
            await inventoryApi.update(details.id, {
              ...payload,
            });
            setLoading(false);
            message.success("Cập nhật thành công");
            onUpdateOk();
            break;
        }
      } finally {
        setLoading(false);
      }
    };

    const onUpdateOk = () => {
      form.resetFields();
      onOk();
      setVisible(false);
      fetchData();
      removeSearchParam();
    };

    const debounceSearchProvider = useCallback(
      debounce((keyword) => {
        queryProvider.search = keyword;
        fetchProviders();
      }, 300),
      []
    );

    const debounceSearchDepartment = useCallback(
      debounce((keyword) => {
        queryDepartment.search = keyword;
        fetchDepartment();
      }, 300),
      []
    );

    const removeSearchParam = () => {
      searchParams.delete("mode");
      searchParams.delete("type");
      searchParams.delete("id");
      searchParams.delete("purchaseorder");
      searchParams.delete("purchaseId");

      setSearchParams([...searchParams], { replace: true });
    };

    const handleCancelModal = () => {
      form.resetFields();
      setVisible(false);
      setStatus("create");
      setInspecStaffs([]);
      setFollowStaffs([]);
      setSupperStaff(undefined);
      setIsDisabledStock(false);
      setImportInventoryMode(undefined);
      if (status == "create") {
        searchParams.delete("mode");
        searchParams.delete("type");
        setSearchParams([...searchParams], { replace: true });

        onCancel();
      }
    };

    const debounceSearchPurchases = useCallback(
      debounce((keyword) => {
        queryPurchase.search = keyword;
        fetchPurchase();
      }, 300),
      []
    );

    const handleFillTable = async (purchaseOrderId: number) => {
      const { data } = await purchaseApi.findOne(purchaseOrderId);
      if (type == InventoryType.In) {
        if (quantityLogFormRef.current) {
          quantityLogFormRef.current.fillTableWithPurchase(data);
        }
      }

      if (type == InventoryType.InReceiveProductError) {
        if (tableReceiveProductError.current) {
          tableReceiveProductError.current.fillTableWithPurchase(data);
        }
      }
    };
    // const data = importInventoryRef.current?.getValue();
    const data = tableImportInventoryRef.current?.getValue();
    console.log("Dữ liệu từ table import inventory là:", data);
    return (
      <div>
        <Modal
          className="custom-close-icon custom-title"
          style={{ top: 15 }}
          width={5000}
          keyboard={false}
          maskClosable={false}
          zIndex={1000}
          title={`${
            // tao phieu nhap xuat kho
            status === "update" ? `Chỉnh sửa ` : `Tạo `
            } ${getTitleModalByType(type as InventoryType)}`}
          closeIcon={<CloseOutlined className="text-lg" />}
          open={visible || searchParams.get("purchaseorder") === "REPAIR"}
          onCancel={handleCancelModal}
          okText="Xác nhận"
          destroyOnClose
          afterClose={removeSearchParam}
          footer={[
            <>
              {isDisableSaveBtn && (
                <span className="mr-10 text-red-500">
                  Cập nhật chữ ký{" "}
                  <a className="underline" target="_blank" href="/profile">
                    tại đây
                  </a>{" "}
                  để được cấp quyền tạo {/* @ts-ignore */}
                  {InventoryTypeTrans[type]}
                </span>
              )}

              {/* {!network.online && (
                <span className="mr-10 text-red-500">
                  Bạn đang offline, hãy chọn lưu nháp để lưu thông tin vào bộ
                  nhớ
                </span>
              )} */}

              <Button
                onClick={handleCancelModal}
                className="text-xs md:text-sm px-2 md:px-[15px]"
              >
                Đóng
              </Button>
              {/* {(status == "create" ||
                (status == "update" &&
                  details?.status == ProposalStatus.Temp)) && (
                <Button
                  onClick={() => handleCreateInventory(true)}
                  type="primary"
                  ghost
                  icon={<ContainerFilled />}
                  className="text-xs md:text-sm px-2 md:px-[15px]"
                >
                  Lưu nháp
                </Button>
              )} */}

              <Button
                disabled={
                  isDisableSaveBtn ||
                  (!contractId &&
                    (importInventoryMode == InventoryMode.OemProcessing ||
                      (type == InventoryType.Out && importInventoryMode === InventoryMode.Contract)))
                }
                loading={loading}
                onClick={() => {
                  if (type == InventoryType.Out) {
                    checkInspectStaffInventory();
                  } else {
                    handleCreateInventory();
                  }
                  // handleCreateInventory();
                }}
                icon={<SaveFilled />}
                type="primary"
                className="text-xs md:text-sm px-2 md:px-[15px]"
              >
                {"Lưu thông tin"}
              </Button>
            </>,
          ]}
        >
          {contextHolder}
          <Form
            className="form-margin-0"
            initialValues={{
              checkedAt: dayjs(),
              ["staffId"]: toJS(userStore?.info)?.id,
            }}
            form={form}
            name="validateOnly"
            layout="vertical"
            autoComplete="off"
          >
            <div className="flex flex-col gap-1">
              {/* Xuất cho bộ phận: */}
              {/* {type === InventoryType.Out ? (
                <div className="flex items-center">
                  <label
                    htmlFor=""
                    className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                  >
                    Xuất cho bộ phận:
                  </label>
                  <Form.Item
                    name="departmentId"
                    className="w-full md:w-1/2 !mb-0"
                    // label="Tên đề nghị"
                    rules={[
                      {
                        required: true,
                        message: "Vui lòng chọn bộ phận được xuất",
                      },
                    ]}
                  >
                    <Select
                      placeholder="Chọn bộ phận được xuất"
                      allowClear
                      showSearch
                      filterOption={false}
                      onSearch={debounceSearchDepartment}
                      style={{ width: "100%" }}
                      options={departments.map((item) => ({
                        label: item.name,
                        value: item.id,
                        provider: item,
                      }))}
                      onChange={(value, option: any) => {
                        if (option?.provider) {
                          form.setFieldsValue({
                            name: `Xuất kho của ${option?.provider?.name}`,
                          });
                        } else {
                          form.resetFields(["name"]);
                        }
                      }}
                    />
                  </Form.Item>
                </div>
              ) : (
                // chọn nhà cung cấp
                type !== InventoryType.Check &&
                type !== InventoryType.Change && (
                  <div className="flex items-center">
                    <label
                      htmlFor=""
                      className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                    >
                      Nhà cung cấp:
                    </label>
                    <Form.Item
                      name="providerId"
                      className="w-full md:w-1/2 !mb-0 overflow-hidden"
                      // label="Tên đề nghị"
                      rules={[
                        {
                          required: true,
                          message: "Vui lòng nhập tên đơn hàng",
                        },
                      ]}
                    >
                      <Select
                        placeholder="Chọn nhà cung cấp"
                        allowClear
                        showSearch
                        filterOption={false}
                        onSearch={debounceSearchProvider}
                        style={{ width: "100%" }}
                        options={providers.map((item) => ({
                          label: item.code + " - " + item.name,
                          value: item.id,
                          provider: item,
                        }))}
                        onChange={(value, option: any) => {
                          form.setFieldsValue({
                            purchaseOrderId: null,
                          });
                          if (value) {
                            queryPurchase.providerId = value;
                            setLoadingFetchPO(true);
                            fetchPurchase().finally(() => {
                              setLoadingFetchPO(false);
                            });
                          }
                          if (type == InventoryType.In) {
                            if (option?.provider) {
                              form.setFieldsValue({
                                name: `Nhập kho của ${option?.provider?.name}`,
                              });
                            } else {
                              form.resetFields(["name"]);
                            }
                          }
                        }}
                      />
                    </Form.Item>
                  </div>
                )
              )} */}

              {/* tên phiếu */}

              {/* <div className="flex items-center">
                <label
                  htmlFor=""
                  className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Tên phiếu
                </label>
                <Form.Item
                  name="name"
                  className="w-full md:w-1/2 !mb-0"
                  // label="Tên đề nghị"
                  rules={[
                    {
                      required: true,
                      message: `Vui lòng nhập tên phiếu`,
                    },
                  ]}
                >
                  <Input placeholder={`Nhập tên phiếu`} />
                </Form.Item>
              </div> */}

              {/* ngày kiểm */}
              {/* {type == InventoryType.Check && (
                <div className="flex items-center">
                  <label
                    htmlFor=""
                    className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                  >
                    Ngày kiểm kho
                  </label>
                  <Form.Item
                    name="checkedAt"
                    className="w-full md:w-1/2 !mb-0"
                    // label="Tên đề nghị"
                    rules={[requiredRule]}
                  >
                    <DatePicker
                      disabledDate={disabledDate}
                      showTime
                      format={settings.fullDateFormat}
                      style={{ width: "100%" }}
                      allowClear={false}
                    />
                  </Form.Item>
                </div>
              )} */}

              {/* chọn PO */}
              {/* {[
                InventoryType.In,
                InventoryType.InReceiveProductError,
                InventoryType.OutReturnProductError,
              ].includes(type as InventoryType) && (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <label
                      htmlFor=""
                      className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                    >
                      Đơn mua hàng{" "}
                      <Tooltip title="Vui lòng chọn nhà cung cấp">
                        <InfoCircleFilled
                          style={{ color: "#333" }}
                          className="cursor-pointer"
                        />
                      </Tooltip>
                    </label>

                    <Form.Item
                      name="purchaseOrderId"
                      className="w-full md:w-1/2 !mb-0"
                      rules={[
                        {
                          required: true,
                          message: `Vui lòng chọn đơn hàng`,
                        },
                      ]}
                    >
                      <Select
                        disabled={!providerId}
                        allowClear={false}
                        onChange={(id) => {
                          console.log("onChange:", id);
                          handleFillTable(id);
                        }}
                        showSearch
                        notFoundContent={loadingFetchPO ? <Spin /> : null}
                        filterOption={false}
                        onSearch={debounceSearchPurchases}
                        style={{ width: "100%" }}
                        options={purchaseOrders.map((item) => ({
                          label: item.code,
                          value: item.id,
                        }))}
                      />
                    </Form.Item>
                  </div>

                  <Form.Item shouldUpdate noStyle>
                    {() => {
                      return (
                        <div>
                          {form.getFieldValue("purchaseOrderId") && (
                            <div>
                              <label
                                htmlFor=""
                                className="min-w-[120px] font-semibold !text-xs md:!text-[13px]"
                              >
                                Đã hoàn thành PO (đã về đủ){" "}
                                <Checkbox
                                  value={isCompletePo}
                                  checked={isCompletePo}
                                  onChange={(e) => {
                                    const value = e.target.checked;
                                    setIsCompletePo(value);
                                  }}
                                />
                              </label>
                            </div>
                          )}
                        </div>
                      );
                    }}
                  </Form.Item>
                </div>
              )} */}

              {/* {type == InventoryType.In && (
                <div>
                  <label
                    htmlFor=""
                    className="min-w-[120px] font-semibold !text-xs md:!text-[13px]"
                  >
                    Đã có hóa đơn{" "}
                    <Form.Item
                      noStyle
                      valuePropName="checked"
                      name={"isReceipt"}
                    >
                      <Checkbox />
                    </Form.Item>
                  </label>
                </div>
              )} */}

              <div className="flex items-center gap-2">
                <label
                  htmlFor=""
                  className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Tên phiếu
                </label>
                <Form.Item
                  name="name"
                  className="w-full md:w-1/2 !mb-0"
                  rules={[
                    {
                      required: true,
                      message: `Vui lòng nhập tên phiếu`,
                    },
                  ]}
                >
                  <Input placeholder={`Nhập tên phiếu`} />
                </Form.Item>
              </div>

              <div
                className={`flex items-center gap-2 ${(type == InventoryType.In || type === InventoryType.Out) ? "block" : "hidden"
                  }`}
              >
                <label
                  htmlFor=""
                  className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Chọn loại nhập kho
                </label>
                <Form.Item
                  name="mode"
                  className="w-full md:w-1/2 !mb-0"
                // rules={[
                //   {
                //     required: true,
                //     message: `Vui lòng chọn loại nhập kho`,
                //   },
                // ]}
                >
                  <Select
                    disabled={status == "update"}
                    placeholder={`Chọn loại nhập kho`}
                    onChange={(value) => {
                      form.setFieldValue("mode", value);
                      setImportInventoryMode(value);
                      form.setFieldValue("contractId", undefined);
                      tableImportInventoryRef.current?.refetchTableData?.();
                      form.setFieldValue("contractId", undefined);
                    }}
                  >
                    {Object.values(type == InventoryType.In ? ImportInventoryTypeTrans : ExportInventoryTypeTrans).map((item) => (
                      <Select.Option
                        key={item.value}
                        value={item.value}
                        className="text-xs md:text-sm"
                      >
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
              <div

                className={`flex items-center gap-2 ${importInventoryMode == InventoryMode.OemProcessing ||
                  //@ts-ignore
                  (type == InventoryType.Out && importInventoryMode == InventoryMode.OemProcessing) || type === InventoryType.ChangeProduct
                  ? "block"
                  : "hidden"
                  }`}
              >
                <label
                  htmlFor=""
                  className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Hợp đồng
                </label>
                <Form.Item name="contractId" className="w-full md:w-1/2 !mb-0">
                  <ContractSelector disabled={status == "update"} />
                </Form.Item>
              </div>
              {/* <div
                className={`flex items-center ${
                  type == InventoryType.Out ? "block" : "hidden"
                }`}
              >
                <label
                  htmlFor=""
                  className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                >
                  Hợp đồng
                </label>
                <Form.Item
                  name="contractId"
                  className="w-full md:w-1/2 !mb-0"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: `Vui lòng nhập diễn giải`,
                  //   },
                  // ]}
                >
                  <ContractSelector disabled={status == "update"} />
                </Form.Item>
              </div> */}
              {type != InventoryType.Change && (
                <div className="flex items-center gap-2">
                  <label
                    htmlFor=""
                    className="min-w-[120px] font-semibold !text-xs md:!text-[13px]"
                  >
                    Diễn giải
                  </label>
                  <Form.Item
                    name="description"
                    className="w-full md:w-1/2 !mb-0"
                  // rules={[
                  //   {
                  //     required: true,
                  //     message: `Vui lòng nhập diễn giải`,
                  //   },
                  // ]}
                  >
                    <Input placeholder={`Nhập diễn giải`} />
                  </Form.Item>
                </div>
              )}

              {/* chọn kho chuyển */}
              {type == InventoryType.Change && (
                <Flex align="start" gap={10}>
                  <div className="flex items-center">
                    <label
                      htmlFor=""
                      className="min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                    >
                      Chuyển từ{" "}
                      {isDisabledStock && (
                        <Tooltip title="Để thay đổi kho. Vui lòng xóa hết dữ liệu chuyển kho bên dưới">
                          <InfoCircleTwoTone />
                        </Tooltip>
                      )}
                    </label>
                    <Form.Item
                      name="fromStockId"
                      rules={[
                        {
                          required: true,
                          message: `Vui lòng chọn kho`,
                        },
                      ]}
                    >
                      <StockSelector
                        disabled={isDisabledStock}
                        allowClear={false}
                        width={"200px"}
                        placeholder="Từ kho"
                        onChange={(id) => {
                          if (id == toStockId) {
                            form.resetFields(["toStockId"]);
                          }
                        }}
                        onChangeStock={(stock) =>
                          (fromStockValue.current = stock)
                        }
                        initQuery={{ module: Module.Freeze }}
                      />
                    </Form.Item>
                  </div>
                  <ArrowRightOutlined className="translate-y-2" />
                  <div className="flex items-center">
                    <Form.Item
                      name="toStockId"
                      rules={[
                        {
                          required: true,
                          message: `Vui lòng chọn kho`,
                        },
                      ]}
                    >
                      <StockSelector
                        allowClear={false}
                        width={"200px"}
                        placeholder="Đến kho"
                        disabled={!fromStockId || isDisabledStock}
                        disabledItem={fromStockId}
                        onChangeStock={(stock) =>
                          (toStockValue.current = stock)
                        }
                        initQuery={{ module: Module.Freeze }}
                      />
                    </Form.Item>
                  </div>
                </Flex>
              )}
              {type == InventoryType.Out && (
                <Form.Item
                  name="inspecStaffIds"
                  // rules={[{ required: true }]}
                  style={{ width: "100%", marginBottom: 0 }}
                >
                  <div className="flex gap-2 items-center">
                    <label className="font-semibold !text-xs md:!text-[13px] label-required">
                      Lãnh đạo ký tên:{" "}
                    </label>
                    <div className="gap-1 text-ellipsis w-[250px] overflow-hidden whitespace-nowrap text-blue-500">
                      {supperStaff && (
                        <>
                          {supperStaff?.name} ({supperStaff?.code})
                        </>
                      )}
                    </div>

                    <Popover
                      className="z-[1001]"
                      overlayStyle={{
                        width: breakpoint === "mobile" ? 300 : 600,
                      }}
                      placement={breakpoint === "mobile" ? "topLeft" : "right"}
                      content={
                        <>
                          <TableListStaffGroup
                            isSupper
                            multiple={false}
                            onChange={(data) => {
                              setSupperStaff(data?.[0]);
                            }}
                            type={StaffGroupAction.Inspec}
                            dataSource={supperStaff ? [supperStaff] : undefined}
                            highlightSupper={true}
                          />
                        </>
                      }
                      trigger="click"
                    >
                      <Button
                        size="small"
                        className="font-medium ml-3 !text-xs"
                      >
                        Xem thêm
                      </Button>
                    </Popover>
                  </div>
                </Form.Item>
              )}
              {/* người duyệt */}
              <Form.Item
                name="inspecStaffIds"
                // rules={[{ required: true }]}
                style={{ width: "100%", marginBottom: 0 }}
              >
                <div className="flex gap-2 items-center">
                  <label className="font-semibold !text-xs md:!text-[13px] label-required">
                    Thông tin người duyệt ({inspecStaffs?.length} thành viên):{" "}
                  </label>
                  <div className="gap-1 text-ellipsis w-[250px] overflow-hidden whitespace-nowrap text-blue-500">
                    {inspecStaffs &&
                      inspecStaffs?.length > 0 &&
                      inspecStaffs
                        ?.map((item) => {
                          return `${item?.name} (${item?.code})`;
                        })
                        .join(", ")}
                  </div>
                  <Popover
                    className="z-[1001]"
                    overlayStyle={{
                      width: breakpoint === "mobile" ? 300 : 600,
                    }}
                    placement={breakpoint === "mobile" ? "topLeft" : "right"}
                    content={
                      <>
                        <TableListStaffGroup
                          onChange={(data) => {
                            setInspecStaffs(data);
                          }}
                          type={StaffGroupAction.Inspec}
                          dataSource={inspecStaffs}
                          highlightSupper={type == InventoryType.Out}
                        />
                      </>
                    }
                    trigger="click"
                  >
                    <Button size="small" className="font-medium ml-3 !text-xs">
                      Xem thêm
                    </Button>
                  </Popover>
                </div>
              </Form.Item>
              {/* người theo dõi */}
              <Form.Item
                name="followStaffIds"
                style={{ width: "100%", marginBottom: 5 }}
              // rules={[{ required: true }]}
              >
                <div className="flex flex-col md:flex-row md:gap-2 md:items-center">
                  <label className="font-semibold !text-xs md:!text-[13px] label-required">
                    Thông tin người theo dõi ({followStaffs?.length} thành
                    viên):
                  </label>
                  <div className="flex items-center">
                    <div className="md:text-sm text-xs gap-1 text-ellipsis w-[250px] overflow-hidden whitespace-nowrap text-blue-500">
                      {followStaffs &&
                        followStaffs.length > 0 &&
                        followStaffs
                          ?.map((item) => {
                            return `${item?.name} (${item?.code})`;
                          })
                          .join(", ")}
                    </div>

                    <Popover
                      className=""
                      overlayStyle={{
                        width: breakpoint === "mobile" ? 300 : 600,
                      }}
                      placement={breakpoint === "mobile" ? "topLeft" : "right"}
                      content={
                        <>
                          <TableListStaffGroup
                            onChange={(data) => {
                              setFollowStaffs(data);
                            }}
                            type={StaffGroupAction.Follow}
                            dataSource={followStaffs}
                          />
                        </>
                      }
                      trigger="click"
                    >
                      <Button
                        size="small"
                        className="font-medium ml-3 !text-xs"
                      >
                        Xem thêm
                      </Button>
                    </Popover>
                  </div>
                </div>
                <div className="flex items-center">
                  <label
                    htmlFor=""
                    className="min-w-[100px] md:min-w-[120px] font-semibold !text-xs md:!text-[13px] label-required"
                  >
                    Đóng hợp đồng:
                  </label>
                  <Form.Item
                    name="isAllInspec"
                    className="w-full md:w-1/2 !mb-0 !overflow-hidden"
                  >
                    <Radio.Group>
                      <Radio value={true}>Tất cả người duyệt đều duyệt</Radio>
                      <Radio value={false}>1 người duyệt</Radio>
                    </Radio.Group>
                  </Form.Item>
                </div>
              </Form.Item>

              <Form.Item hidden name="isAllInspec">
                <Input />
              </Form.Item>
              {/* <ImportInventory ref={importInventoryRef} /> */}

              <div>
                {/* {type === InventoryType.OutReturnProductError && (
                  <TableErrorReturnSlip ref={tableErrorReturnSlipRef} />
                )}
                {type === InventoryType.InReceiveProductError && (
                  <TableReceiveProductError ref={tableReceiveProductError} />
                )}
                {type === InventoryType.OutSendProduct && (
                  <TableOutSendProduct ref={tableOutSendProductRef} />
                )}
                {type === InventoryType.InReceiveProduct && (
                  <TableInReceiveProduct ref={tableInReceiveProductRef} />
                )}
                {type === InventoryType.In && (
                  <InventoryImportForm
                    ref={quantityLogFormRef}
                    onSubmitOk={() => ""}
                  />
                )}
                {type === InventoryType.Check && (
                  <InventoryCheckForm
                    ref={inventoryCheckFormRef}
                    onSubmitOk={() => ""}
                  />
                )} */}
                {type === InventoryType.Change && fromStockId && toStockId && (
                  <TableInventoryChange
                    ref={tableInventoryChangeRef}
                    fromStockId={fromStockId}
                    onDisabledStock={(isDisabled) =>
                      setIsDisabledStock(isDisabled)
                    }
                  />
                )}
              </div>
              {/* {type === InventoryType.In && (
                <InventoryImportForm
                  ref={quantityLogFormRef}
                  onSubmitOk={() => ""}
                />
              )} */}

              <div>

                {type == InventoryType.In && (
                  <TableImportInventory
                    status={status}
                    // ref={importInventoryRef}
                    ref={tableImportInventoryRef}
                    hiddenPrivateInfo={false}
                    contractId={contractId}
                    type={type as InventoryType}
                    typeContract={importInventoryMode === InventoryMode.OemProcessing}
                  />
                )}
              </div>

              <div>
                {type === InventoryType.Out && (
                  <TableExportInventory
                    ref={tableExportInventoryRef}
                    contractId={contractId}
                    status={status}
                  />
                )}
              </div>


              {type == InventoryType.ChangeProduct && (
                <TableChangeProduct
                  status={status}
                  // ref={importInventoryRef}
                  ref={tableChangeProductRef}
                  hiddenPrivateInfo={false}
                  contractId={contractId}
                  type={type as InventoryType}
                />
              )}

            </div>
          </Form>
        </Modal>
      </div>
    );
  }
);

export default CreateInventoryModal;
