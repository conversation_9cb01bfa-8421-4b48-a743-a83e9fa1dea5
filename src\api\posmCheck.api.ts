import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const posmCheckApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posmCheck",
      params,
    }),
  detail: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posmCheck/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posmCheck",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posmCheck/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posmCheck/${id}`,
      method: "delete",
    }),
};
