import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const produceApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/produce",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/produce",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/produce/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/produce/${id}`,
      method: "delete",
    }),
};
