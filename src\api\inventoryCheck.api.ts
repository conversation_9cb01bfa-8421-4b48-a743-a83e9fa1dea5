import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const inventoryCheckApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/inventoryCheck",
      params,
    }),
  detail: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventoryCheck/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/inventoryCheck",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventoryCheck/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/inventoryCheck/${id}`,
      method: "delete",
    }),
};
