import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const productCategoryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productCategory",
      params,
    }),
  findOne: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productCategory/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productCategory",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productCategory/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productCategory/${id}`,
      method: "delete",
    }),
};
