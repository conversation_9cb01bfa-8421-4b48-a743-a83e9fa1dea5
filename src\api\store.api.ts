import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const storeApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/store",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/store",
      data,
      method: "post",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/store/import",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/store/${id}`,
      method: "patch",
      data,
    }),
  approve: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/store/${id}/approve`,
      method: "patch",
    }),
  reject: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/store/${id}/reject`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/store/${id}`,
      method: "delete",
    }),
};
