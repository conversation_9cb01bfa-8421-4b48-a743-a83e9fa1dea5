import { SearchOutlined } from "@ant-design/icons";
import { Button, Input, Space, Spin, Table } from "antd";
import { useEffect, useRef } from "react";
import { ContractContentModal } from "./ContractContentModal";
import { useContractContent } from "@/hooks/useContractContent";
import {
  ContractConfigType,
  ContractConfigTypeTrans,
  ContractContent,
} from "@/types/contractContent";
import { ContractType } from "@/types/contract";
import { observer } from "mobx-react";
import { userStore } from "@/store/userStore";
import { PermissionName } from "@/router";
import { Pagination } from "@/components/Pagination";

const { Column } = Table;

interface ContractTypeTabProps {
  contractType: ContractType;
}

export const ContractTypeTab = observer(
  ({ contractType }: ContractTypeTabProps) => {
    const modalRef = useRef<ContractContentModal>(null);

    const { fetchData, contractContents, loading, setQuery, total, query } =
      useContractContent({
        initQuery: {
          limit: 100,
          page: 1,
          contractType, // Truyền contractType vào query
        },
      });

    useEffect(() => {
      fetchData();
    }, [contractType]);

    return (
      <>
        <div className="filter-container">
          <Space>
            <div className="filter-item">
              <label>Tìm kiếm</label>
              <Input
                onKeyDown={(ev) => {
                  if (ev.code === "Enter") {
                    query.page = 1;
                    setQuery({ ...query });
                  }
                }}
                size="middle"
                onChange={(ev) => {
                  query.search = ev.currentTarget.value;
                }}
                placeholder="Tìm kiếm"
              />
            </div>

            <div className="filter-item btn">
              <Button
                onClick={fetchData}
                type="primary"
                icon={<SearchOutlined />}
              >
                Tìm kiếm
              </Button>
            </div>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Table pagination={false} rowKey="id" dataSource={contractContents}>
            <Column
              title="STT"
              dataIndex="id"
              key="id"
              width={50}
              align="center"
              render={(_, __, index) =>
                (query.page - 1) * query.limit + index + 1
              }
            />
            <Column
              title="Loại"
              dataIndex="type"
              key="type"
              render={(_, record: ContractContent) =>
                ContractConfigTypeTrans[record.type as ContractConfigType]
                  ?.label
              }
              width={200}
            />
            <Column
              title="Tiêu đề"
              dataIndex="titleEn"
              key="titleEn"
              width={220}
            />
            {/* <Column title="Tiêu đề tiếng anh" dataIndex="titleEn" key="titleEn" width={120} /> */}
            <Column
              title="Nội dung"
              dataIndex="contentEn"
              key="contentEn"
              render={(text) => (
                <div dangerouslySetInnerHTML={{ __html: text }} />
              )}
            />
            {/* <Column
            title="Nội dung tiếng anh"
            dataIndex="contentEn"
            key="contentEn"
            render={(text) => <div dangerouslySetInnerHTML={{ __html: text }} />}
          /> */}
            {userStore.checkRoleByName(PermissionName.updateContractConfig) && (
              <Column
                title="Thao tác"
                key="action"
                render={(_, record: ContractContent) => (
                  <Button
                    type="primary"
                    onClick={() => modalRef.current?.handleUpdate(record)}
                  >
                    Cập nhật
                  </Button>
                )}
              />
            )}
          </Table>

          <Pagination
            currentPage={query.page}
            total={total}
            onChange={({ limit, page }) => {
              query.page = page;
              query.limit = limit;
              setQuery({ ...query });
            }}
          />
        </Spin>

        <ContractContentModal
          onSubmitOk={fetchData}
          onClose={() => {}}
          ref={modalRef}
          contractType={contractType}
        />
      </>
    );
  }
);
