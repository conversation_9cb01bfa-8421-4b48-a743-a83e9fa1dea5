import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const companyApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/company",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/company",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/company/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/company/${id}`,
      method: "delete",
    }),
};
