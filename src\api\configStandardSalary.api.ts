import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const payrollCongChuanApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payrollCongChuan",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payrollCongChuan",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollCongChuan/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollCongChuan/${id}`,
      method: "delete",
    }),
};
