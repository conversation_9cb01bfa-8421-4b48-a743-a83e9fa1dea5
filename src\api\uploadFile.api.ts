import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const fileAttachApi = {
  upload: (data?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach/upload",
      data,
      method: "post",
    }),
  batch: (data?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach/batch",
      data,
      method: "post",
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/fileAttach",
      data,
      method: "post",
    }),
};
