import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const productSampleApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productSample",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/productSample",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productSample/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/productSample/${id}`,
      method: "delete",
    }),
};
