import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const jobCategoryApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/jobCategory",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/jobCategory",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/jobCategory/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/jobCategory/${id}`,
      method: "delete",
    }),
  deleteBatch: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/jobCategory/batch`,
      method: "delete",
      data,
    }),
};
