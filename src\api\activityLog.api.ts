import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const activityLogApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/activityLog",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/activityLog",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/activityLog/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/activityLog/${id}`,
      method: "delete",
    }),
};
