import { UnitModal, UnitModalRef } from "@/views/Unit/components/UnitModal";
import { PlusOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useRef } from "react";

type Props = {
  onSuccess: () => void;
};

const CreateUnitBtn = ({ onSuccess }: Props) => {
  const modal = useRef<UnitModalRef>();
  const handleOnOpenCreateModal = () => {
    modal.current?.handleOpen("create");
  };

  return (
    <>
      <Button onClick={handleOnOpenCreateModal} type="primary" size="small">
        <PlusOutlined></PlusOutlined>
      </Button>
      <UnitModal ref={modal} onSubmitOk={onSuccess}></UnitModal>
    </>
  );
};

export default CreateUnitBtn;
