import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const positionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/position",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/position",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/position/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/position/${id}`,
      method: "delete",
    }),
};
