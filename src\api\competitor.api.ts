import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const competitorApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/competitor",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/competitor",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/competitor/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/competitor/${id}`,
      method: "delete",
    }),
};

export const competitorUploadApi =
  import.meta.env.VITE_API_URL + "/v1/admin/image/upload";
