import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const proposalConfigApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/proposalConfig",
      params,
    }),
  findByType: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/proposalConfig/type",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/proposalConfig",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposalConfig/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/proposalConfig/${id}`,
      method: "delete",
    }),
};
