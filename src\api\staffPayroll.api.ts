import { Employee } from "@/types/employee";
import { StaffPayroll } from "@/types/staffPayroll";
import { AnnualLeaveOfStaffData } from "@/utils/export/PayrollReview/annualLeaveSheet";
import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export interface ResponseData {
  total: number;
}

export interface StaffPayrollResponseData extends ResponseData {
  payrolls: StaffPayroll[];
  employees: Employee[];
}

export const staffPayrollApi = {
  findAll: (params?: any): AxiosPromise<StaffPayrollResponseData> =>
    request({
      url: "/v1/admin/payroll",
      params,
    }),
  nangSuat: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/summary/nangSuat",
      params,
    }),
  thongKe: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/summary/tsIQF",
      params,
    }),
  phepNam: (params?: any) =>
    request({
      url: "v1/admin/payroll/summary/phepNam",
      params,
    }),
  employeePrevData: (params?: any): AxiosPromise<StaffPayrollResponseData> =>
    request({
      url: "/v1/admin/payroll/employees/prev",
      params,
    }),
  employees: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/employees",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll",
      data,
      method: "post",
    }),
  createBatch: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/batch",
      data,
      method: "post",
    }),
  deleteBatch: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/batch",
      data,
      method: "delete",
    }),
  annualDays: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/ngayNghiPhepNam",
      data,
      method: "post",
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payroll/${id}`,
      method: "delete",
    }),
  commit: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payroll/commit`,
      method: "post",
      data,
    }),
  reload: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/payroll/re-calc/v2",
      data,
      method: "post",
    }),
  commitVP: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payroll/vp/commit/`,
      method: "post",
      data,
    }),
  summaryTS: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payroll/summary/ts/`,
      method: "post",
      data,
    }),
  getCommitData: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollCommit/month`,
      params,
    }),
  importPayrollFile: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollImport`,
      data,
      method: "POST",
    }),
  uploadPayrollFile: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/payrollImport/upload`,
      data,
      method: "POST",
    }),
};
