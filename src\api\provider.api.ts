import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const providerApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/provider",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/provider",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/provider/${id}`,
      method: "patch",
      data,
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/provider/${id}/block`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/provider/${id}`,
      method: "delete",
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/provider/${id}/password/reset`,
      method: "patch",
      data,
    }),
};
