import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const regionApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/region",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/region",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/region/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/region/${id}`,
      method: "delete",
    }),
};
