import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const taxConfigApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/taxConfig",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/taxConfig",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/taxConfig/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/taxConfig/${id}`,
      method: "delete",
    }),
};
