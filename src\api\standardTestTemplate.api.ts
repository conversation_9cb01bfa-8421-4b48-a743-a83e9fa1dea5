import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const standardTestTemplateApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/standardTestTemplate",
      params,
    }),
  findOne: (id: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/standardTestTemplate/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/standardTestTemplate",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/standardTestTemplate/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/standardTestTemplate/${id}`,
      method: "delete",
    }),
};
