import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const posmStoreApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posmStore",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/posmStore",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posmStore/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/posmStore/${id}`,
      method: "delete",
    }),
};
