import { contractApi } from "@/api/contract.api";
import { contractContentApi } from "@/api/contractContent.api";
import { SingleImageUpload } from "@/components/Uploads/SingleImageUpload";
import { Contract, ContractType } from "@/types/contract";
import { ModalStatus } from "@/types/modal";
import { Col, Form, Input, message, Modal, Row, Tabs, TabsProps } from "antd";
import { Rule } from "antd/lib/form";
import React, { useEffect, useImperativeHandle, useState } from "react";
import { ContentViTab } from "./Tabs/ContentViTab";
import { ContentEnTab } from "./Tabs/ContentEnTab";
import { ContractContent } from "@/types/contractContent";

const rules: Rule[] = [{ required: true }];

export interface ContractContentModal {
  handleCreate: (contractType?: ContractType) => void;
  handleUpdate: (contractContent: ContractContent) => void;
}
interface ContractModalProps {
  onClose: () => void;
  onSubmitOk: () => void;
  contractType?: ContractType;
}

export const ContractContentModal = React.forwardRef(
  ({ onClose, onSubmitOk, contractType }: ContractModalProps, ref) => {
    const [form] = Form.useForm<ContractContent>();
    const [loading, setLoading] = useState(false);
    const [visible, setVisible] = useState(false);
    const [status, setStatus] = useState<ModalStatus>("create");

    useImperativeHandle<any, ContractContentModal>(
      ref,
      () => ({
        handleCreate(contractTypeParam?: ContractType) {
          form.resetFields();
          // Set contractType nếu được truyền vào hoặc sử dụng prop contractType
          if (contractTypeParam || contractType) {
            form.setFieldsValue({
              contractType: contractTypeParam || contractType,
            });
          }
          setVisible(true);
          setStatus("create");
        },
        handleUpdate(contractContent) {
          form.setFieldsValue({ ...contractContent });
          setVisible(true);
          setStatus("update");
        },
      }),
      [contractType]
    );

    const createData = async () => {
      const valid = await form.validateFields();
      const formData = form.getFieldsValue();
      const data = {
        contractConfig: {
          ...formData,
          contractType: formData.contractType || contractType,
        },
      };

      setLoading(true);
      try {
        const res = await contractApi.create(data);
        message.success("Create Contract successfully!");
        onClose();
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };

    const updateData = async () => {
      const { id, ...restData } = await form.validateFields();
      setLoading(true);
      try {
        console.log("nội dung nhận được là", restData);
        const res = await contractContentApi.update(id || 0, {
          contractConfig: restData,
        });
        message.success("Cập nhật nội dung thành công!");
        onClose();
        setVisible(false);
        onSubmitOk();
      } finally {
        setLoading(false);
      }
    };
    const onChange = (key: string) => {
      console.log(key);
    };

    const items: TabsProps["items"] = [
      // {
      //   key: "1",
      //   label: "Tiếng việt",
      //   children: <ContentViTab form={form} contractType={contractType} />,
      // },
      {
        key: "2",
        label: "Tiếng anh",
        children: <ContentEnTab form={form} contractType={contractType} />,
      },
    ];
    return (
      <Modal
        onCancel={() => {
          onClose?.();
          setVisible(false);
        }}
        visible={visible}
        title={status == "create" ? "Thêm nội dung" : "Cập nhật nội dung"}
        style={{ top: 20 }}
        width={1000}
        confirmLoading={loading}
        onOk={() => {
          status == "create" ? createData() : updateData();
        }}
      >
        <ContentEnTab form={form} contractType={contractType} />
        {/* <Tabs
          defaultActiveKey="1"
          items={items}
          onChange={onChange}
          // destroyInactiveTabPane
        /> */}
        {/* <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Tiêu đề" name="titleVi" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            {status == "create" && (
              <Col span={12}>
                <Form.Item label="Password" name="password" rules={rules}>
                  <Input placeholder="" />
                </Form.Item>
              </Col>
            )}

            <Col span={12}>
              <Form.Item label="Name" name="name" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Phone" name="phone" rules={rules}>
                <Input placeholder="" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Email" name="email">
                <Input placeholder="" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item shouldUpdate={true}>
            {() => {
              return (
                <Form.Item label="Avatar" name="avatar">
                  <SingleImageUpload
                    onUploadOk={(path: string) => {
                      console.log("onUploadOk:", path);
                      //   form.setFieldsValue({
                      //     avatar: path,
                      //   });
                    }}
                    imageUrl={form.getFieldValue("avatar")}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        </Form> */}
      </Modal>
    );
  }
);
