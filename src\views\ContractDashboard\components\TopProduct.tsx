import { Card, DatePicker, message, Select, Space, Spin, Table } from "antd";

import moment from "moment";
import { useEffect, useRef, useState } from "react";
import "../Dashboard.scss";
import { Customer } from "@/types/customer";
import { QueryParam } from "@/types/query";
import { dashboardApi } from "@/api/dashboard.api";
import { formatVND } from "@/utils";
import { Pagination } from "@/components/Pagination";
import { settings } from "../../../../settings";
import dayjs from "dayjs";

const { Column } = Table;

interface TopProductData {
  contractProductName: string;
  totalPriceComplete: number;
}

export const TopProduct = () => {
  const [data, setData] = useState<TopProductData[]>([]);
  // const [currentOrderType, setCurrentOrderType] = useState(EOrderType.food);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  const [query, setQuery] = useState<QueryParam>({
    page: 1,
    limit: 10,
    // fromAt: moment().startOf("month").unix(),
    // toAt: moment().endOf("month").unix(),
  });

  useEffect(() => {
    setQuery({ ...query });
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const res = await dashboardApi.findTopSaleProduct(query);
      setData(res.data);
      // setTotal(res.data.total);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [query]);

  const onChange = (value: any) => {
    // setCurrentOrderType(value);
    setQuery({ ...query, orderType: value });
  };

  return (
    <Card size="small">
      <div className="chart-container">
        <div className="">
          <Space>
            <div className="chart-filter">
              <Space style={{ width: "100%", justifyContent: "flex-end" }}>
                <div className="filter-item">
                  <label htmlFor="">Thời gian</label>
                  <br />
                  <DatePicker.RangePicker
                    allowClear={false}
                    onChange={(dates) => {
                      if (dates?.length) {
                        let fromAt = dates[0]?.startOf("day");
                        let toAt = dates[1]?.endOf("day");
                        const diffDays = toAt?.diff(fromAt, "days");

                        // Update query with adjusted or selected date range
                        setQuery({
                          ...query,
                          page: 1,
                          fromAt: fromAt?.unix(),
                          toAt: toAt?.unix(),
                        });
                      } else {
                        // Clear the dates if no range is selected
                        setQuery({
                          ...query,
                          page: 1,
                          fromAt: undefined,
                          toAt: undefined,
                        });
                      }
                    }}
                    value={
                      query.fromAt && query.toAt
                        ? [dayjs.unix(query.fromAt), dayjs.unix(query.toAt)]
                        : undefined
                    }
                    style={{ width: 250 }}
                    ranges={{
                      "Hôm nay": [dayjs().startOf("day"), dayjs().endOf("day")],
                      "Tuần này": [
                        dayjs().startOf("week"),
                        dayjs().endOf("week"),
                      ],
                      "Tháng này": [
                        dayjs().startOf("month"),
                        dayjs().endOf("month"),
                      ],
                      // "3 tháng gần nhất": [
                      //   moment().subtract(3, "month").startOf("month"),
                      //   moment().endOf("month"),
                      // ],
                      // "Năm nay": [
                      //   moment().startOf("year"),
                      //   moment().endOf("year"),
                      // ],
                    }}
                    format={settings.dateFormat}
                  />
                </div>
              </Space>
            </div>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Table
            scroll={{
              y: 360,
            }}
            // style={{

            //   minHeight: 442,
            // }}
            size="small"
            locale={{
              emptyText: "Không có dữ liệu",
            }}
            dataSource={data}
            pagination={false}
            sticky
          >
            <Column
              align="center"
              width={40}
              title=""
              dataIndex="stt"
              key="stt"
              render={(text, record, index) => <>{index + 1}</>}
            />
            <Column
              title="Sản phẩm"
              dataIndex="contractProductName"
              key="contractProductName"
            />

            {/* <Column
              title="Điện thoại"
              dataIndex="customerPhone"
              key="customerPhone"
            /> */}

            <Column
              title="Doanh số USD"
              key={"totalPriceComplete"}
              dataIndex="totalPriceComplete"
              align="right"
              render={(text, record) => <span>{formatVND(text, 2) + ""}</span>}
            />
            {/* <Column
              title="Đơn hàng"
              key={"totalOrders"}
              dataIndex="totalOrders"
              align="right"
              render={(text, record) => <span>{formatVND(text)}</span>}
            /> */}
          </Table>
          <Pagination
            autoScrollToTop={false}
            total={data.length}
            onChange={({ page, limit }) => setQuery({ ...query, page, limit })}
            defaultPageSize={query.limit}
            currentPage={query.page}
          />
        </Spin>

        <div className="text-center">
          <label style={{ fontSize: 18 }}>
            Top 10 sản phẩm có doanh thu cao nhất
          </label>
        </div>
      </div>
    </Card>
  );
};
