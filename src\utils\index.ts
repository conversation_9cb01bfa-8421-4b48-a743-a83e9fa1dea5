import { RcFile } from "antd/es/upload";
import { ToWords } from "to-words";
//@ts-ignore
import { default as VNnum2words } from "vn-num2words";

export const getTitle = (title: string) => {
  return title + " - " + import.meta.env.VITE_WEBSITE_NAME;
};

/**
 * Format number to VND.
 * Example: formatVND(10000);           // '10.000'
 */
export function formatVND(num: number = 0, maximumFractionDigits: number = 9) {
  const config = {
    currency: "VND",
    unit: undefined,
    maximumFractionDigits: maximumFractionDigits,
  };
  const formated = new Intl.NumberFormat("vi-VN", config).format(num);
  return formated;
}

export function formatVND2FDs(num: number = 0) {
  const config = {
    currency: "VND",
    unit: undefined,
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
  };
  const formated = new Intl.NumberFormat("vi-VN", config).format(num);
  return formated;
}

export function formatUSD(num: number = 0) {
  const config = {
    currency: "USD",
    unit: undefined,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  };
  const formatted = new Intl.NumberFormat("en-US", config).format(num);
  return formatted;
}

export function formatNumber(num: number) {
  if (num == 0) return 0;
  if (num) return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
}

export const getImageSize = (
  setImageDimensions: ({
    width,
    height,
  }: {
    width: number;
    height: number;
  }) => void,
  imageUrl: string
) => {
  const img = new Image();
  img.src = imageUrl;

  img.onload = () => {
    setImageDimensions({ width: img.width, height: img.height });
    return { width: img.width, height: img.height };
  };
  img.onerror = (err) => {
    console.error(err);
  };
  return { width: img.width, height: img.height };
};

export function changeToSlug(title: string, separator: string = "-") {
  let slug;

  //Đổi chữ hoa thành chữ thường
  slug = title.toLowerCase().normalize("NFC");

  //Đổi ký tự có dấu thành không dấu
  slug = slug.replace(/á|à|ả|ạ|ã|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ/gi, "a");
  slug = slug.replace(/é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ/gi, "e");
  slug = slug.replace(/i|í|ì|ỉ|ĩ|ị/gi, "i");
  slug = slug.replace(/ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ/gi, "o");
  slug = slug.replace(/ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự/gi, "u");
  slug = slug.replace(/ý|ỳ|ỷ|ỹ|ỵ/gi, "y");
  slug = slug.replace(/đ/gi, "d");
  //Xóa các ký tự đặt biệt
  slug = slug.replace(
    /\`|\~|\!|\@|\#|\||\$|\%|\^|\&|\*|\(|\)|\+|\=|\,|\.|\/|\?|\>|\<|\'|\"|\:|\;|_/gi,
    ""
  );
  //Đổi khoảng trắng thành ký tự gạch ngang
  slug = slug.replace(/ /gi, separator);
  //Đổi nhiều ký tự gạch ngang liên tiếp thành 1 ký tự gạch ngang
  //Phòng trường hợp người nhập vào quá nhiều ký tự trắng
  slug = slug.replace(/\-\-\-\-\-/gi, separator);
  slug = slug.replace(/\-\-\-\-/gi, separator);
  slug = slug.replace(/\-\-\-/gi, separator);
  slug = slug.replace(/\-\-/gi, separator);
  //Xóa các ký tự gạch ngang ở đầu và cuối
  slug = "@" + slug + "@";
  slug = slug.replace(/\@\-|\-\@|\@/gi, "");

  return slug;
}

export const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

export function roundNumber(number: number = 0) {
  if (typeof number != "number") return 0;
  return Math.round((number + Number.EPSILON) * 100) / 100;
}

export const roundUp = (number: number) => {
  return Math.ceil(number / 1000) * 1000;
};

export const titleCase = (str: string) => {
  var splitStr = str.toLowerCase().split(" ");
  for (var i = 0; i < splitStr.length; i++) {
    splitStr[i] =
      splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
  }
  return splitStr.join(" ");
};

export const priceToWordsVi = (num: number) => {
  const [intPart, decimalPart] = num.toString().split(".");
  let result = VNnum2words(parseInt(intPart));

  if (decimalPart) {
    const decimalWords = decimalPart
      .split("")
      .map((digit) => VNnum2words(parseInt(digit)))
      .join(" ");
    result += " phẩy " + decimalWords;
  }

  return titleCase(result);
};

export const priceToWordsEn = new ToWords({
  localeCode: "en-US",
  converterOptions: {
    currency: false,
    ignoreDecimal: false,
    ignoreZeroCurrency: false,
    doNotAddOnly: true,
  },
});
