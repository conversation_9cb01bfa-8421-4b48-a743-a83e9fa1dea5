import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { Button, Input, Popconfirm, Space, Table, message } from "antd";
import { debounce } from "lodash";
import { useCallback, useEffect, useRef, useState } from "react";

import { customerApi } from "@/api/customer.api";
import { Pagination } from "@/components/Pagination";
import { StaffItem } from "@/components/StaffItem";
import TableContent from "@/components/Table/TableContent";
import { MarketSelector } from "@/components/Selector/MarketSelector";
import { useCustomer } from "@/hooks/useCustomer";
import { Customer } from "@/types/customer";
import { getTitle } from "@/utils";
import { CustomerModal, CustomerModalRef } from "./components/CustomerModal";
import ImportCustomer from "./components/ImportCustomer";
import { userStore } from "@/store/userStore";
import { useParams, useSearchParams } from "react-router-dom";
import { observer } from "mobx-react";
import { PermissionName } from "@/router";

const { Column } = Table;

export const CustomerPage = observer(({ title = "" }: { title?: string }) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    fetchCustomer,
    customers,
    isFetchCustomer,
    queryCustomer,
    totalCustomer,
  } = useCustomer({
    initQuery: {
      page: 1,
      limit: 50,
      createdStaffId: !userStore.checkRoleByName(PermissionName.viewAllCustomer)
        ? userStore.info.id
        : undefined,
    },
  });

  const CustomerModalRef = useRef<CustomerModalRef>();
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC" | null>(null);
  const [selectedMarketId, setSelectedMarketId] = useState<number | undefined>(
    undefined
  );

  const fieldMap: Record<string, string> = {
    market: "market.name",
    code: "customer.code",
    name: "customer.name",
    endName: "customer.endName",
    codeContract: "customer.codeContract",
    contactName: "customer.contactName",
    contactEmail: "customer.contactEmail",
    note: "customer.note",
    nation: "nation.name",
    address: "customer.address",
    phone: "customer.phone",
    representBy: "customer.representBy",
    position: "customer.position",
    updatedStaff: "updatedStaff.name",
  };

  useEffect(() => {
    document.title = getTitle(title);
    fetchCustomer();
  }, []);

  const debounceSearch = useCallback(
    debounce((keyword: string) => {
      queryCustomer.page = 1;
      queryCustomer.search = keyword;
      fetchCustomer();
    }, 300),
    []
  );

  const handleMarketChange = (marketId: number | undefined) => {
    setSelectedMarketId(marketId);
    queryCustomer.page = 1;
    queryCustomer.marketId = marketId;
    fetchCustomer();
  };

  //check if auto open edit customer
  useEffect(() => {
    const codeEdit = searchParams.get("edit");
    if (customers.length > 0 && codeEdit) {
      const record = customers.find((customer) => customer.code === codeEdit);

      record && CustomerModalRef.current?.handleOpen("update", record);

      setSearchParams({});
    }
  }, [customers, searchParams]);

  const handleDelete = async (id: number) => {
    await customerApi.delete(id);
    message.success("Đã xóa");
    fetchCustomer();
  };

  const onTableChange = (page: number, filters: any, sorter: any) => {
    if (!Array.isArray(sorter)) {
      const columnKey = sorter.field || sorter.column?.key;

      if (!sorter.order) {
        setSortField(null);
        setSortOrder(null);
        queryCustomer.queryObject = "";
      } else {
        const order = sorter.order === "ascend" ? "ASC" : "DESC";
        const field = fieldMap[columnKey as string];

        if (field) {
          setSortField(field);
          setSortOrder(order);
          queryCustomer.queryObject = JSON.stringify([
            {
              type: "sort",
              field,
              value: order,
            },
          ]);
        }
      }
    }
    fetchCustomer();
  };

  return (
    <div className="shadow-sm p-4 bg-white rounded-sm">
      <div className="filter-container">
        <Space className="w-full items-end">
          <div className="filter-item">
            <p className="font-semibold mb-1">Tìm kiếm</p>
            <Input
              onChange={(e) => {
                queryCustomer.search = e.target.value;
                debounceSearch(e.target.value);
              }}
              size="middle"
              placeholder="Tìm kiếm tên khách hàng"
            />
          </div>
          <div className="filter-item">
            <p className="font-semibold mb-1">Thị trường</p>
            <MarketSelector
              value={selectedMarketId}
              onChange={handleMarketChange}
              placeholder="Chọn thị trường"
            />
          </div>
          {userStore.managerProductionRole.capNhatTatCaKhachHang && (
            <>
              <div className="filter-item">
                <Button
                  onClick={() => {
                    CustomerModalRef.current?.handleOpen("create");
                  }}
                  type="primary"
                  icon={<PlusOutlined />}
                >
                  Tạo mới
                </Button>
              </div>

              <div className="filter-item">
                <ImportCustomer onSuccess={fetchCustomer}></ImportCustomer>
              </div>
            </>
          )}
        </Space>
      </div>

      <Table
        size="small"
        className="table-striped-rows"
        loading={isFetchCustomer}
        pagination={false}
        rowKey="id"
        dataSource={customers}
        scroll={{ x: 1000 }}
        onChange={onTableChange as any}
      >
        <Column
          title="Mã khách hàng"
          dataIndex="code"
          key="code"
          width={100}
          render={(text, record: Customer) => (
            <TableContent
              text={record.code}
              onClick={() => {
                CustomerModalRef.current?.handleOpen("update", record);
              }}
            ></TableContent>
          )}
          sorter
          sortOrder={
            sortField === fieldMap["code"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />

        <Column
          title="Tên khách hàng"
          key="name"
          width={150}
          render={(text, record: Customer) => (
            <span>{record.name}</span>
          )}
          sorter
          sortOrder={
            sortField === fieldMap["name"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />

        <Column
          title="Tên khách hàng cuối cùng"
          width={150}
          render={(text, record: Customer) => (
            <span>{record.endName}</span>
          )}
          key="endName"
          sorter
          sortOrder={
            sortField === fieldMap["endName"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />

        <Column
          title="Ký tự mã hóa"
          width={100}
          render={(text, record: Customer) => (
            <span>{record.codeContract}</span>
          )}
          key="codeContract"
          sorter
          sortOrder={
            sortField === fieldMap["codeContract"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Tên liên hệ"
          render={(text, record: Customer) => (
            <span>{record.contactName}</span>
          )}
          key="contactName"
          sorter
          sortOrder={
            sortField === fieldMap["contactName"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Email liên hệ"
          render={(text, record: Customer) => (
            <span>{record.contactEmail}</span>
          )}
          key="contactEmail"
          sorter
          sortOrder={
            sortField === fieldMap["contactEmail"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Ghi chú"
          width={150}
          render={(text, record: Customer) => (
            <span className="font-semibold">{record.note}</span>
          )}
          key="note"
          sorter
          sortOrder={
            sortField === fieldMap["note"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Khu vực quốc gia"
          width={150}
          render={(text, record: Customer) => (
            <span>{record?.nation?.name}</span>
          )}
          key="nation"
          sorter
          sortOrder={
            sortField === fieldMap["nation"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Địa chỉ"
          render={(text, record: Customer) => (
            <span>{record?.address}</span>
          )}
          key="address"
          sorter
          sortOrder={
            sortField === fieldMap["address"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Số điện thoại"
          render={(text, record: Customer) => (
            <span>{record?.phone}</span>
          )}
          key="phone"
          sorter
          sortOrder={
            sortField === fieldMap["phone"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Đại diện bởi"
          width={100}
          render={(text, record: Customer) => (
            <span>{record?.representBy}</span>
          )}
          key="representBy"
          sorter
          sortOrder={
            sortField === fieldMap["representBy"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Chức vụ"
          width={100}
          render={(text, record: Customer) => (
            <span>{record?.position}</span>
          )}
          sorter
          key="position"
          sortOrder={
            sortField === fieldMap["position"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Thị trường"
          key="market"
          width={100}
          render={(text, record: Customer) => (
            <span>{record?.market?.name}</span>
          )}
          sorter
          sortOrder={
            sortField === "market.name"
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        <Column
          title="Người sửa gần nhất"
          key="updatedStaff"
          width={150}
          render={(_, record: Customer) =>
            record?.updatedStaff && (
              <span>{`${record?.updatedStaff?.name} - ${record?.updatedStaff?.code}`}</span>
            )
          }
          sorter
          sortOrder={
            sortField === fieldMap["updatedStaff"]
              ? sortOrder === "ASC"
                ? "ascend"
                : "descend"
              : undefined
          }
        />
        {/* <Column
          title="Format ngày tháng năm"
          render={(text, record: Customer) => (
            <span className="font-semibold">{record?.dateFormat}</span>
          )}
        /> */}

        <Column
          width={100}
          title="Thao tác"
          key="action"
          render={(text, record: Customer) => (
            <Space>
              <Button
                icon={<EyeOutlined />}
                onClick={() =>
                  CustomerModalRef.current?.handleOpen("update", record)
                }
              >
                Xem
              </Button>

              {!!userStore.checkRoleByName(PermissionName.xoaKhachHang) && (
                <Popconfirm
                  placement="topLeft"
                  title={`Xác nhận xóa khách hàng này?`}
                  okText="Ok"
                  cancelText="Không"
                  onConfirm={() => handleDelete(record.id)}
                >
                  <Button
                    ghost
                    icon={<DeleteOutlined />}
                    block
                    type="primary"
                    danger
                  >
                    Xóa
                  </Button>
                </Popconfirm>
              )}
            </Space>
          )}
        />
      </Table>

      <Pagination
        total={totalCustomer}
        currentPage={queryCustomer.page}
        onChange={({ page, limit }) => {
          queryCustomer.page = page;
          queryCustomer.limit = limit;
          fetchCustomer();
        }}
        defaultPageSize={queryCustomer.limit}
      />
      <CustomerModal ref={CustomerModalRef} onSubmitOk={fetchCustomer} />
    </div>
  );
});
