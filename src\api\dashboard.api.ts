import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const dashboardApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard",
      params,
    }),
  findTotalCustomer: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/customer",
      params,
    }),
  findTotalContract: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/contract",
      params,
    }),
  findTopSaleCustomer: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/sale/customer",
      params,
    }),
  findTopSaleProduct: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/sale/product",
      params,
    }),
  findTopSaleMarket: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/top/sale/market",
      params,
    }),
  findDateCustomer: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/date/customer",
      params,
    }),
  findDateContract: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/date/contract",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dashboard/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/dashboard/${id}`,
      method: "delete",
    }),
  findTotalAscContract: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/contract/asc",
      params,
    }),
  findSummaryAscCF: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/summary/contract/ascCF",
      params,
    }),
  findTotalAllContract: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/dashboard/total/contract/all",
      params,
    }),
};
