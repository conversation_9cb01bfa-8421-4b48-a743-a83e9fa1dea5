import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const areaApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/area",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/area",
      data,
      method: "post",
    }),
  getByName: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/area/name",
      data,
      method: "post",
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/area/import",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/area/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/area/${id}`,
      method: "delete",
    }),
};
