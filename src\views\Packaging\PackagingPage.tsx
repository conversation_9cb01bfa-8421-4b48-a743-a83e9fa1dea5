import { Market } from "@/types/market";
import { PlusOutlined, SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Input,
  message,
  Popconfirm,
  Select,
  Space,
  Spin,
  Table,
} from "antd";
import { useEffect, useState, useRef } from "react";
import { MarketModal, PackagingModal } from "./components/PackagingModal";
import { getTitle } from "@/utils";
import { Pagination } from "@/components/Pagination";
import DropdownCell from "@/components/Table/DropdownCell";
import { useCustomerProduct } from "@/hooks/useCustomerProduct";
import { useCustomer } from "@/hooks/useCustomer";

const { ColumnGroup, Column } = Table;

export const PackagingPage = ({ title = "" }) => {
  const {
    loading,
    customerProducts,
    totalCustomerProducts: total,
    fetchCustomerProducts,
    deleteCustomerProduct,
    setQuery,
    query,
  } = useCustomerProduct({ initQuery: { page: 1, limit: 20 } });

  const { fetchCustomer, customers } = useCustomer({
    initQuery: { page: 1, limit: 50 },
  });

  const modalRef = useRef<MarketModal>(null);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC" | null>(null);

  useEffect(() => {
    document.title = getTitle(title);
    fetchCustomerProducts();
    fetchCustomer();
  }, []);

  useEffect(() => {
    fetchCustomerProducts();
  }, [query]);

  const handleDelete = async (id: number) => {
    await deleteCustomerProduct(id);
    message.success("Đã xóa");
  };

  return (
    <div>
      <div className="filter-container">
        <Space>
          <div className="filter-item">
            <label htmlFor="">Tìm kiếm</label>
            <Input
              onKeyDown={(ev) => {
                if (ev.code == "Enter") {
                  query.page = 1;
                  setQuery({ ...query });
                }
              }}
              size="middle"
              onChange={(ev) => {
                query.search = ev.target.value;
              }}
              placeholder="Tìm kiếm"
            />
          </div>

          <div className="filter-item flex flex-col w-[200px]">
            <label htmlFor="">Khách hàng</label>
            <Select
              allowClear
              options={customers.map((customer) => ({
                value: customer.id,
                label: customer.name,
              }))}
              onChange={(value) => {
                setQuery({ ...query, page: 1, customerId: value });
              }}
            ></Select>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={fetchCustomerProducts}
              type="primary"
              icon={<SearchOutlined />}
            >
              Tìm kiếm
            </Button>
          </div>

          <div className="filter-item btn">
            <Button
              onClick={() => {
                modalRef.current?.handleCreate();
              }}
              type="primary"
              icon={<PlusOutlined />}
            >
              Thêm mới
            </Button>
          </div>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          pagination={false}
          rowKey="id"
          dataSource={customerProducts}
          scroll={{ x: "max-content" }}
          onChange={(pagination, filters, sorter) => {
            if (!Array.isArray(sorter)) {
              const fieldMap: Record<string, string> = {
                name: "market.name",
                code: "market.code",
              };

              const columnKey = sorter.field || sorter.column?.key;

              if (!sorter.order) {
                setSortField(null);
                setSortOrder(null);
                query.queryObject = "";
              } else {
                const order = sorter.order === "ascend" ? "ASC" : "DESC";
                const field = fieldMap[columnKey as string];

                if (field) {
                  setSortField(field);
                  setSortOrder(order);
                  query.queryObject = JSON.stringify([
                    {
                      type: "sort",
                      field,
                      value: order,
                    },
                  ]);
                }
              }
              setQuery({ ...query });
            }
          }}
        >
          <Column
            width={150}
            title="Mã"
            dataIndex="code"
            key="code"
            sorter
            sortOrder={
              sortField === "code"
                ? sortOrder === "ASC"
                  ? "ascend"
                  : "descend"
                : undefined
            }
          />
          <Column
            width={300}
            title="Tên"
            dataIndex="name"
            key="name"
            sorter
            sortOrder={
              sortField === "name"
                ? sortOrder === "ASC"
                  ? "ascend"
                  : "descend"
                : undefined
            }
          />
          <Column
            width={200}
            title="Ngày tạo"
            dataIndex="createdAt"
            key="createdAt"
            sorter
            render={(text) => new Date(text * 1000).toLocaleString()}
          />
          <Column
            width={200}
            title="Ngày cập nhật"
            dataIndex="updatedAt"
            key="updatedAt"
            sorter
            render={(text) => new Date(text * 1000).toLocaleString()}
          />
          <Column width={300} title="Ghi chú" dataIndex="note" key="note" />

          <Column
            width={100}
            title="Thao tác"
            align="right"
            key="action"
            render={(text, record: Market) => (
              <DropdownCell
                items={[
                  {
                    onClick: () => {
                      modalRef.current?.handleUpdate(record);
                    },
                    key: "update",
                    label: "Chỉnh sửa",
                  },
                  {
                    key: "remove",
                    label: (
                      <Popconfirm
                        placement="topLeft"
                        title={`Xác nhận xóa market này?`}
                        okText="Ok"
                        cancelText="Không"
                        onConfirm={() => handleDelete(record.id)}
                      >
                        Xóa
                      </Popconfirm>
                    ),
                  },
                ]}
              />
            )}
          />
        </Table>

        <Pagination
          currentPage={query.page}
          total={total}
          onChange={({ limit, page }) => {
            query.page = page;
            query.limit = limit;
            setQuery({ ...query });
          }}
        />
      </Spin>

      <PackagingModal
        onSubmitOk={fetchCustomerProducts}
        onClose={() => {}}
        ref={modalRef}
      />
    </div>
  );
};
