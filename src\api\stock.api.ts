import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const stockApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/stock",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/stock",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/stock/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/stock/${id}`,
      method: "delete",
    }),
};
