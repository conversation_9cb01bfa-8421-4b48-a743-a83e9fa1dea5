import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const defineCompetitorApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/defineCompetitor",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/defineCompetitor",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/defineCompetitor/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/defineCompetitor/${id}`,
      method: "delete",
    }),
};
