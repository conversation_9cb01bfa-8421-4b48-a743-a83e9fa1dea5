import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const competitorCheckApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/competitorCheck",
      params,
    }),
  detail: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/competitorCheck/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/competitorCheck",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/competitorCheck/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/competitorCheck/${id}`,
      method: "delete",
    }),
};
