import { AxiosPromise } from "axios";
import { request } from "../utils/request";
import { getDeviceInfo } from "@/utils/device";

export const authApi = {
  getOTP: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/otp",
      params,
    }),

  login: (data: any): AxiosPromise<any> => {
    const { deviceId, deviceName } = getDeviceInfo();

    return request({
      url: "/v1/admin/auth/login",
      data: { ...data, deviceId, deviceName },
      method: "post",
    });
  },
  token: (data: any): AxiosPromise<any> => {
    const { deviceId, deviceName } = getDeviceInfo();

    return request({
      url: "/v1/admin/auth/otp/token",
      data: { ...data, deviceId, deviceName },
      method: "post",
    });
  },

  passwordUpdate: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/password/update",
      data,
      method: "post",
    }),

  profile: (): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/profile",
    }),

  updateProfile: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/auth/profile",
      method: "patch",
      data,
    }),
};
