import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const staffApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff",
      data,
      method: "post",
    }),
  importNpp: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/staff/import/npp",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}`,
      method: "patch",
      data,
    }),
  requiredOTP: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/requireOTP`,
      method: "patch",
      data,
    }),
  block: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/block`,
      method: "patch",
    }),
  unblock: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/unblock`,
      method: "patch",
    }),
  resetPassword: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}/password/reset`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/staff/${id}`,
      method: "delete",
    }),
};
