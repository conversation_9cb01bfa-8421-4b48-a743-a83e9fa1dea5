import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const visitApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/visit",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/visit",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/visit/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/visit/${id}`,
      method: "delete",
    }),
};
