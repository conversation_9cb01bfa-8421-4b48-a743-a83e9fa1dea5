import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const customerProductApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerProduct",
      params,
    }),
  findOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerProduct/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/customerProduct",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerProduct/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/customerProduct/${id}`,
      method: "delete",
    }),
};
