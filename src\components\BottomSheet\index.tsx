import { Drawer } from "antd";
import { DrawerProps } from "antd/lib";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";

export interface BottomSheetProps extends Omit<DrawerProps, "children"> {
  children: React.ReactNode | Function;
  afterClose?: () => void;
  afterOpen?: (data: any) => void;
  showCloseBtn?: boolean;
}

export interface BottomSheetRef {
  open: (data: any) => void;
  close: () => void;
  refresh: () => void;
}

const BottomSheet = forwardRef(
  (
    {
      children,
      className,
      height,
      afterOpen,
      showCloseBtn = false,
      title,
      destroyOnClose = false,
      afterClose,
      ...props
    }: BottomSheetProps,
    ref
  ) => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [appState, setAppState] = useState(false);

    const pageYOffset = useRef(0);
    const data = useRef<any>(null);
    const handleOnOpen = (newData: any) => {
      data.current = newData;
      pageYOffset.current = window.pageYOffset;
      setIsOpen(true);
    };
    const handleOnClose = () => {
      data.current = null;
      setIsOpen(false);
    };

    useImperativeHandle(
      ref,
      () => ({
        open: handleOnOpen,
        close: handleOnClose,
        refresh: () => setAppState((state) => !state),
      }),
      []
    );

    useEffect(() => {
      return () => {
        afterClose?.();
      };
    }, []);

    const child =
      typeof children == "function" ? children(data.current) : children;

    return (
      <Drawer
        {...props}
        destroyOnClose={destroyOnClose}
        afterOpenChange={(open) => {
          if (!open) {
            afterClose?.();
          } else {
            afterOpen?.(data.current);
          }
        }}
        bodyStyle={{
          padding: 0,
        }}
        className={"pb-safe  rounded-tl-xl rounded-tr-xl " + className}
        title={title}
        height={height}
        closable={showCloseBtn}
        placement="bottom"
        onClose={handleOnClose}
        open={isOpen}
      >
        {child}
      </Drawer>
    );
  }
);

export default BottomSheet;
