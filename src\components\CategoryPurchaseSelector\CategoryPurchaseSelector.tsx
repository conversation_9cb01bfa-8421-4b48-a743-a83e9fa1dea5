import { useCategory } from "@/hooks/useCategory";
import { Category } from "@/types/category";
import { ProposalType } from "@/types/proposal";
import { PurchaseOrderType } from "@/types/purchaseOrder";
import { Select } from "antd";
import { useEffect, useState } from "react";

type CustomFormItemProps = {
  value?: number;
  type: PurchaseOrderType;
  mode?: "PURCHASE" | "PROPOSAL";
  onChange?: (id: number) => void;
  onChangeOption?: (option?: Category) => void;
};

export const CategoryPurchaseSelector: React.FC<CustomFormItemProps> = ({
  value,
  type,
  mode = "PROPOSAL",
  onChange,
  onChangeOption,
}) => {
  const [valueState, setvalueState] = useState(value);

  let typeChange =
    (type === PurchaseOrderType.PO && ProposalType.Material) ||
    (type === PurchaseOrderType.CheckPrice && ProposalType.Material) ||
    (type === PurchaseOrderType.Repair && ProposalType.Repair);

  const { fetchData, categories, query } = useCategory({
    initQuery: {
      page: 1,
      limit: 50,
      type: mode == "PROPOSAL" ? typeChange : ProposalType.PurchaseOrder,
    },
  });

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <>
      <Select
        className="custom-select"
        value={valueState}
        showSearch
        filterOption={(input, option) => {
          if (option?.label) {
            return (
              option.label
                .toString()
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
            );
          }
          return false;
        }}
        style={{ width: "100%", height: 28 }}
        options={categories.map((item) => ({
          label: item.name,
          value: item.id,
          option: item,
        }))}
        onChange={(value, option: any) => {
          //@ts-ignore
          onChange(value);
          setvalueState(value);
          //@ts-ignore
          onChangeOption(option?.option);
        }}
      ></Select>
    </>
  );
};
