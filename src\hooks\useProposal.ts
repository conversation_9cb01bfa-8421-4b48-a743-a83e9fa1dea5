import { proposalApi } from "@/api/proposal.api";
import { Proposal } from "@/types/proposal";
import { QueryParam } from "@/types/query";
import { useRef, useState } from "react";
import {
  getInitQueryForPackageModule,
  getInitQueryForProductionManage,
} from "./useProductionOrder";
import { userStore } from "@/store/userStore";

export interface ProposalQuery extends QueryParam { }

interface UseProposalProps {
  initQuery: ProposalQuery;
  api?: (data: any) => Promise<any>;
}

export const useProposal = ({ initQuery, api }: UseProposalProps) => {
  const [data, setData] = useState<Proposal[]>([]);
  const [total, setTotal] = useState(0);
  const query = useRef<ProposalQuery>(initQuery);
  const [loading, setLoading] = useState(false);


  const fetchData = async (selectedProposals?: Proposal[]) => {
    setLoading(true);
    try {
      const initQueryForBaoBi =
        query.current?.types?.includes?.("BAO_BI") &&
          query.current.isGetOwn == undefined
          ? getInitQueryForPackageModule()
          : {};

      const { data } = await (api?.({
        ...query.current,
        ...initQueryForBaoBi,
      }) ||
        proposalApi.findAll({
          ...query.current,
          ...initQueryForBaoBi,
        }));

      let newProposals: Proposal[] = data.proposals;

      if (selectedProposals?.filter((item) => item?.id)?.length) {
        selectedProposals
          .filter((item) => item?.id)
          .forEach((item) => {
            const find = newProposals.find((product) => product.id == item?.id);
            if (!find) {
              newProposals.push(item);
            }
          });
      }

      setData(newProposals);
      setTotal(data.total);
    } finally {
      setLoading(false);
    }
  };

  return {
    proposals: data,
    total,
    fetchData,
    loading,
    query: query.current,
    setProposals: setData,
  };
};
