import { request } from "@/utils/request";
import { AxiosPromise } from "axios";


export const unitApi = {
    findAll: (params?: any): AxiosPromise<any> => request({
        url: '/v1/admin/unit',
        params
    }),
    create: (data: any): AxiosPromise<any> => request({
        url: '/v1/admin/unit',
        data,
        method: 'post'
    }),
    update: (id: number, data: any): AxiosPromise<any> => request({
        url: `/v1/admin/unit/${id}`,
        method: 'patch',
        data
    }),
    delete: (id: number): AxiosPromise<any> => request({
        url: `/v1/admin/unit/${id}`,
        method: 'delete'
    }),
}
