import { categoryApi } from "@/api/category.api";
import { proposalApi } from "@/api/proposal.api";
import { purchaseApi } from "@/api/purchase.api";
import FilterTable from "@/components/Filter/FilterTable";
import { AppModalAction } from "@/components/Modal/AppModal";
import { Pagination } from "@/components/Pagination";
import TableContent from "@/components/Table/TableContent";
import { usePurchase } from "@/hooks/usePurchase";
import { useStaff } from "@/hooks/useStaff2";
import { userStore } from "@/store/userStore";
import {
  Proposal,
  ProposalStatus,
  ProposalType,
  ProposalTypeTrans,
} from "@/types/proposal";
import {
  PurchaseOrder,
  PurchaseOrderStatus,
  PurchaseOrderType,
  purchaseStatusObject,
} from "@/types/purchaseOrder";
import { QueryParams2 } from "@/types/query";
import { formatVND } from "@/utils";
import { checkRole } from "@/utils/checkRole";
import { unixToFullDate } from "@/utils/dateFormat";
import { DeleteOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import { Popconfirm, Space, Tag, Tooltip, message } from "antd";
import Table from "antd/es/table";
import Column from "antd/es/table/Column";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";

import { useProvider } from "@/hooks/useProvider";
import { useTermOfPayment } from "@/hooks/useTermOfPayment";
import { debounce } from "lodash";
import { Quote, QuoteStatus, QuoteStatusObject } from "@/types/quote";
import { useQuote } from "@/hooks/useQuote";
import { quoteApi } from "@/api/quote.api";

import ModalQuoteDetail, {
  ModalDetailQuoteRef,
} from "@/views/QuotePage/components/ModalQuoteDetail";
import {
  Contract,
  ContractStatus,
  ContractStatusObject,
} from "@/types/contract";
import { useContract } from "@/hooks/useContract";
import { contractStatusLabel } from "../ContractStatusBtn/ContractStatusBtn";
import { contractApi } from "@/api/contract.api";
import ModalCreateContract, {
  ModalCreateContractRef,
} from "../ModalCreateContract";
import ModalContractDetail, {
  ModalDetailContractRef,
} from "../ModalContractDetail";
import { PermissionName } from "@/router";

interface propTypes {
  status: ContractStatus | undefined;
  query?: QueryParams2;
  isActive?: boolean;
  onOk: () => void;
}

const ContractList = React.forwardRef(
  ({ status, query, isActive = false, onOk }: propTypes, ref) => {
    let [searchParams, setSearchParams] = useSearchParams();

    const [sortField, setSortField] = useState<string | null>(null);
    const [sortOrder, setSortOrder] = useState<"ASC" | "DESC" | null>(null);

    const tableRef = useRef<HTMLDivElement>(null);
    const reportModalPreview = useRef<AppModalAction>();

    const {
      fetchData: fetchProviders,
      query: providerQuery,
      providers,
    } = useProvider({
      initQuery: { page: 1, limit: 50 },
    });

    const contractModalRef = useRef<ModalCreateContractRef>();
    const contractModalDetailRef = useRef<ModalDetailContractRef>();
    const [title, setTitle] = useState("Danh sách tất cả hợp đồng");

    const {
      fetchData: fetchContract,
      contracts,
      query: queryContract,
      loading,
      total,
      setQuery,
    } = useContract({
      initQuery: {
        page: 1,
        limit: 50,
        status,
        createdStaffId: !userStore.checkRoleByName(
          PermissionName.viewAllContract
        )
          ? userStore.info.id
          : undefined,
        ...query,
      },
    });

    const handleDelete = async (id: number) => {
      const res = await contractApi.delete(id);
      fetchContract();
      message.success("Xóa thành công");
    };

    // const canDeletePurchase = useMemo(
    //   () =>
    //     userStore.info.role?.isAdmin ||
    //     checkRole("delete-purchase", userStore?.info?.role?.permissions),
    //   [userStore.info]
    // );

    const {
      fetchData: fetchStaff,
      staffs,
      query: queryStaff,
    } = useStaff({
      initQuery: {
        page: 1,
        limit: 20,
      },
    });

    useEffect(() => {
      fetchStaff();
      fetchProviders();
    }, []);

    // const showMoneyInfo = useMemo(() => {
    //   const isViewMoneyInfoRole = checkRole(
    //     "view-money-info",
    //     userStore.info.role?.permissions
    //   );
    //   return (
    //     userStore.info.role?.isAccounting ||
    //     userStore.info.role?.isAdmin ||
    //     isViewMoneyInfoRole
    //   );
    // }, [userStore.info.role]);

    // const showProviderInfo = useMemo(() => {
    //   const isViewProviderInfoRole = checkRole(
    //     "view-provider-info",
    //     userStore.info.role?.permissions
    //   );
    //   return (
    //     userStore.info.role?.isAccounting ||
    //     userStore.info.role?.isAdmin ||
    //     isViewProviderInfoRole
    //   );
    // }, [userStore.info.role]);

    const handleOpenModalDetail = (data: Contract) => {
      contractModalDetailRef?.current?.viewDetail(data);
      searchParams.set("mode", "update");
      searchParams.set("id", data.id + "");
      setSearchParams([...searchParams]);
    };

    const handleOk = () => {
      contractModalDetailRef.current?.refreshData();
      fetchContract();
      onOk();
    };

    const handleModalSuggestionDetailCancel = () => {
      searchParams.delete("mode");
      searchParams.delete("id");
      setSearchParams([...searchParams], { replace: true });
    };

    const getTitle = async (query: QueryParams2) => {
      //check query nếu là query.type thì show tên ra
      //Nếu là categoryId thì get cate về
      if (query.type) {
        return setTitle(ProposalTypeTrans?.[query.type as ProposalType]);
      }

      if (query.categoryId) {
        const { data } = await categoryApi.findOne(query.categoryId);
        return setTitle(ProposalTypeTrans?.[data.type as ProposalType]);
      }

      setTitle("Danh sách tất cả hợp đồng");
    };

    useEffect(() => {
      if (!isActive) return;
      if (query) {
        getTitle(query);
        // let finalQuery = { ...queryProposal, ...query };

        queryContract.paymentTermId =
          queryContract.paymentTermId || query.paymentTermId || undefined;
        queryContract.categoryId = query.categoryId || undefined;
        queryContract.type = query.type || undefined;
        queryContract.search = query.search || undefined;
        queryContract.createdStaffId = query.createdStaffId;
        queryContract.isNeedFollow = query.isNeedFollow;
        queryContract.isNeedInspec = query.isNeedInspec;
        queryContract.isOverdue = query.isOverdue;
        queryContract.staffId = query.staffId;
        queryContract.isHiddenComplete = query.isHiddenComplete;
        queryContract.type = query.type;
        queryContract.fromAt = query.fromAt;
        queryContract.toAt = query.toAt;
        queryContract.providerId =
          queryContract.providerId || query.providerId || undefined;
        queryContract.customerId = query.customerId;
        fetchContract();
        setQuery({ ...queryContract });
      }
    }, [query, isActive]);

    useEffect(() => {
      handleFillColor();
    }, [contracts, query]);

    useEffect(() => {
      //Chỉ trigger trên tab tất cả và khi tab TẤT CẢ active
      if (status == undefined && isActive) {
        handleCheckParam();
      }
    }, [searchParams.get("mode"), searchParams.get("type"), status]);

    const handleFillColor = () => {
      const rows = tableRef.current?.querySelectorAll(".ant-table-row-level-0");
      rows?.forEach((row: any) => {
        const className = row.className;
        if (className.includes("row-color")) {
          const classArr = className.split("row-color-");
          //@ts-ignore
          row.style.backgroundColor = classArr[classArr.length - 1];
        } else {
          //@ts-ignore
          row.style.backgroundColor = "unset";
        }
      });
    };

    const handleCheckParam = async () => {
      if (!searchParams.get("mode")) return;
      //Mode: Update
      //Type
      //id: Payment
      // ==> Chi tiết
      const mode = searchParams.get("mode");
      const id = searchParams.get("id");
      const proposalId = searchParams.get("proposalId");
      if (mode == "update" && id) {
        contractModalDetailRef.current?.viewDetail({
          id: +id,
        } as Contract);
      }
      //Mode: create
      //Type: Payment
      // ==> Tạo mới
      if (mode == "create") {
        let data;
        if (proposalId) {
          const res = await proposalApi.findOne(+proposalId);

          data = res.data;
        }

        contractModalRef.current?.createData(data);
      }
    };

    const onTableChange = (page: number, filters: any, sorter: any) => {
      //Tới dây
      const staff = filters?.staffName?.[0] || "";
      const createdAt = filters?.createdAt;
      const providerId = filters?.code?.[0];
      const paymentTermId = filters?.paymentTerm?.[0];
      //@ts-ignore
      const fromAt = createdAt?.[0];
      //@ts-ignore
      const toAt = createdAt?.[1];
      queryContract.createdStaffId = staff || undefined;
      queryContract.providerId = providerId || undefined;
      queryContract.paymentTermId = paymentTermId || undefined;
      queryContract.fromAt = fromAt;
      queryContract.toAt = toAt;
      queryContract.page = 1;

      if (!Array.isArray(sorter)) {
        const fieldMap: Record<string, string> = {
          code: "contract.code",
          name: "contract.name",
          description: "contract.description",
          createdAt: "contract.createdAt",
          po: "contract.po",
          status: "contract.status",
        };

        const columnKey = sorter.field || sorter.column?.key;

        if (!sorter.order) {
          setSortField(null);
          setSortOrder(null);
          queryContract.queryObject = "";
        } else {
          const order = sorter.order === "ascend" ? "ASC" : "DESC";
          const field = fieldMap[columnKey as string];

          if (field) {
            setSortField(field);
            setSortOrder(order);
            queryContract.queryObject = JSON.stringify([
              {
                type: "sort",
                field,
                value: order,
              },
            ]);
          }
        }
      }
      setQuery({ ...queryContract });
      fetchContract();
    };

    const getRowColor = (record: Contract) => {
      //@ts-ignore
      if (contractStatusLabel[record.status]?.rowColor) {
        //@ts-ignore
        return `row-color-${contractStatusLabel[record.status]?.rowColor}`;
      } else {
        return "";
      }
    };

    const debounceSearchProvider = debounce((search: string) => {
      providerQuery.search = search;
      fetchProviders();
    }, 200);

    return (
      <>
        <div className="header flex justify-between">
          <h1 className="text-base my-2">{title}</h1>
          <div className="count mt-2">Tổng dòng: {formatVND(total)}</div>
        </div>
        <div>
          <Table
            pagination={false}
            dataSource={contracts}
            className="table-striped-rows custom-table"
            loading={loading}
            tableLayout="fixed"
            //@ts-ignore
            onChange={onTableChange}
            scroll={{ y: 600, x: "max-content" }}
            rowClassName={(record, index) => getRowColor(record)}
            ref={tableRef as any}
          >
            <Column
              width={30}
              align="center"
              title=" "
              key="stt"
              render={(text, record: Proposal, index: number) => {
                return index + 1;
              }}
            ></Column>
            <Column
              width={120}
              title="Tên hợp đồng"
              key="name"
              render={(text, record: Contract) => {
                return (
                  <TableContent
                    text={record.name}
                    onClick={() => handleOpenModalDetail(record)}
                  />
                );
              }}
              sorter
              sortOrder={
                sortField === "contract.name"
                  ? sortOrder === "ASC"
                    ? "ascend"
                    : "descend"
                  : undefined
              }
            ></Column>
            <Column
              width={200}
              title="Số PO"
              key="po"
              render={(text, record: Contract) => {
                return <TableContent text={record.po} />;
              }}
              sorter
              sortOrder={
                sortField === "contract.po"
                  ? sortOrder === "ASC"
                    ? "ascend"
                    : "descend"
                  : undefined
              }
            ></Column>
            <Column
              width={200}
              title="Mô tả"
              key="description"
              render={(text, record: Contract) => {
                return <TableContent text={record.description} />;
              }}
              sorter
              sortOrder={
                sortField === "contract.description"
                  ? sortOrder === "ASC"
                    ? "ascend"
                    : "descend"
                  : undefined
              }
            ></Column>
            {/* {showProviderInfo && (
              <> */}

            {/* <Column
                  filterDropdown={(props) => (
                    <FilterTable
                      {...props}
                      onSearch={debounceSearchPaymentTerm}
                      options={paymentTerms}
                      fieldNames={{
                        label: "name",
                        value: "id",
                      }}
                      type="select"
                    />
                  )}
                  width={200}
                  title="Điều khoản TT"
                  key="paymentTerm"
                  render={(text, record: PurchaseOrder) => {
                    return <TableContent text={record.paymentTerm?.name} />;
                  }}
                ></Column> */}
            {/* </>
            )} */}

            {/* {showMoneyInfo && (
              <Column
                align="right"
                width={80}
                title="Tổng tiền"
                key="code"
                render={(text, record: PurchaseOrder) => {
                  return <TableContent text={formatVND(record?.amount)} />;
                }}
              ></Column>
            )} */}

            <Column
              width={120}
              title="Trạng thái"
              key="status"
              dataIndex="status"
              render={(text: ContractStatus, record: Contract) => (
                //@ts-ignore
                <Tag color={ContractStatusObject[record.status]?.bgColor}>
                  {/* @ts-ignore */}
                  {ContractStatusObject[record.status]?.title || record.status}
                </Tag>
              )}
              sorter
              sortOrder={
                sortField === "contract.status"
                  ? sortOrder === "ASC"
                    ? "ascend"
                    : "descend"
                  : undefined
              }
            />
            <Column
              width={150}
              title={"Ngày tạo"}
              key="createdAt"
              render={(text, record: Contract) => {
                return (
                  <TableContent text={unixToFullDate(record?.createdAt)} />
                );
              }}
              sorter
              sortOrder={
                sortField === "contract.createdAt"
                  ? sortOrder === "ASC"
                    ? "ascend"
                    : "descend"
                  : undefined
              }
            ></Column>
            <Column
              filterDropdown={(props) => (
                <FilterTable
                  onSearch={(v) => {
                    queryStaff.search = v;
                    fetchStaff();
                  }}
                  {...props}
                  options={staffs}
                  fieldNames={{ label: "name", value: "id" }}
                  type="select"
                />
              )}
              title="Tên người tạo"
              key="staffName"
              render={(text, record: Contract) => {
                return (
                  <TableContent
                    text={[
                      record?.createdStaff?.name,
                      record?.createdStaff?.code
                        ? `(${record?.createdStaff?.code})`
                        : undefined,
                    ]
                      .filter(Boolean)
                      .join(" - ")}
                  />
                );
              }}
            ></Column>
            <Column
              title=" "
              align="center"
              width={80}
              key="name"
              render={(text, record: any) => (
                <Space>
                  {(userStore.info.role?.isAdmin ||
                    record.createdStaff?.id === userStore.info.id) &&
                    record.status == ContractStatus.New &&
                    userStore.checkRoleByName(PermissionName.updateContract) ? (
                    <div className="flex gap-2 text-lg justify-center">
                      <Tooltip title="Chỉnh sửa hợp đồng" placement="bottom">
                        <div>
                          <EditOutlined
                            className="text-yellow-500 cursor-pointer outline-none"
                            onClick={() => handleOpenModalDetail(record)}
                          />
                        </div>
                      </Tooltip>
                    </div>
                  ) : (
                    <Tooltip title="Xem chi tiết">
                      <EyeOutlined
                        onClick={() => handleOpenModalDetail(record)}
                        className="text-lg text-blue-600 cursor-pointer outline-none"
                      />
                    </Tooltip>
                  )}
                  {
                    // canDeletePurchase ||
                    ((userStore.info.role?.isAdmin ||
                      record.createdStaff?.id === userStore.info.id) &&
                      userStore.checkRoleByName(PermissionName.deleteContract)) &&
                    record.status == ContractStatus.New && (
                      <div>
                        <Popconfirm
                          placement="topLeft"
                          title={`Xác nhận xóa hợp đồng này?`}
                          okText="Ok"
                          cancelText="Không"
                          onConfirm={() =>
                            handleDelete(
                              //@ts-ignore
                              record?.id || 0
                            )
                          }
                        >
                          <Tooltip title="Xóa hợp đồng" placement="bottom">
                            <DeleteOutlined className="text-red-500 cursor-pointer outline-none text-[18px]" />
                          </Tooltip>
                        </Popconfirm>
                      </div>
                    )
                  }
                </Space>
              )}
            ></Column>
          </Table>
          <Pagination
            total={total}
            onChange={({ page, limit }) => {
              queryContract.page = page;
              queryContract.limit = limit;
              setQuery({ ...queryContract });
              if (query) {
                query.page = page;
                query.limit = limit;
                onOk?.();
              }
              fetchContract();
            }}
            defaultPageSize={queryContract.limit}
            currentPage={queryContract.page}
          />

          <ModalCreateContract
            onOk={handleOk}
            ref={contractModalRef}
            fetchData={fetchContract}
            onCancel={() => ""}
          />

          <ModalContractDetail
            onEdit={(data) => {
              contractModalRef.current?.handleUpdate(data);
            }}
            fetchData={fetchContract}
            onOk={() => {
              fetchContract();
              onOk();
            }}
            onCancel={handleModalSuggestionDetailCancel}
            ref={contractModalDetailRef}
          />
        </div>
      </>
    );
  }
);

export default ContractList;
