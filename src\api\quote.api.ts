import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const quoteApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/quote",
      params,
    }),
  summaryProvider: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/quote/provider/summary",
      params,
    }),
  findOne: (id?: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}`,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/quote",
      data,
      method: "post",
    }),
  followStaff: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}/followStaff`,
      method: "patch",
      data,
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}`,
      method: "delete",
    }),
  deleteDetail: (quoteDetailId: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/detail/${quoteDetailId}`,
      method: "delete",
    }),
  summary: (params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/summary/status`,
      params,
    }),
  checkUpdate: (id: number, params: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}/checkUpdate`,
      params,
    }),
  complete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}/complete`,
      method: "patch",
    }),
  approve: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}/approve`,
      method: "patch",
    }),
  reject: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/quote/${id}/reject`,
      method: "delete",
    }),
};
