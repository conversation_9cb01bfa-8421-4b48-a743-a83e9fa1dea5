import { request } from "@/utils/request";

export const logisticApi = {
  getList: (params: any) => request.get('/v1/admin/logistic', { params }),
  getById: (id: number) => request.get(`/v1/admin/logistic/${id}`),
  create: (data: any) => request.post('/v1/admin/logistic', data),
  update: (id: number, data: any) => request.patch(`/v1/admin/logistic/${id}`, data),
  delete: (id: number) => request.delete(`/v1/admin/logistic/${id}`),
  
  // Các phương thức cập nhật trạng thái
  processing: (id: number) => request.patch(`/v1/admin/logistic/${id}/processing`),
  complete: (id: number) => request.patch(`/v1/admin/logistic/${id}/complete`),
  cancel: (id: number) => request.patch(`/v1/admin/logistic/${id}/cancel`),
};
