import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const areaDetailApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/areaDetail",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/areaDetail",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/areaDetail/${id}`,
      method: "patch",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/areaDetail/import`,
      method: "post",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/areaDetail/${id}`,
      method: "delete",
    }),
};
