import { AxiosPromise } from "axios";
import { request } from "../utils/request";
import { settings } from "../../settings";
import { generateHash } from "@/utils/auth";

interface CreateTelegramLogParams {
  message: string;
  password?: string;
}
export const TelegramLogApi = {
  create: ({ message }: CreateTelegramLogParams): any => {
    const { hash, time } = generateHash();
    const password = hash;
    if (!settings.isDev) {
      return request({
        url: "/v1/public/telegram/log",
        data: {
          message,
          password,
        },
        method: "post",
      });
    }
  },
  createPayroll: ({ message }: CreateTelegramLogParams): any => {
    const { hash, time } = generateHash();
    const password = hash;
    return request({
      url: "/v1/public/telegram/log/payroll",
      data: {
        message,
        password,
      },
      method: "post",
    });
  },
};
