import { request } from "@/utils/request";
import { AxiosPromise } from "axios";

export const paymentTermApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentTerm",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/paymentTerm",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentTerm/${id}`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/paymentTerm/${id}`,
      method: "delete",
    }),
};
