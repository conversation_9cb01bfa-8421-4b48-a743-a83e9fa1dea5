import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const employeeApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/employee",
      params,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/employee",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}`,
      method: "patch",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/import`,
      method: "post",
      data,
    }),
  importThaiSan: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/import/thaisan`,
      method: "post",
      data,
    }),
  importBHXH: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/import/bhxh`,
      method: "post",
      data,
    }),
  resetPass: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}/reset/password`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/employee/${id}`,
      method: "delete",
    }),
};
