import { Customer, Market } from "@/types/customer";
import { InspecHistory, Proposal } from "@/types/proposal";
import { Staff } from "@/types/staff";
import { ContractConfigType } from "./contractContent";
import { Module, Product } from "./product";
import { PaymentTerm } from "./paymentTerm";
export enum ContractDetailETDType {
  Early = "EARLY",
  Mid = "MID",
  End = "END",
}
export enum ContractStatus {
  New = "NEW", //mới
  Processing = "PROCESSING", //đang duyệt
  Approved = "APPROVED",
  Complete = "COMPLETE", //đã đóng
  Reject = "REJECT", //đã từ chối
}

export enum ContType {
  FT20 = "20FT",
  FT40 = "40FT",
  ContRep = "CONT_REP",
}

export const ContractStatusTrans = {
  [ContractStatus.New]: "Mới",
  [ContractStatus.Processing]: "Đang duyệt",
  [ContractStatus.Reject]: "Đã từ chối",
  [ContractStatus.Complete]: "Đã đóng",
  [ContractStatus.Approved]: "Đã duyệt",
};

export const ContractStatusObject = {
  [ContractStatus.New]: {
    title: "Mới",
    color: "white",
    value: ContractStatus.New,
    bgColor: "rgba(0, 0, 0, 0.88)",
  },

  [ContractStatus.Processing]: {
    title: "Đang duyệt",
    color: "white",
    value: ContractStatus.Processing,
    bgColor: "#2db7f5",
  },
  [ContractStatus.Complete]: {
    title: "Đã đóng",
    color: "white",
    value: ContractStatus.Complete,
    bgColor: "#87d068",
  },
  [ContractStatus.Approved]: {
    title: "Đã duyệt",
    color: "white",
    value: ContractStatus.Approved,
    bgColor: "#87d068",
  },
  [ContractStatus.Reject]: {
    title: "Đã từ chối",
    color: "white",
    value: ContractStatus.Reject,
    bgColor: "#cd201f",
  },
};

export const ContractDetailETDTypeTrans = {
  [ContractDetailETDType.Early]: {
    title: "Early",
    color: "white",
    value: ContractDetailETDType.Early,
  },

  [ContractDetailETDType.Mid]: {
    title: "Mid",
    color: "white",
    value: ContractDetailETDType.Mid,
  },
  [ContractDetailETDType.End]: {
    title: "End",
    color: "white",
    value: ContractDetailETDType.End,
  },
};

export interface ContractDetail {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  poCode: string;
  productName: string;
  ascCF: boolean;
  ascNCF: boolean;
  quantity: number;
  unitPrice: number; // đơn giá cho 1 mặt hàng USD/Lb
  amount: number; // tông tiền cuối cùng
  etdMonth: string; // yyyy-mm-dd
  etdType: ContractDetailETDType;
  specification: string; // Đặc điểm kỹ thuật
  temperature?: number; // Nhiệt độ
  contract: Contract;
  unit: Unit;
  unitId: number;
  code: string;
  contractPackingDetails: ContractPackingDetail[];
  packing?: number | string;
  thermometer?: number | string;
  specificationEn?: string;
  numberBox?: number;
  contType?: string;
  quantityUnit?: number;
  quantityFinal?: number; // Số lượng cuối cùng
  contractProduct?: ContractProduct;
}

export interface ContractPackingDetail {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  nameVi: string;
  nameEn: string;
  productCode: string;
  quantityOnUnit: number; //(đề nghị bao bì)
  contractQuantity: number; //sl trên hợp đồng
  note: string; // ghi trú
  contractProduct: ContractProduct;
  isMain: boolean;
  productName: string; // tên sản phẩm
  length?: number;
  width?: number;
  height?: number;
  quantity?: number; // số lượng bao bì
  quantityUnit?: string; // đơn vị bao bì
}

export interface ContractProduct {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string; // mã quy trình
  productName: string;
  ascCF: boolean;
  ascNCF: boolean; //
  quantity: number; // số lượng
  unitPrice: number; // đơn giá cho 1 mặt hàng USD/Lb
  amount: number; // tông tiền cuối cùng
  specification: string; // Đặc điểm kỹ thuật
  specificationEn: string; // Đặc điểm kỹ thuật
  quantityUnit: number; // đơn vị trên thùng
  quantityFinal: number; // số lượng cuối
  packing: string;
  thermometer: number; // nhiệt kế kho lạnh
  contract: Contract;
  unit: Unit;
  unitId?: number;
  proposal: Proposal;
  contractProductIndex: number;
  poCode: string;
  address?: string;
  qualityTerms?: string;
  totalFCL: number;
  contType?: string;
  paymentTerm?: string;
  po?: string;
  etdDetail?: string;
  poDetails?: PODetail[];
  contractProduct?: ContractProduct;
  contractPos?: ContractPO[];
  etdDetailEn?: string;
}

export interface ContractContent {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  type: ContractConfigType;
  titleVi: string;
  titleEn: string;
  contentVi: string;
  contentEn: string;
  contract: Contract;
}

export interface PODetail {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  quantity: number;
  price: number; // đơn giá
  amount: number; // tổng tiền
  contractPo: ContractPO;
  contractProduct: ContractProduct;
  contractProductIndex: number;
}

export interface ContractPO {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  po: string;
  contType: ContType;
  totalFCL: number;
  quantity: number; // số lượng = totalPO * totalFCL
  etdDetail: string; // Thời gian giao hàng cho (Thông báo sản xuất)
  address: string; // địa điểm giao hàng
  amount: number; // tông tiền
  contract: Contract;
  poDetails: PODetail[];
  totalPrice?: number;
  productName?: string;
  unitPrice?: number;
  etdDetailEn?: string;
}

export interface Contract {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  code: string;
  name: string;
  po: string;
  description: string;
  signAt: number; // thời gian ký hợp đồng
  paymentTerm: string;
  status: ContractStatus;
  type: ContractType;
  totalQuantity: number;
  totalPrice: number;
  isAllInspec: boolean;
  completedAt: number;
  procedureCode: string; // mã quy trình
  productBy: string;
  contactPersonName: string;
  total: number;
  temperature: number; // nhiệt độ kho lạnh
  contractDetails: ContractDetail[];
  customer: Customer;
  market: Market;
  contractContents: ContractContent[];
  contractPos: ContractPO[];
  contractProducts: ContractProduct[];
  createdStaff: Staff; //người tạo
  followStaffs: Staff[];
  inspecStaffs: Staff[];
  inspecHistories: InspecHistory[];
  proposal: Proposal;
  unit: Unit;
  totalFCL?: string;
  termsOfDelivery?: string;
  requiredDocuments?: string;
}

export interface Unit {
  id: number;
  deletedBy: string;
  createdAt: number;
  updatedAt: number;
  deletedAt: number;
  isDeleted: boolean;
  name: string;
  nameEn: string;
  module: Module;
  createdStaff: Staff;
}

export const ContractUnits = [
  { id: 1, value: "lb", name: "LB", nameEn: "LB" },
  { id: 2, value: "kg", name: "Kg", nameEn: "Kg" },
  { id: 3, value: "tray", name: "Khay", nameEn: "Tray" },
  { id: 4, value: "carton", name: "Thùng", nameEn: "Cartons" },
  { id: 5, value: "box", name: "Hộp", nameEn: "Box" },
  { id: 6, value: "ring", name: "Ring", nameEn: "Ring" },
  { id: 7, value: "piece", name: "Piece", nameEn: "Piece" },
  { id: 8, value: "bag", name: "Túi", nameEn: "Bag" },
] as const;

export enum ContractType {
  General = "GENERAL",
  USTax = "US_TAX", // Hợp đồng US đã bao gồm thuế
  USNoTax = "US_NO_TAX", // Hợp đồng US chưa bao gồm thuế
  Inbound = "INBOUND", //trong nước
  Mixed = "MIXED", //ghép
}

export const ContractTypeTrans = {
  [ContractType.General]: "Hợp đồng chung",
  [ContractType.USTax]: "HĐ Mỹ có thuế",
  [ContractType.USNoTax]: "HĐ Mỹ không thuế",
  [ContractType.Inbound]: "HĐ trong nước",
  [ContractType.Mixed]: "HĐ xuất ghép",
};
