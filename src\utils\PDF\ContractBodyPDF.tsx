import { PurchaseOrder } from "@/types/purchaseOrder";
import moment from "moment";
import {
  formatUSD,
  formatVND,
  formatVND2FDs,
  priceToWordsEn,
  priceToWordsVi,
} from "..";
import { getLargeDate } from "../date";
import { CTY_KE_TOAN_MAIL } from "./ReportOrderComponent";
import { stringToDate, unixToDate } from "../dateFormat";
import {
  Contract,
  ContractDetail,
  ContractDetailETDType,
  ContractPO,
  ContractProduct,
  ContractType,
  ContractUnits,
} from "@/types/contract";
import dayjs from "dayjs";
import { ContractConfigType, ContractContent } from "@/types/contractContent";
import { Checkbox } from "antd";
import { ToWords } from "to-words";
import React, { useEffect, useMemo, useState } from "react";
import { Unit } from "@/types/unit";
import { useContractContent } from "@/hooks/useContractContent";
import { contractContentApi } from "@/api/contractContent.api";

type UnitItem = {
  nameVi: string;
  nameEn: string;
  quantityOnUnit: number;
  contractQuantity: number;
};

export const Highlight = ({
  isBold = false,
  children,
}: {
  isBold?: boolean;
  children: React.ReactNode;
}) => (
  <span className={["text-red-500", isBold ? "font-bold" : ""].join(" ")}>
    {children}
  </span>
);

export const PO_DETAIL_COLUMN_SIZE = 90;
export const RULE_CLASS_NAME = "mt-0";

export const ContractBodyPDF = ({
  data,
  printType,
  unitType = "unit", // thêm prop unitType
  typeContract,
  contents = [],
}: {
  data: Contract;
  printType: "vi" | "en" | "all";
  unitType?: "box" | "unit";
  typeContract?: string;
  contents?: ContractContent[];
}) => {
  function getUnitName(item: UnitItem, printType: "vi" | "en" | "all") {
    if (printType === "vi") return item.nameVi;
    if (printType === "en") return item.nameEn;
    return `${item.nameVi} (${item.nameEn})`;
  }

  function renderPackingDescription(
    data: UnitItem[],
    printType: "vi" | "en" | "all" = "all"
  ) {
    // Sắp xếp từ cấp lớn đến nhỏ (theo contractQuantity, quantityOnUnit nếu có)
    const levels = [...data].sort((a, b) => {
      // Nếu có contractQuantity thì ưu tiên sắp xếp
      if (a.contractQuantity && !b.contractQuantity) return -1;
      if (!a.contractQuantity && b.contractQuantity) return 1;
      return 0;
    });

    const parts: string[] = [];

    for (let i = 0; i < levels.length; i++) {
      const current = levels[i];
      const parent = levels[i - 1];

      if (i === 0) {
        parts.push(
          `${current.contractQuantity} ${getUnitName(current, printType)}`
        );
      } else if (current.quantityOnUnit) {
        parts.push(
          `${current.quantityOnUnit} ${getUnitName(
            current,
            printType
          )}/${getUnitName(parent, printType)}`
        );
      }
    }

    // Tính tổng đơn vị nhỏ nhất
    let totalUnits = levels[0].contractQuantity;
    for (let i = 1; i < levels.length; i++) {
      totalUnits *= levels[i].quantityOnUnit || 1;
    }

    return `${parts.join(" × ")}`;
  }

  const [reversedContents, setReversedContents] = useState<ContractContent[]>(
    []
  );

  useEffect(() => {
    const fetchData = async () => {
      setReversedContents([]);
      let contractContents = [...contents];

      if (
        !contractContents.find((item: any) => item.titleEn === "Payment terms")
      ) {
        contractContents.splice(contractContents.length - 1, 0, {
          contentEn:
            `${data.paymentTerm ?? ""}<br/>` +
            (contractContents.find((it) => it.type === "PAYMENT")?.contentEn ??
              ""),
          contentVi:
            `${data.paymentTerm ?? ""}<br/>` +
            (contractContents.find((it) => it.type === "PAYMENT")?.contentVi ??
              ""),
          titleEn: "Payment terms",
          titleVi: "Điều kiện thanh toán",
          type: "PAYMENT",
        } as ContractContent);
      }

      const index = contractContents.findIndex(
        (item: any) => item.type === "REQUIRED_DOCUMENTS"
      );

      if (index !== -1) {
        contractContents[index].contentEn = data?.requiredDocuments ?? "";
        contractContents[index].contentVi = data?.requiredDocuments ?? "";
      }

      const sortOrder = [
        "QUALITY",
        "PAYMENT",
        "CUSTOM",
        "REQUIRED_DOCUMENTS",
        "PACKAGING_DESIGNS",
        "WAIVER_OF_RESPONSIBILITY",
        "GUARANTEE_OF_COMPENSATION",
        "FORCE_MAJEURE_CLAUSE",
        "TRADE_TERMS",
        "ARBITRATION",
        "GENERAL_CONDITIONS",
      ];

      let paymentFound = false;
      const filteredData = contractContents.filter((item) => {
        if (item.type === "PAYMENT" && !paymentFound) {
          paymentFound = true;
          return false;
        }
        return true;
      });

      const sortedItems = filteredData.sort((a, b) => {
        const getIndex = (type: string) =>
          sortOrder.indexOf(type) === -1
            ? sortOrder.length
            : sortOrder.indexOf(type);

        return getIndex(a.type) - getIndex(b.type);
      });

      setReversedContents(sortedItems);
    };

    fetchData();
  }, [typeContract]);

  function checkAndReplaceContent(content: any) {
    const textContent = content?.replace(/<[^>]*>/g, "").trim();

    const startsWithList = /^(\d+\.|[a-zA-Z]\.|[-+])/.test(textContent);

    if (!startsWithList) {
      const result = content?.replace(
        /<p class="ql-align-justify">(.*?)<\/p>/g,
        "<label>$1</label>"
      );
      return result;
    }

    return content;
  }

  const productsGroupByName = useMemo(() => {
    const result: Record<
      string,
      { contractDetails: ContractDetail[]; contractProducts: ContractProduct[] }
    > = {};

    data.contractDetails?.forEach((item) => {
      const name = item.productName.toLowerCase();
      if (!result[name]) {
        result[name] = {
          contractDetails: [],
          contractProducts: [],
        };
      }
      item.unit = ContractUnits.find(
        (unit) => unit.id === (item?.unit?.id ?? 1)
      ) as any;

      result[name].contractDetails.push(item);
      result[name].contractProducts.push(
        data.contractProducts?.find((it) => it.poCode === item.poCode)!
      );
    });

    return result;
  }, [data]);

  const productsGroupPO = useMemo(() => {
    const result: { po: ContractProduct; details: ContractDetail[] }[] = [];

    data.contractDetails?.forEach((item) => {
      const po = data.contractProducts?.find(
        (it) => it.poCode === item.poCode
      )!;
      const poItem = result.find((it) => it.po?.poCode === po?.poCode);

      item.unit = ContractUnits.find(
        (unit) => unit.id === (item?.unit?.id ?? 1)
      ) as any;

      if (poItem) {
        poItem.details.push(item);
      } else {
        result.push({
          po,
          details: [item],
        });
      }
    });

    return result;
  }, [data]);

  const unitNameEn = (data: Contract) => {
    const nameEn = data?.contractDetails?.[0]?.unit.nameEn;

    if (nameEn && nameEn !== "") return nameEn;
    return data?.contractDetails?.[0]?.unit.name;
  };

  // Hàm lấy tên đơn vị hiển thị
  const getDisplayUnit = () => {
    if (unitType === "box") {
      return printType === "en" ? "Cartons" : "Thùng";
    }
    if (printType === "en")
      return data?.contractDetails?.[0]?.unit?.nameEn || "Unit";
    return data?.contractDetails?.[0]?.unit?.name || "Đơn vị tính";
  };

  const getTotalQuantityDisplay = (data: Contract, en?: boolean) => {
    let totalFlc = 0;

    data.contractProducts.forEach((p) => {
      totalFlc += (p.quantity ?? 0) / (p.quantityUnit ?? 1);
    });

    if (unitType === "box") {
      let total = 0;
      data?.contractProducts?.forEach((product) => {
        total += (product.quantity ?? 0) / (product.quantityUnit || 1);
      });

      const flc = data.contractPos?.[0]?.totalFCL;
      return `${formatVND(Math.ceil(total))} ${
        printType === "en" ? "Cartons" : "Thùng"
      } (=${formatVND(Math.ceil(totalFlc ?? 0))} x ${flc} FCL)`;
    }

    const quantityByUnit: Record<string, number> =
      data?.contractProducts.reduce((acc, item) => {
        const unitName =
          ContractUnits.find((unit) => unit.id === item.unit?.id)?.nameEn ?? "";
        acc[unitName] = (acc[unitName] || 0) + item.quantity;
        return acc;
      }, {} as Record<string, number>);

    const result: string = Object.entries(quantityByUnit)
      .map(([unit, qty]) => `${formatVND(Math.ceil(qty))} ${unit}`)
      .join(", ");

    const flc = data.contractPos?.[0]?.totalFCL;

    return `${result} (=${formatVND(Math.ceil(totalFlc ?? 0))} x ${flc} FCL)`;
  };

  const totalQuantityPO = (data: Contract, poCode: string, en?: boolean) => {
    const total = data?.contractProducts
      ?.filter((el) => el.poCode === poCode)
      ?.reduce((sum, item) => sum + item.quantity, 0);
    const totalFlc = data?.contractProducts
      ?.filter((el) => el.poCode === poCode)
      ?.reduce((sum, item) => sum + item.totalFCL, 0);

    if (en) {
      return `${total} (=${totalFlc} x 40’ FCL)`;
    }
    return `${total} (= ${totalFlc} x 40’ FCL)`;
  };

  const checkMultipleProduct = (contractPos: ContractPO[]): boolean => {
    for (const po of contractPos) {
      const ids = new Set<number>();
      for (const detail of po.poDetails) {
        const contractId = detail.contractProduct?.id;
        if (contractId !== undefined) {
          ids.add(contractId);
        }
      }
      if (ids.size > 1) {
        return true;
      }
    }
    return false;
  };

  const getUnitNameProduct = (productId: number) => {
    const product = data.contractProducts?.find((pd) => pd.id === productId);
    if (product) {
      const unit = ContractUnits.find((unit) => unit.id === product.unit?.id);
      return unit?.nameEn;
    }

    return "";
  };

  return (
    <div className="po-inland-body text-[13px]">
      <div className="mt-3 text-[14px]">
        <p className="font-semibold text-blue-700 underline mb-2">
          1. {printType == "vi" && "Hàng hóa - Đóng gói - Số lượng - Đơn giá"}
          {printType == "en" && "Commodity - Packing - Quantity - Unit price"}
          {printType == "all" &&
            "Hàng hóa - Đóng gói - Số lượng - Đơn giá (Commodity - Packing - Quantity - Unit price)"}
          :
        </p>
        {data.contractProducts.map((product) => (
          <>
            <table className="mt-2 w-full border border-black border-solid border-collapse table-fixed">
              <tbody>
                <tr className="border border-black border-solid">
                  <td className="border border-black border-solid align-center w-[20%] font-semibold p-1">
                    {printType == "vi" && "Tên mặt hàng"}
                    {printType == "en" && "Item name"}
                    {printType === "all" && "Tên mặt hàng (Item name)"}
                  </td>
                  <td className="border border-black border-solid p-1 font-bold uppercase ">
                    {product.productName}
                  </td>
                </tr>
                <tr className="border border-black border-solid">
                  <td className="border border-black border-solid align-center font-semibold p-1">
                    {printType == "vi" && "Đặc điểm kỹ thuật"}
                    {printType == "en" && "Specification"}
                    {printType === "all" && "Đặc điểm kỹ thuật (Specification)"}
                  </td>
                  <td className="border border-black border-solid p-1 whitespace-pre-line">
                    {printType == "vi"
                      ? product.specification
                      : product.specificationEn}
                  </td>
                </tr>
                {/* <tr className="border border-black border-solid">
                  <td className="border border-black border-solid align-center font-semibold p-1">
                    {printType == "vi" && "Nhiệt kế cont lạnh (cái)"}
                    {printType == "en" && "Cold container thermometer (pcs)"}
                    {printType === "all" &&
                      "Nhiệt kế cont lạnh (cái) (Cold container thermometer (pcs))"}
                  </td>
                  <td className="border border-black border-solid p-1">
                    {it.contractDetails[0]?.thermometer
                      ? `${it.contractDetails[0]?.thermometer}`
                      : "0"}
                  </td>
                </tr> */}
                <tr className="border border-black border-solid">
                  <td className="border border-black border-solid align-center font-semibold p-1">
                    {printType == "vi" && "Đóng gói"}
                    {printType == "en" && "Packing"}
                    {printType === "all" && "Đóng gói (Packing)"}
                  </td>
                  <td className="border border-black border-solid p-1">
                    {product.packing}
                  </td>
                </tr>
                {/* <tr className="border border-black border-solid">
                  <td className="border border-black border-solid align-center font-semibold p-1">
                    {printType == "vi" && ("Số lượng (Xấp xỉ. " + it.contractDetails?.[0]?.unit?.name) + ")"}
                    {printType == "en" && ("Quantity (Abt. " + unitNameEn(data)) + ")"}
                    {printType === "all" && ("Số lượng (Quantity) (Abt. " + unitNameEn(data)) + ")"}
                  </td>
                  <td className="border border-black border-solid p-1">
                    {totalQuantityPO(data, it.contractDetails?.[0]?.poCode, printType == "en")}
                  </td>
                </tr> */}
              </tbody>
            </table>
          </>
        ))}
      </div>
      <div className="flex text-[12px] mt-4">
        <div className=" overflow-auto">
          {!checkMultipleProduct(data.contractPos) ? (
            <>
              <table className="w-full border-collapse text-sm" border={1}>
                <thead>
                  <tr>
                    <th className="font-bold px-2 py-1 text-center">PO#</th>
                    {/* <th className="font-bold px-2 py-1 text-center">
                  {printType == "vi" && "Tên mặt hàng"}
                  {printType == "en" && "Item name"}
                  {printType === "all" && "Tên mặt hàng (Item name)"}
                </th> */}

                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Số lượng"}
                      {printType == "en" && "Quantity"}
                      {printType === "all" && "Số lượng (Quantity)"}(
                      {productsGroupPO[0]?.details?.[0]?.unit?.name})
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Đơn giá"}
                      {printType == "en" && "Unit Price"}
                      {printType === "all" && "Đơn giá (Unit Price)"} (
                      {productsGroupPO[0]?.details?.[0]?.unit?.name}/USD)
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Tổng tiền"}
                      {printType == "en" && "Total amount"}
                      {printType === "all" && "Tổng tiền (Total amount)"} (USD)
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Thời gian giao hàng"}
                      {printType == "en" && "Delivery time"}
                      {printType === "all" &&
                        "Thời gian giao hàng (Delivery time)"}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {data.contractPos?.map((po, index) => (
                    <tr>
                      <td
                        className="px-2 py-1 text-center"
                        align="center"
                        rowSpan={1}
                      >
                        {po.po}
                      </td>

                      <td
                        className="px-2 py-1 text-center"
                        align="center"
                        rowSpan={1}
                      >
                        {formatVND(
                          po.poDetails?.reduce(
                            (a, b) => a + (b?.quantity ?? 0),
                            0
                          )
                        )}
                      </td>

                      <td
                        className="px-2 py-1 text-center"
                        align="center"
                        rowSpan={1}
                      >
                        {formatVND(
                          po.poDetails?.reduce(
                            (a, b) => a + (b?.contractProduct?.unitPrice ?? 0),
                            0
                          )
                        )}
                      </td>

                      <td
                        className="px-2 py-1 text-center"
                        align="center"
                        rowSpan={1}
                      >
                        {formatVND2FDs(
                          po.poDetails?.reduce(
                            (a, b) => a + (b?.amount ?? 0),
                            0
                          )
                        )}
                      </td>
                      <td
                        className="px-2 py-1 text-center"
                        align="center"
                        rowSpan={1}
                      >
                        {po.etdDetail}
                      </td>
                    </tr>
                  ))}
                  {/* {productsGroupPO?.map(({ po, details }) => (
                <>
                  {details.map((it, index) => (
                    <tr key={index}>
                      {index === 0 && (
                        <td
                          className="px-2 py-1 text-center"
                          align="center"
                          rowSpan={details.length}
                        >
                          {it.poCode}
                        </td>
                      )}
                      <td className="px-2 py-1 text-center">
                        {it.productName}
                      </td>

                      <td className="px-2 py-1 text-center">
                        {printType == "vi" && it.quantity}
                        {printType == "en" && it.quantity}
                        {printType === "all" && it.quantity}
                      </td>
                      <td className="px-2 py-1 text-center">
                        {printType == "vi" && formatVND2FDs(it.unitPrice)}
                        {printType == "en" && formatUSD(it.unitPrice)}
                        {printType === "all" && formatUSD(it.unitPrice)}
                      </td>
                      <td className="px-2 py-1 text-center">
                        {printType == "vi" && formatVND2FDs(it.amount)}
                        {printType == "en" && formatUSD(it.amount)}
                        {printType === "all" && formatUSD(it.amount)}
                      </td>
                      {index === 0 && (
                        <td
                          align="center"
                          className="px-2 py-1 text-center"
                          rowSpan={details.length + 1}
                        >
                          {printType === "vi" && `${it.etdMonth}`}{" "}
                          {printType == "en" && `${it.etdMonth}`}{" "}
                          {printType == "all" &&
                            ` ${it.etdMonth} 
                      `}
                        </td>
                      )}
                    </tr>
                  ))}
                  <tr>
                    <td className="text-center font-bold" colSpan={4}>
                      {printType == "vi" && `Tổng tiền PO ${po?.poCode} (USD)`}
                      {printType == "en" &&
                        `Total amount PO ${po?.poCode} (USD)`}
                      {printType == "all" && `Tổng tiền PO ${po?.poCode} (USD)`}
                    </td>
                    <td className="text-center font-bold">
                      {printType == "vi" && formatVND2FDs(po?.amount)}
                      {printType == "en" && formatUSD(po?.amount)}
                      {printType === "all" && formatUSD(po?.amount)}
                    </td>
                  </tr>
                </>
              ))} */}
                  {data.contractDetails?.length > 0 && (
                    <>
                      <tr>
                        <td
                          className="align-top w-[140px]"
                          colSpan={4}
                          align="center"
                        >
                          <span>
                            {printType === "vi" && "Tổng giá trị"}{" "}
                            {printType == "en" && "Total value"}{" "}
                            {printType == "all" && "Tổng giá trị (Total value)"}
                          </span>
                        </td>
                        <td className="align-top" colSpan={1} align="center">
                          <span className="font-semibold">
                            {printType === "vi" &&
                              formatVND2FDs(data.totalPrice)}
                            {printType == "en" && formatUSD(data.totalPrice)}
                            {printType == "all" && formatUSD(data.totalPrice)}
                          </span>
                        </td>
                      </tr>
                      <tr>
                        <td
                          className="align-center w-[140px]"
                          colSpan={4}
                          align="center"
                        >
                          <p>
                            {printType === "vi" && `Bằng chữ`}
                            {printType == "en" && `By words`}
                            {printType == "all" && `Bằng chữ (By words)`}
                          </p>
                        </td>
                        <td className="align-top" colSpan={2}>
                          <span className="font-semibold">
                            {printType === "vi" &&
                              priceToWordsVi(data.totalPrice)}
                            {printType == "en" &&
                              priceToWordsEn.convert(data.totalPrice)}
                            {printType == "all" &&
                              priceToWordsVi(data.totalPrice) +
                                ` (${priceToWordsEn.convert(data.totalPrice)})`}
                          </span>
                        </td>
                      </tr>
                    </>
                  )}
                </tbody>
              </table>
              <div className="mb-2 whitespace-pre-line leading-5 ml-[60px]">
                {printType === "vi" && (
                  <div className="flex">
                    <div className="w-[120px]">
                      <div>- Tổng số lượng</div>
                      <div>- Tổng giá trị</div>
                      <div>- Điều kiện giao hàng</div>
                    </div>
                    <div>
                      <div>
                        {/* : <strong>Xấp xỉ. {data?.contractProducts?.reduce((sum, item) => sum + item.totalFCL, 0)}  {data?.contractDetails?.[0]?.unit.name} (=)</strong>{" "} */}
                        :{" "}
                        <strong>Xấp xỉ. {getTotalQuantityDisplay(data)}</strong>{" "}
                        (+/- 10% chấp nhận được)
                      </div>
                      <div>
                        :{" "}
                        <strong>
                          Xấp xỉ.{" "}
                          {(data.totalPrice ?? 0)
                            ?.toLocaleString("en-US", {
                              style: "currency",
                              currency: "USD",
                            })
                            ?.replace(/^\$/, "") || "0 USD"}{" "}
                          {" USD"}
                        </strong>
                      </div>
                      <div>: {data.termsOfDelivery}</div>
                    </div>
                  </div>
                )}{" "}
                {printType === "en" && (
                  <div className="flex">
                    <div className="w-[120px]">
                      <div>- Total quantity</div>
                      <div>- Total value</div>
                      <div>- Term of delivery</div>
                    </div>
                    <div>
                      <div>
                        :{" "}
                        <strong>
                          Abt. {getTotalQuantityDisplay(data, true)}
                        </strong>{" "}
                        (+/- 10% is acceptable)
                      </div>
                      <div>
                        :{" "}
                        <strong>
                          Abt.{" "}
                          {(data.totalPrice ?? 0)
                            ?.toLocaleString("en-US", {
                              style: "currency",
                              currency: "USD",
                            })
                            ?.replace(/^\$/, "") || "0 USD"}{" "}
                          {" USD"}
                        </strong>
                      </div>
                      <div>: {data.termsOfDelivery}</div>
                    </div>
                  </div>
                )}
              </div>
            </>
          ) : (
            <>
              <table className="w-full border-collapse text-sm" border={1}>
                <thead>
                  <tr>
                    <th className="font-bold px-2 py-1 text-center">PO#</th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Tên mặt hàng"}
                      {printType == "en" && "Product Name"}
                      {printType === "all" && "Tên mặt hàng (Product Name)"}
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Đơn vị tính"}
                      {printType == "en" && "Unit"}
                      {printType === "all" && "Đơn vị tính (Unit)"}
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Số lượng"}
                      {printType == "en" && "Quantity"}
                      {printType === "all" && "Số lượng (Quantity)"}
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Đơn giá"}
                      {printType == "en" && "Unit Price"}
                      {printType === "all" && "Đơn giá (Unit Price)"}
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Tổng tiền"}
                      {printType == "en" && "Total amount"}
                      {printType === "all" && "Tổng tiền (Total amount)"}
                    </th>
                    <th className="font-bold px-2 py-1 text-center">
                      {printType == "vi" && "Thời gian giao hàng"}
                      {printType == "en" && "Delivery time"}
                      {printType === "all" &&
                        "Thời gian giao hàng (Delivery time)"}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {data.contractPos?.map((po) => {
                    return po.poDetails.map((detail, detailIndex) => {
                      console.log("detail", po);

                      return (
                        <tr key={`${po.po}-${detailIndex}`}>
                          {detailIndex === 0 && (
                            <td
                              className="px-2 py-1 text-center"
                              align="center"
                              rowSpan={po.poDetails.length}
                            >
                              {po.po}
                            </td>
                          )}
                          <td className="px-2 py-1 text-left">
                            {detail.contractProduct?.productName}
                          </td>
                          <td className="px-2 py-1 text-center">
                            {getUnitNameProduct(detail.contractProduct?.id)}
                          </td>
                          <td className="px-2 py-1 text-center">
                            {formatVND(detail.quantity)}
                          </td>
                          <td className="px-2 py-1 text-center">
                            {formatVND(detail.contractProduct?.unitPrice)}
                          </td>
                          <td className="px-2 py-1 text-center">
                            {formatVND2FDs(detail.amount)}
                          </td>
                          <td className="px-2 py-1 text-center">
                            {po.etdDetail}
                          </td>
                        </tr>
                      );
                    });
                  })}
                </tbody>
              </table>
              <div className="mb-2 whitespace-pre-line leading-5 ml-[60px]">
                {printType === "vi" && (
                  <div className="flex">
                    <div className="w-[120px]">
                      <div>- Tổng số lượng</div>
                      <div>- Tổng giá trị</div>
                      <div>- Điều kiện giao hàng</div>
                    </div>
                    <div>
                      <div>
                        {/* : <strong>Xấp xỉ. {data?.contractProducts?.reduce((sum, item) => sum + item.totalFCL, 0)}  {data?.contractDetails?.[0]?.unit.name} (=)</strong>{" "} */}
                        :{" "}
                        <strong>Xấp xỉ. {getTotalQuantityDisplay(data)}</strong>{" "}
                        (+/- 10% chấp nhận được)
                      </div>
                      <div>
                        :{" "}
                        <strong>
                          Xấp xỉ.{" "}
                          {(data.totalPrice ?? 0)
                            ?.toLocaleString("en-US", {
                              style: "currency",
                              currency: "USD",
                            })
                            ?.replace(/^\$/, "") || "0 USD"}{" "}
                          {" USD"}
                        </strong>
                      </div>
                      <div>: {data.termsOfDelivery}</div>
                    </div>
                  </div>
                )}{" "}
                {printType === "en" && (
                  <div className="flex">
                    <div className="w-[120px]">
                      <div>- Total quantity</div>
                      <div>- Total value</div>
                      <div>- Term of delivery</div>
                    </div>
                    <div>
                      <div>
                        :{" "}
                        <strong>
                          Abt. {getTotalQuantityDisplay(data, true)}
                        </strong>{" "}
                        (+/- 10% is acceptable)
                      </div>
                      <div>
                        :{" "}
                        <strong>
                          Abt.{" "}
                          {(data.totalPrice ?? 0)
                            ?.toLocaleString("en-US", {
                              style: "currency",
                              currency: "USD",
                            })
                            ?.replace(/^\$/, "") || "0 USD"}{" "}
                          {" USD"}
                        </strong>
                      </div>
                      <div>: {data.termsOfDelivery}</div>
                    </div>
                  </div>
                )}
              </div>
            </>
          )}

          <div className="text-sm leading-relaxed mt-4 space-y-3">
            {reversedContents.map((section, index) => {
              const contentVi = checkAndReplaceContent(section.contentVi);
              const contentEn = checkAndReplaceContent(section.contentEn);
              return (
                <div key={section.id}>
                  <div className="">
                    <label>
                      <label className="text-blue-700 font-semibold">
                        {index + 2}.{" "}
                      </label>
                      <label className="text-blue-700 font-semibold underline">
                        {printType === "vi"
                          ? section.titleEn
                          : printType === "en"
                          ? section.titleEn
                          : `${section.titleVi} (${section.titleEn})}`}
                      </label>
                      <label className="text-blue-700 font-semibold pr-1">
                        :
                      </label>

                      {section?.type === "REQUIRED_DOCUMENTS" ? (
                        contentEn
                          ?.split("\n")
                          .map((line: any, index: number) => (
                            <React.Fragment key={index}>
                              {line}
                              <br />
                            </React.Fragment>
                          ))
                      ) : printType === "all" ? (
                        <>
                          <label
                            dangerouslySetInnerHTML={{
                              __html: contentEn,
                            }}
                          />
                          <label
                            className="mt-2"
                            dangerouslySetInnerHTML={{
                              __html: contentEn,
                            }}
                          />
                        </>
                      ) : (
                        <label
                          dangerouslySetInnerHTML={{
                            __html: contentEn,
                          }}
                        />
                      )}
                    </label>

                    {/* <p className="text-blue-700 font-semibold underline">
                      {index + 2}. {printType === "vi" && section.titleVi}
                      {printType === "en" && section.titleEn}
                      {printType === "all" &&
                        `${section.titleVi} (${section.titleEn})`}
                      :
                    </p> */}

                    {/* {section.type !== ContractConfigType.GeneralConditions &&
                      section.type !==
                      ContractConfigType.GuaranteeOfCompensation &&
                      section.type !== ContractConfigType.PackagingDesigns && (
                        <>
                          {printType === "all" ? (
                            <>
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: contentEn,
                                }}
                              />
                              <span
                                className="mt-2"
                                dangerouslySetInnerHTML={{
                                  __html: section.contentEn,
                                }}
                              />
                            </>
                          ) : (
                            <span
                              dangerouslySetInnerHTML={{
                                __html:
                                  printType === "vi"
                                    ? section.contentVi
                                    : section.contentEn,
                              }}
                            />
                          )}
                        </>
                      )} */}
                  </div>

                  {/* <div className="!pl-4 ql-editor !p-0">
                    {(section.type == ContractConfigType.GeneralConditions ||
                      section.type ==
                      ContractConfigType.GuaranteeOfCompensation ||
                      section.type == ContractConfigType.PackagingDesigns) && (
                        <>
                          {printType === "all" ? (
                            <>
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: section.contentVi,
                                }}
                              />
                              <div
                                className="mt-2"
                                dangerouslySetInnerHTML={{
                                  __html: section.contentEn,
                                }}
                              />
                            </>
                          ) : (
                            <div
                              dangerouslySetInnerHTML={{
                                __html:
                                  printType === "vi"
                                    ? section.contentVi
                                    : section.contentEn,
                              }}
                            />
                          )}
                        </>
                      )}
                  </div> */}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
