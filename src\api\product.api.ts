import { AxiosPromise } from "axios";
import { request } from "../utils/request";

export const productApi = {
  findAll: (params?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      params,
    }),
  getNextCode: (code: string): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product/nextCode",
      params: { code },
    }),
  codes: (data?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product/codes",
      method: "post",
      data,
    }),
  warehouse: (data?: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product/warehouse",
      method: "post",
      data,
    }),
  create: (data: any): AxiosPromise<any> =>
    request({
      url: "/v1/admin/product",
      data,
      method: "post",
    }),
  update: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "patch",
      data,
    }),
  import: (data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/import`,
      method: "post",
      data,
    }),
  block: (id: number, data: any): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}/block`,
      method: "patch",
      data,
    }),
  delete: (id: number): AxiosPromise<any> =>
    request({
      url: `/v1/admin/product/${id}`,
      method: "delete",
    }),
};
